import Foundation
import SwiftUI
import Combine
import CommonCrypto
import UserNotifications

// MARK: - Update Models

struct AppUpdate: Codable, Identifiable {
    let id: UUID
    let version: String
    let buildNumber: String
    let releaseDate: Date
    let downloadURL: URL
    let releaseNotes: String
    let isSecurityUpdate: Bool
    let isCritical: Bool
    let minimumSystemVersion: String
    let fileSize: Int64
    let checksum: String
    let signature: String?
    
    init(version: String, buildNumber: String, releaseDate: Date, downloadURL: URL, releaseNotes: String, isSecurityUpdate: Bool, isCritical: Bool, minimumSystemVersion: String, fileSize: Int64, checksum: String, signature: String? = nil) {
        self.id = UUID()
        self.version = version
        self.buildNumber = buildNumber
        self.releaseDate = releaseDate
        self.downloadURL = downloadURL
        self.releaseNotes = releaseNotes
        self.isSecurityUpdate = isSecurityUpdate
        self.isCritical = isCritical
        self.minimumSystemVersion = minimumSystemVersion
        self.fileSize = fileSize
        self.checksum = checksum
        self.signature = signature
    }
    
    enum CodingKeys: String, CodingKey {
        case version, buildNumber, releaseDate, downloadURL, releaseNotes
        case isSecurityUpdate, isCritical, minimumSystemVersion, fileSize
        case checksum, signature
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = UUID()
        self.version = try container.decode(String.self, forKey: .version)
        self.buildNumber = try container.decode(String.self, forKey: .buildNumber)
        self.releaseDate = try container.decode(Date.self, forKey: .releaseDate)
        self.downloadURL = try container.decode(URL.self, forKey: .downloadURL)
        self.releaseNotes = try container.decode(String.self, forKey: .releaseNotes)
        self.isSecurityUpdate = try container.decode(Bool.self, forKey: .isSecurityUpdate)
        self.isCritical = try container.decode(Bool.self, forKey: .isCritical)
        self.minimumSystemVersion = try container.decode(String.self, forKey: .minimumSystemVersion)
        self.fileSize = try container.decode(Int64.self, forKey: .fileSize)
        self.checksum = try container.decode(String.self, forKey: .checksum)
        self.signature = try container.decodeIfPresent(String.self, forKey: .signature)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(version, forKey: .version)
        try container.encode(buildNumber, forKey: .buildNumber)
        try container.encode(releaseDate, forKey: .releaseDate)
        try container.encode(downloadURL, forKey: .downloadURL)
        try container.encode(releaseNotes, forKey: .releaseNotes)
        try container.encode(isSecurityUpdate, forKey: .isSecurityUpdate)
        try container.encode(isCritical, forKey: .isCritical)
        try container.encode(minimumSystemVersion, forKey: .minimumSystemVersion)
        try container.encode(fileSize, forKey: .fileSize)
        try container.encode(checksum, forKey: .checksum)
        try container.encodeIfPresent(signature, forKey: .signature)
    }
    
    func isNewerThan(_ currentVersion: String) -> Bool {
        return version.compare(currentVersion, options: .numeric) == .orderedDescending
    }
    
    var formattedFileSize: String {
        ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)
    }
    
    var formattedReleaseDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: releaseDate)
    }
}

struct UpdateCheckResponse: Codable {
    let hasUpdate: Bool
    let latestVersion: AppUpdate?
    let currentVersion: String
    let checkDate: Date
    let nextCheckDate: Date
}

enum UpdateStatus: Equatable {
    case checking
    case available(AppUpdate)
    case noUpdate
    case downloading(progress: Double)
    case readyToInstall(AppUpdate)
    case installing
    case error(UpdateError)
    case disabled
    
    static func == (lhs: UpdateStatus, rhs: UpdateStatus) -> Bool {
        switch (lhs, rhs) {
        case (.checking, .checking),
             (.noUpdate, .noUpdate),
             (.installing, .installing),
             (.disabled, .disabled):
            return true
        case (.available(let lhsUpdate), .available(let rhsUpdate)):
            return lhsUpdate.version == rhsUpdate.version
        case (.downloading(let lhsProgress), .downloading(let rhsProgress)):
            return lhsProgress == rhsProgress
        case (.readyToInstall(let lhsUpdate), .readyToInstall(let rhsUpdate)):
            return lhsUpdate.version == rhsUpdate.version
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

enum UpdateChannel: String, CaseIterable, Codable {
    case stable = "stable"
    case beta = "beta"
    case alpha = "alpha"
    
    var displayName: String {
        switch self {
        case .stable: return "Stable"
        case .beta: return "Beta"
        case .alpha: return "Alpha"
        }
    }
    
    var description: String {
        switch self {
        case .stable: return "Stable releases with full testing"
        case .beta: return "Pre-release versions with new features"
        case .alpha: return "Early development builds (may be unstable)"
        }
    }
}

struct UpdateSettings: Codable {
    var automaticChecks: Bool = true
    var automaticDownload: Bool = false
    var automaticInstall: Bool = false
    var checkInterval: TimeInterval = 86400 // 24 hours
    var channel: UpdateChannel = .stable
    var includePrerelease: Bool = false
    var notifyOnUpdate: Bool = true
    var downloadInBackground: Bool = true
}

// MARK: - Update Errors

enum UpdateError: LocalizedError {
    case networkError(Error)
    case invalidResponse
    case downloadFailed(Error)
    case verificationFailed
    case installationFailed(Error)
    case insufficientPermissions
    case diskSpaceInsufficient
    case unsupportedSystem
    case updateServerUnavailable
    case checksumMismatch
    case signatureInvalid
    
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .invalidResponse:
            return "Invalid response from update server"
        case .downloadFailed(let error):
            return "Download failed: \(error.localizedDescription)"
        case .verificationFailed:
            return "Update verification failed"
        case .installationFailed(let error):
            return "Installation failed: \(error.localizedDescription)"
        case .insufficientPermissions:
            return "Insufficient permissions to install update"
        case .diskSpaceInsufficient:
            return "Insufficient disk space for update"
        case .unsupportedSystem:
            return "This update is not compatible with your system"
        case .updateServerUnavailable:
            return "Update server is currently unavailable"
        case .checksumMismatch:
            return "Update file integrity check failed"
        case .signatureInvalid:
            return "Update signature verification failed"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .networkError:
            return "Check your internet connection and try again"
        case .downloadFailed:
            return "Try downloading the update again"
        case .insufficientPermissions:
            return "Run the application as administrator"
        case .diskSpaceInsufficient:
            return "Free up disk space and try again"
        case .unsupportedSystem:
            return "Update your operating system"
        case .updateServerUnavailable:
            return "Try again later"
        default:
            return "Contact support if the problem persists"
        }
    }
}

// MARK: - Update Manager

@MainActor
class UpdateManager: ObservableObject {
    static let shared = UpdateManager()
    
    @Published var status: UpdateStatus = .disabled
    @Published var settings: UpdateSettings {
        didSet {
            saveSettings()
            scheduleNextCheck()
        }
    }
    @Published var lastCheckDate: Date?
    @Published var downloadProgress: Double = 0.0
    
    private let updateServerURL = "https://api.wowmonitor.com/updates"
    private let currentVersion: String
    private let currentBuildNumber: String
    
    private var checkTimer: Timer?
    private var downloadTask: URLSessionDownloadTask?
    private var cancellables = Set<AnyCancellable>()
    
    private let userDefaults = UserDefaults.standard
    private let settingsKey = "UpdateSettings"
    private let lastCheckKey = "LastUpdateCheck"
    
    private init() {
        // Get current version from bundle
        self.currentVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
        self.currentBuildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        
        // Load settings
        self.settings = Self.loadSettings()
        self.lastCheckDate = userDefaults.object(forKey: lastCheckKey) as? Date
        
        setupUpdateScheduler()
    }
    
    // MARK: - Settings Management
    
    private static func loadSettings() -> UpdateSettings {
        let userDefaults = UserDefaults.standard
        guard let data = userDefaults.data(forKey: "UpdateSettings"),
              let settings = try? JSONDecoder().decode(UpdateSettings.self, from: data) else {
            return UpdateSettings()
        }
        return settings
    }
    
    private func saveSettings() {
        do {
            let data = try JSONEncoder().encode(settings)
            userDefaults.set(data, forKey: settingsKey)
        } catch {
            print("Failed to save update settings: \(error)")
        }
    }
    
    // MARK: - Update Checking
    
    func checkForUpdates(manual: Bool = false) async {
        guard settings.automaticChecks || manual else { return }
        
        status = .checking
        
        do {
            let update = try await performUpdateCheck()
            
            if let update = update, update.isNewerThan(currentVersion) {
                status = .available(update)
                
                if settings.notifyOnUpdate {
                    await sendUpdateNotification(update)
                }
                
                if settings.automaticDownload {
                    await downloadUpdate(update)
                }
            } else {
                status = .noUpdate
            }
            
            lastCheckDate = Date()
            userDefaults.set(lastCheckDate, forKey: lastCheckKey)
            
        } catch {
            status = .error(.networkError(error))
        }
    }
    
    private func performUpdateCheck() async throws -> AppUpdate? {
        var urlComponents = URLComponents(string: updateServerURL)!
        urlComponents.queryItems = [
            URLQueryItem(name: "current_version", value: currentVersion),
            URLQueryItem(name: "build_number", value: currentBuildNumber),
            URLQueryItem(name: "channel", value: settings.channel.rawValue),
            URLQueryItem(name: "platform", value: "macos"),
            URLQueryItem(name: "arch", value: ProcessInfo.processInfo.machineHardwareName)
        ]
        
        guard let url = urlComponents.url else {
            throw UpdateError.invalidResponse
        }
        
        var request = URLRequest(url: url)
        request.setValue("WOW Monitor/\(currentVersion)", forHTTPHeaderField: "User-Agent")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw UpdateError.updateServerUnavailable
        }
        
        let updateResponse = try JSONDecoder().decode(UpdateCheckResponse.self, from: data)
        return updateResponse.latestVersion
    }
    
    // MARK: - Update Download
    
    func downloadUpdate(_ update: AppUpdate) async {
        status = .downloading(progress: 0.0)
        downloadProgress = 0.0
        
        do {
            let downloadedURL = try await performDownload(update)
            
            // Verify download
            try await verifyDownload(downloadedURL, expectedChecksum: update.checksum)
            
            status = .readyToInstall(update)
            
            if settings.automaticInstall {
                await installUpdate(update, from: downloadedURL)
            }
            
        } catch {
            status = .error(.downloadFailed(error))
        }
    }
    
    private func performDownload(_ update: AppUpdate) async throws -> URL {
        return try await withCheckedThrowingContinuation { continuation in
            downloadTask = URLSession.shared.downloadTask(with: update.downloadURL) { [weak self] url, response, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let url = url else {
                    continuation.resume(throwing: UpdateError.downloadFailed(NSError(domain: "UpdateManager", code: -1)))
                    return
                }
                
                // Move to permanent location
                let documentsPath = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask).first!
                let destinationURL = documentsPath.appendingPathComponent("WOWUpdate_\(update.version).dmg")
                
                do {
                    if FileManager.default.fileExists(atPath: destinationURL.path) {
                        try FileManager.default.removeItem(at: destinationURL)
                    }
                    try FileManager.default.moveItem(at: url, to: destinationURL)
                    continuation.resume(returning: destinationURL)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
            
            // Observe download progress
            downloadTask?.progress.publisher(for: \.fractionCompleted)
                .receive(on: DispatchQueue.main)
                .sink { [weak self] progress in
                    self?.downloadProgress = progress
                    self?.status = .downloading(progress: progress)
                }
                .store(in: &cancellables)
            
            downloadTask?.resume()
        }
    }
    
    private func verifyDownload(_ url: URL, expectedChecksum: String) async throws {
        let data = try Data(contentsOf: url)
        let actualChecksum = data.sha256
        
        guard actualChecksum == expectedChecksum else {
            throw UpdateError.checksumMismatch
        }
    }
    
    // MARK: - Update Installation
    
    func installUpdate(_ update: AppUpdate, from url: URL) async {
        status = .installing
        
        do {
            try await performInstallation(update, from: url)
            
            // Schedule app restart
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                self.restartApplication()
            }
            
        } catch {
            status = .error(.installationFailed(error))
        }
    }
    
    private func performInstallation(_ update: AppUpdate, from url: URL) async throws {
        // Check system compatibility
        guard isSystemCompatible(with: update) else {
            throw UpdateError.unsupportedSystem
        }
        
        // Check disk space
        guard hasSufficientDiskSpace(for: update) else {
            throw UpdateError.diskSpaceInsufficient
        }
        
        // Mount DMG and copy application
        try await mountAndInstall(from: url)
    }
    
    private func mountAndInstall(from url: URL) async throws {
        // This is a simplified implementation
        // In practice, you'd use proper DMG mounting and installation procedures
        
        let process = Process()
        process.executableURL = URL(fileURLWithPath: "/usr/bin/hdiutil")
        process.arguments = ["attach", url.path, "-nobrowse", "-quiet"]
        
        try process.run()
        process.waitUntilExit()
        
        guard process.terminationStatus == 0 else {
            throw UpdateError.installationFailed(NSError(domain: "UpdateManager", code: Int(process.terminationStatus)))
        }
        
        // Additional installation steps would go here
    }
    
    private func isSystemCompatible(with update: AppUpdate) -> Bool {
        let systemVersion = ProcessInfo.processInfo.operatingSystemVersion
        let systemVersionString = "\(systemVersion.majorVersion).\(systemVersion.minorVersion).\(systemVersion.patchVersion)"
        
        return systemVersionString.compare(update.minimumSystemVersion, options: .numeric) != .orderedAscending
    }
    
    private func hasSufficientDiskSpace(for update: AppUpdate) -> Bool {
        guard let attributes = try? FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory()),
              let freeSpace = attributes[.systemFreeSize] as? Int64 else {
            return false
        }
        
        // Require 2x the update size for safety
        return freeSpace > (update.fileSize * 2)
    }
    
    private func restartApplication() {
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/bin/sh")
        task.arguments = ["-c", "sleep 2; open '\(Bundle.main.bundlePath)'"]
        
        do {
            try task.run()
            NSApplication.shared.terminate(nil)
        } catch {
            print("Failed to restart application: \(error)")
        }
    }
    
    // MARK: - Scheduling
    
    private func setupUpdateScheduler() {
        scheduleNextCheck()
        
        // Check on app launch if it's been a while
        if let lastCheck = lastCheckDate,
           Date().timeIntervalSince(lastCheck) > settings.checkInterval {
            Task {
                await checkForUpdates()
            }
        }
    }
    
    private func scheduleNextCheck() {
        checkTimer?.invalidate()
        
        guard settings.automaticChecks else { return }
        
        checkTimer = Timer.scheduledTimer(withTimeInterval: settings.checkInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.checkForUpdates()
            }
        }
    }
    
    // MARK: - Notifications
    
    private func sendUpdateNotification(_ update: AppUpdate) async {
        let content = UNMutableNotificationContent()
        content.title = "Update Available"
        content.body = "WOW Monitor \(update.version) is now available"
        content.sound = .default
        content.categoryIdentifier = "UPDATE_AVAILABLE"
        content.userInfo = [
            "version": update.version,
            "isSecurityUpdate": update.isSecurityUpdate,
            "isCritical": update.isCritical
        ]
        
        let request = UNNotificationRequest(
            identifier: "update_\(update.version)",
            content: content,
            trigger: nil as UNNotificationTrigger?
        )
        
        try? await UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - Public Interface
    
    func cancelDownload() {
        downloadTask?.cancel()
        downloadTask = nil
        status = .noUpdate
    }
    
    func skipVersion(_ version: String) {
        userDefaults.set(version, forKey: "SkippedVersion")
    }
    
    func isVersionSkipped(_ version: String) -> Bool {
        return userDefaults.string(forKey: "SkippedVersion") == version
    }
    
    func resetSkippedVersions() {
        userDefaults.removeObject(forKey: "SkippedVersion")
    }
    
    func enableUpdates() {
        settings.automaticChecks = true
        status = .noUpdate
        scheduleNextCheck()
    }
    
    func disableUpdates() {
        settings.automaticChecks = false
        status = .disabled
        checkTimer?.invalidate()
    }
}

// MARK: - Extensions

extension Data {
    var sha256: String {
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        self.withUnsafeBytes {
            _ = CC_SHA256($0.baseAddress, CC_LONG(self.count), &digest)
        }
        return digest.map { String(format: "%02hhx", $0) }.joined()
    }
}

extension ProcessInfo {
    var machineHardwareName: String {
        var size = 0
        sysctlbyname("hw.machine", nil, &size, nil, 0)
        var machine = [CChar](repeating: 0, count: size)
        sysctlbyname("hw.machine", &machine, &size, nil, 0)
        return String(cString: machine)
    }
}

// MARK: - Update View

struct UpdateView: View {
    @StateObject private var updateManager = UpdateManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Image(systemName: "arrow.down.circle")
                    .font(.title)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading) {
                    Text("Software Update")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Keep WOW Monitor up to date")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button("Close") {
                    dismiss()
                }
                .buttonStyle(.bordered)
            }
            
            Divider()
            
            // Status content
            Group {
                switch updateManager.status {
                case .checking:
                    checkingView
                case .available(let update):
                    updateAvailableView(update)
                case .noUpdate:
                    noUpdateView
                case .downloading(let progress):
                    downloadingView(progress)
                case .readyToInstall(let update):
                    readyToInstallView(update)
                case .installing:
                    installingView
                case .error(let error):
                    errorView(error)
                case .disabled:
                    disabledView
                }
            }
            
            Spacer()
            
            // Settings and actions
            HStack {
                Button("Check Now") {
                    Task {
                        await updateManager.checkForUpdates(manual: true)
                    }
                }
                .disabled(updateManager.status == .checking)
                
                Spacer()
                
                if case .available(let update) = updateManager.status {
                    Button("Skip This Version") {
                        updateManager.skipVersion(update.version)
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Download") {
                        Task {
                            await updateManager.downloadUpdate(update)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .padding()
        .frame(width: 500, height: 400)
    }
    
    private var checkingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Checking for updates...")
                .font(.headline)
        }
    }
    
    private func updateAvailableView(_ update: AppUpdate) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading) {
                    Text("Update Available")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Version \(update.version) • \(update.formattedFileSize)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if update.isSecurityUpdate {
                    Label("Security", systemImage: "shield.fill")
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(4)
                }
            }
            
            ScrollView {
                Text(update.releaseNotes)
                    .font(.body)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .frame(height: 150)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    private var noUpdateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 48))
                .foregroundColor(.green)
            
            Text("You're up to date")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("WOW Monitor is currently up to date.")
                .font(.body)
                .foregroundColor(.secondary)
            
            if let lastCheck = updateManager.lastCheckDate {
                Text("Last checked: \(lastCheck, style: .relative) ago")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private func downloadingView(_ progress: Double) -> some View {
        VStack(spacing: 16) {
            ProgressView(value: progress)
                .progressViewStyle(.linear)
            
            Text("Downloading update...")
                .font(.headline)
            
            Text("\(Int(progress * 100))% complete")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func readyToInstallView(_ update: AppUpdate) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 48))
                .foregroundColor(.green)
            
            Text("Ready to Install")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Version \(update.version) has been downloaded and is ready to install.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Install and Restart") {
                Task {
                    await updateManager.installUpdate(update, from: URL(fileURLWithPath: ""))
                }
            }
            .buttonStyle(.borderedProminent)
        }
    }
    
    private var installingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Installing update...")
                .font(.headline)
            
            Text("The application will restart automatically.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func errorView(_ error: UpdateError) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("Update Error")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text(error.localizedDescription)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            if let suggestion = error.recoverySuggestion {
                Text(suggestion)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var disabledView: some View {
        VStack(spacing: 16) {
            Image(systemName: "pause.circle")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("Updates Disabled")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Automatic updates are currently disabled.")
                .font(.body)
                .foregroundColor(.secondary)
            
            Button("Enable Updates") {
                updateManager.enableUpdates()
            }
            .buttonStyle(.borderedProminent)
        }
    }
}

#Preview {
    UpdateView()
}