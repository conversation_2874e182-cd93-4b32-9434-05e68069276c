{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/HashTreeCollections.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/HashTreeCollections.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_AncestorHashSlots.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_AncestorHashSlots.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_AncestorHashSlots~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_AncestorHashSlots.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_Bitmap.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Bitmap.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Bitmap~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Bitmap.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_Bucket.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Bucket.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Bucket~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Bucket.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_Hash.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Hash.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Hash~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_Hash.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashLevel.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashLevel.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashLevel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashLevel.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Builder.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Builder.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Builder~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Builder.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Debugging.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Debugging.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Debugging~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Debugging.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Initializers.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Initializers.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Initializers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Initializers.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Invariants.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Invariants.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Invariants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Invariants.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Lookups.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Lookups.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Lookups~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Lookups.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Primitive Insertions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Insertions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Insertions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Insertions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Primitive Removals.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Removals.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Removals~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Removals.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Primitive Replacement.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Replacement.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Replacement~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Primitive Replacement.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Storage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Storage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Storage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Storage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural compactMapValues.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural compactMapValues.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural compactMapValues~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural compactMapValues.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural filter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural filter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural filter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural filter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural intersection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural intersection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural intersection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural intersection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural isDisjoint.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isDisjoint.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isDisjoint~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isDisjoint.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural isEqualSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isEqualSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isEqualSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isEqualSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural isSubset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isSubset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isSubset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural isSubset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural mapValues.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural mapValues.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural mapValues~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural mapValues.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural merge.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural merge.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural merge~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural merge.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural subtracting.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural subtracting.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural subtracting~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural subtracting.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural symmetricDifference.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural symmetricDifference.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural symmetricDifference~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural symmetricDifference.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural union.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural union.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural union~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Structural union.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Subtree Insertions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Insertions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Insertions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Insertions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Subtree Modify.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Modify.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Modify~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Modify.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Subtree Removals.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Removals.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Removals~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+Subtree Removals.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+UnsafeHandle.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+UnsafeHandle.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+UnsafeHandle~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode+UnsafeHandle.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNode.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNodeHeader.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNodeHeader.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNodeHeader~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashNodeHeader.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashSlot.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashSlot.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashSlot~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashSlot.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashStack.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashStack.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashStack~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashStack.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashTreeIterator.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashTreeIterator.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashTreeIterator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashTreeIterator.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashTreeStatistics.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashTreeStatistics.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashTreeStatistics~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_HashTreeStatistics.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_RawHashNode+UnsafeHandle.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_RawHashNode+UnsafeHandle.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_RawHashNode+UnsafeHandle~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_RawHashNode+UnsafeHandle.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_RawHashNode.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_RawHashNode.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_RawHashNode~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_RawHashNode.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_UnmanagedHashNode.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_UnmanagedHashNode.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_UnmanagedHashNode~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_UnmanagedHashNode.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_UnsafePath.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_UnsafePath.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_UnsafePath~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/_UnsafePath.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Codable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Codable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Codable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Codable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Collection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Collection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Collection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Collection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+CustomReflectable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+CustomReflectable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+CustomReflectable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+CustomReflectable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Debugging.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Debugging.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Debugging~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Debugging.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Descriptions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Descriptions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Descriptions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Descriptions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Equatable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Equatable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Equatable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Equatable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+ExpressibleByDictionaryLiteral.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+ExpressibleByDictionaryLiteral.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+ExpressibleByDictionaryLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+ExpressibleByDictionaryLiteral.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Filter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Filter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Filter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Filter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Hashable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Hashable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Hashable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Hashable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Initializers.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Initializers.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Initializers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Initializers.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Keys.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Keys.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Keys~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Keys.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+MapValues.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+MapValues.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+MapValues~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+MapValues.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Merge.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Merge.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Merge~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Merge.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Sendable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Sendable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Sendable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Sendable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Values.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Values.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Values~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary+Values.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeDictionary.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Codable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Codable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Codable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Codable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Collection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Collection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Collection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Collection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+CustomReflectable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+CustomReflectable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+CustomReflectable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+CustomReflectable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Debugging.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Debugging.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Debugging~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Debugging.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Descriptions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Descriptions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Descriptions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Descriptions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Equatable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Equatable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Equatable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Equatable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+ExpressibleByArrayLiteral.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+ExpressibleByArrayLiteral.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+ExpressibleByArrayLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+ExpressibleByArrayLiteral.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Extras.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Extras.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Extras~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Extras.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Filter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Filter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Filter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Filter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Hashable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Hashable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Hashable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Hashable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Sendable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Sendable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Sendable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Sendable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra Initializers.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra Initializers.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra Initializers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra Initializers.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra basics.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra basics.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra basics~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra basics.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra formIntersection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formIntersection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formIntersection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formIntersection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra formSymmetricDifference.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formSymmetricDifference.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formSymmetricDifference~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formSymmetricDifference.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra formUnion.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formUnion.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formUnion~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra formUnion.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra intersection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra intersection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra intersection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra intersection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isDisjoint.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isDisjoint.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isDisjoint~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isDisjoint.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isEqualSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isEqualSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isEqualSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isEqualSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isStrictSubset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isStrictSubset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isStrictSubset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isStrictSubset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isStrictSuperset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isStrictSuperset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isStrictSuperset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isStrictSuperset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isSubset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isSubset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isSubset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isSubset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isSuperset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isSuperset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isSuperset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra isSuperset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra subtract.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra subtract.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra subtract~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra subtract.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra subtracting.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra subtracting.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra subtracting~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra subtracting.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra symmetricDifference.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra symmetricDifference.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra symmetricDifference~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra symmetricDifference.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra union.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra union.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra union~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet+SetAlgebra union.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HashTreeCollections.build/TreeSet.swiftdeps"}}