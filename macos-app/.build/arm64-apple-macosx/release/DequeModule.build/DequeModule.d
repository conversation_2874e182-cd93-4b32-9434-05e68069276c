/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+Codable.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+Collection.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+CustomReflectable.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+Descriptions.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+Equatable.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+ExpressibleByArrayLiteral.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+Extras.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+Hashable.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque+Testing.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque._Storage.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque._UnsafeHandle.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/Deque.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/_DequeBuffer.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/_DequeBufferHeader.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/_DequeSlot.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/_UnsafeWrappedBuffer.swift.o : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/DequeModule.swiftmodule : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/DequeModule.swiftdoc : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DequeModule.build/DequeModule-Swift.h : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/DequeModule.swiftsourceinfo : /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._Storage.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Codable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Hashable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Equatable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+CustomReflectable.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque._UnsafeHandle.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Testing.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+ExpressibleByArrayLiteral.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Collection.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBufferHeader.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_UnsafeWrappedBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeBuffer.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Extras.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/Deque+Descriptions.swift /Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/DequeModule/_DequeSlot.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
