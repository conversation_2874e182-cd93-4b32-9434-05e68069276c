{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIO.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIO.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Ack/SocketAckEmitter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAckEmitter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAckEmitter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAckEmitter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Ack/SocketAckManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAckManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAckManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAckManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketAnyEvent.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAnyEvent.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAnyEvent~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketAnyEvent.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketEventHandler.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEventHandler.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEventHandler~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEventHandler.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClient.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClient.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClient.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClientConfiguration.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientConfiguration.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientConfiguration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientConfiguration.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClientOption.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientOption.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientOption~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientOption.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOClientSpec.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientSpec.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientSpec~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOClientSpec.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketIOStatus.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOStatus.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOStatus~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketIOStatus.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Client/SocketRawView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketRawView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketRawView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketRawView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngineClient.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineClient.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineClient.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEnginePacketType.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEnginePacketType.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEnginePacketType~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEnginePacketType.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEnginePollable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEnginePollable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEnginePollable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEnginePollable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngineSpec.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineSpec.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineSpec~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineSpec.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Engine/SocketEngineWebsocket.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineWebsocket.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineWebsocket~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketEngineWebsocket.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Manager/SocketManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Manager/SocketManagerSpec.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketManagerSpec.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketManagerSpec~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketManagerSpec.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Parse/SocketPacket.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketPacket.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketPacket~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketPacket.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Parse/SocketParsable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketParsable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketParsable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketParsable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketExtensions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketExtensions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketExtensions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketLogger.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketLogger.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketLogger~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketLogger.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketStringReader.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketStringReader.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketStringReader~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketStringReader.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/socket.io-client-swift/Source/SocketIO/Util/SocketTypes.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketTypes.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketTypes~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/SocketIO.build/SocketTypes.swiftdeps"}}