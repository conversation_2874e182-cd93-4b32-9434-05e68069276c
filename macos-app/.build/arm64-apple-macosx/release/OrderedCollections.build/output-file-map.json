{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedCollections.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedCollections.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_HashTable+Bucket.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Bucket.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Bucket~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Bucket.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_HashTable+BucketIterator.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+BucketIterator.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+BucketIterator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+BucketIterator.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_HashTable+Constants.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Constants.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Constants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Constants.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_HashTable+CustomStringConvertible.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+CustomStringConvertible.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+CustomStringConvertible~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+CustomStringConvertible.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_HashTable+Testing.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Testing.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Testing~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+Testing.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_HashTable+UnsafeHandle.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+UnsafeHandle.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+UnsafeHandle~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable+UnsafeHandle.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_HashTable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_HashTable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/HashTable/_Hashtable+Header.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_Hashtable+Header.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_Hashtable+Header~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_Hashtable+Header.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Codable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Codable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Codable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Codable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+CustomReflectable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+CustomReflectable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+CustomReflectable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+CustomReflectable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Deprecations.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Deprecations.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Deprecations~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Deprecations.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Descriptions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Descriptions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Descriptions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Descriptions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Elements.SubSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Elements.SubSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Elements.SubSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Elements.SubSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Elements.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Elements.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Elements~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Elements.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Equatable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Equatable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Equatable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Equatable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+ExpressibleByDictionaryLiteral.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+ExpressibleByDictionaryLiteral.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+ExpressibleByDictionaryLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+ExpressibleByDictionaryLiteral.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Hashable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Hashable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Hashable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Hashable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Initializers.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Initializers.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Initializers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Initializers.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Invariants.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Invariants.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Invariants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Invariants.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Partial MutableCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Partial MutableCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Partial MutableCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Partial MutableCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Partial RangeReplaceableCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Partial RangeReplaceableCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Partial RangeReplaceableCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Partial RangeReplaceableCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Sendable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Sendable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Sendable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Sendable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary+Values.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Values.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Values~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary+Values.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedDictionary/OrderedDictionary.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedDictionary.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Codable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Codable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Codable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Codable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+CustomReflectable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+CustomReflectable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+CustomReflectable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+CustomReflectable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Descriptions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Descriptions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Descriptions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Descriptions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Diffing.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Diffing.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Diffing~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Diffing.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Equatable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Equatable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Equatable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Equatable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+ExpressibleByArrayLiteral.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+ExpressibleByArrayLiteral.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+ExpressibleByArrayLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+ExpressibleByArrayLiteral.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Hashable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Hashable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Hashable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Hashable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Initializers.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Initializers.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Initializers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Initializers.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Insertions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Insertions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Insertions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Insertions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Invariants.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Invariants.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Invariants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Invariants.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial MutableCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial MutableCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial MutableCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial MutableCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial RangeReplaceableCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial RangeReplaceableCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial RangeReplaceableCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial RangeReplaceableCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra formIntersection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formIntersection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formIntersection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formIntersection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra formSymmetricDifference.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formSymmetricDifference.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formSymmetricDifference~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formSymmetricDifference.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra formUnion.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formUnion.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formUnion~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra formUnion.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra intersection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra intersection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra intersection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra intersection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra isDisjoint.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isDisjoint.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isDisjoint~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isDisjoint.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra isEqualSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isEqualSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isEqualSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isEqualSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra isStrictSubset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isStrictSubset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isStrictSubset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isStrictSubset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra isStrictSuperset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isStrictSuperset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isStrictSuperset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isStrictSuperset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra isSubset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isSubset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isSubset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isSubset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra isSuperset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isSuperset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isSuperset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra isSuperset.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra subtract.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra subtract.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra subtract~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra subtract.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra subtracting.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra subtracting.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra subtracting~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra subtracting.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra symmetricDifference.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra symmetricDifference.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra symmetricDifference~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra symmetricDifference.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra union.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra union.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra union~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra union.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Partial SetAlgebra+Basics.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra+Basics.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra+Basics~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Partial SetAlgebra+Basics.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+RandomAccessCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+RandomAccessCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+RandomAccessCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+RandomAccessCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+ReserveCapacity.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+ReserveCapacity.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+ReserveCapacity~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+ReserveCapacity.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Sendable.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Sendable.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Sendable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Sendable.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+SubSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+SubSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+SubSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+SubSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+Testing.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Testing.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Testing~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+Testing.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+UnorderedView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+UnorderedView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+UnorderedView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+UnorderedView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet+UnstableInternals.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+UnstableInternals.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+UnstableInternals~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet+UnstableInternals.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/OrderedSet/OrderedSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/OrderedSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/OrderedCollections/Utilities/_UnsafeBitset.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_UnsafeBitset.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_UnsafeBitset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/OrderedCollections.build/_UnsafeBitset.swiftdeps"}}