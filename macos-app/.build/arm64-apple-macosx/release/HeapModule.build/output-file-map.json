{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/HeapModule.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/HeapModule.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+Descriptions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+Descriptions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+Descriptions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+Descriptions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+ExpressibleByArrayLiteral.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+ExpressibleByArrayLiteral.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+ExpressibleByArrayLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+ExpressibleByArrayLiteral.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+Invariants.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+Invariants.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+Invariants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+Invariants.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+UnsafeHandle.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+UnsafeHandle.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+UnsafeHandle~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap+UnsafeHandle.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/Heap.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/_HeapNode.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/_HeapNode.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/_HeapNode~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/HeapModule.build/_HeapNode.swiftdeps"}}