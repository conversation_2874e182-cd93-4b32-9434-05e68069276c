version: "Apple Swift version 6.1.2 (swiftlang-*******.2 clang-1700.0.13.5)"
options: "39d327a060336601730b528dd10b7d6cc27cae616163c8cc926853ad52b69fb1"
build_start_time: [1750462327, 118297000]
build_end_time: [1750462328, 543971000]
inputs:
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+Descriptions.swift": [1750462297, 887142226]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+ExpressibleByArrayLiteral.swift": [1750462297, 887267059]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+Invariants.swift": [1750462297, 887353393]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap+UnsafeHandle.swift": [1750462297, 887507476]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/Heap.swift": [1750462297, 887672225]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HeapModule/_HeapNode.swift": [1750462297, 888371724]
