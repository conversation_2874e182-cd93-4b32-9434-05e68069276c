{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/_CryptoExtras.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/_CryptoExtras.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/AES_CBC.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CBC.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CBC~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CBC.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/AES_CFB.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CFB.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CFB~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CFB.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/AES_CTR.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CTR.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CTR~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CTR.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/AES_GCM_SIV.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_GCM_SIV.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_GCM_SIV~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_GCM_SIV.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/Block Function.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Block Function.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Block Function~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Block Function.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/BoringSSL/AES_CFB_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CFB_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CFB_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CFB_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/BoringSSL/AES_CTR_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CTR_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CTR_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_CTR_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/AES/BoringSSL/AES_GCM_SIV_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_GCM_SIV_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_GCM_SIV_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/AES_GCM_SIV_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARC+API.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARC+API.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARC+API~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARC+API.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARC.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARC.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARC~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARC.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARCCredential.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCCredential.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCCredential~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCCredential.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARCEncoding.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCEncoding.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCEncoding~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCEncoding.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARCPrecredential.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCPrecredential.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCPrecredential~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCPrecredential.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARCPresentation.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCPresentation.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCPresentation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCPresentation.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARCRequest.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCRequest.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCRequest~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCRequest.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARCResponse.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCResponse.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCResponse~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCResponse.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ARC/ARCServer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCServer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCServer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ARCServer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ChaCha20CTR/BoringSSL/ChaCha20CTR_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ChaCha20CTR_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ChaCha20CTR_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ChaCha20CTR_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ChaCha20CTR/ChaCha20CTR.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ChaCha20CTR.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ChaCha20CTR~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ChaCha20CTR.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ECToolbox/BoringSSL/ECToolbox_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ECToolbox_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ECToolbox_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ECToolbox_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ECToolbox/ECToolbox.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ECToolbox.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ECToolbox~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ECToolbox.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/H2G/HashToField.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/HashToField.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/HashToField~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/HashToField.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Key Derivation/KDF.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/KDF.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/KDF~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/KDF.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Key Derivation/PBKDF2/BoringSSL/PBKDF2_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Key Derivation/PBKDF2/BoringSSL/PBKDF2_commoncrypto.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2_commoncrypto.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2_commoncrypto~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2_commoncrypto.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Key Derivation/PBKDF2/PBKDF2.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PBKDF2.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Key Derivation/Scrypt/BoringSSL/Scrypt_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Scrypt_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Scrypt_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Scrypt_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Key Derivation/Scrypt/Scrypt.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Scrypt.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Scrypt~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Scrypt.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/OPRFs/OPRF.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRF.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRF~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRF.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/OPRFs/OPRFClient.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRFClient.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRFClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRFClient.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/OPRFs/OPRFServer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRFServer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRFServer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/OPRFServer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/OPRFs/VOPRF+API.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRF+API.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRF+API~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRF+API.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/OPRFs/VOPRFClient.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRFClient.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRFClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRFClient.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/OPRFs/VOPRFServer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRFServer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRFServer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/VOPRFServer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/RSA/RSA+BlindSigning.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA+BlindSigning.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA+BlindSigning~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA+BlindSigning.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/RSA/RSA.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/RSA/RSA_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/RSA/RSA_security.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA_security.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA_security~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/RSA_security.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/BoringSSLHelpers.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/BoringSSLHelpers.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/BoringSSLHelpers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/BoringSSLHelpers.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/CryptoKitErrors_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/CryptoKitErrors_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/CryptoKitErrors_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/CryptoKitErrors_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/DigestType.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/DigestType.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/DigestType~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/DigestType.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/Error.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Error.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Error~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Error.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/I2OSP.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/I2OSP.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/I2OSP~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/I2OSP.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/IntegerEncoding.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/IntegerEncoding.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/IntegerEncoding~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/IntegerEncoding.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/PEMDocument.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PEMDocument.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PEMDocument~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PEMDocument.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/PrettyBytes.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PrettyBytes.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PrettyBytes~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/PrettyBytes.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/Util/SubjectPublicKeyInfo.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/SubjectPublicKeyInfo.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/SubjectPublicKeyInfo~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/SubjectPublicKeyInfo.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ZKPs/DLEQ.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/DLEQ.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/DLEQ~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/DLEQ.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ZKPs/Prover.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Prover.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Prover~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Prover.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ZKPs/Verifier.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Verifier.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Verifier~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/Verifier.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/_CryptoExtras/ZKPs/ZKPToolbox.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ZKPToolbox.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ZKPToolbox~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/ZKPToolbox.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/DerivedSources/resource_bundle_accessor.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/_CryptoExtras.build/resource_bundle_accessor.swiftdeps"}}