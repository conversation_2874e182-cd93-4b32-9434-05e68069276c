{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncAlgorithms.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncAlgorithms.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncAdjacentPairsSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncAdjacentPairsSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncAdjacentPairsSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncAdjacentPairsSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncBufferedByteIterator.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncBufferedByteIterator.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncBufferedByteIterator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncBufferedByteIterator.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChain2Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChain2Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChain2Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChain2Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChain3Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChain3Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChain3Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChain3Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunkedByGroupSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunkedByGroupSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunkedByGroupSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunkedByGroupSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunkedOnProjectionSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunkedOnProjectionSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunkedOnProjectionSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunkedOnProjectionSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunksOfCountOrSignalSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunksOfCountOrSignalSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunksOfCountOrSignalSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunksOfCountOrSignalSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunksOfCountSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunksOfCountSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunksOfCountSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChunksOfCountSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncCompactedSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCompactedSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCompactedSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCompactedSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncExclusiveReductionsSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncExclusiveReductionsSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncExclusiveReductionsSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncExclusiveReductionsSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncInclusiveReductionsSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncInclusiveReductionsSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncInclusiveReductionsSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncInclusiveReductionsSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncJoinedBySeparatorSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncJoinedBySeparatorSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncJoinedBySeparatorSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncJoinedBySeparatorSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncJoinedSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncJoinedSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncJoinedSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncJoinedSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncRemoveDuplicatesSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncRemoveDuplicatesSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncRemoveDuplicatesSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncRemoveDuplicatesSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncSyncSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncSyncSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncSyncSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncSyncSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncThrottleSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrottleSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrottleSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrottleSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncThrowingExclusiveReductionsSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingExclusiveReductionsSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingExclusiveReductionsSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingExclusiveReductionsSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncThrowingInclusiveReductionsSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingInclusiveReductionsSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingInclusiveReductionsSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingInclusiveReductionsSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncTimerSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncTimerSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncTimerSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncTimerSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/AsyncBufferSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncBufferSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncBufferSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncBufferSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/BoundedBufferStateMachine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/BoundedBufferStateMachine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/BoundedBufferStateMachine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/BoundedBufferStateMachine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/BoundedBufferStorage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/BoundedBufferStorage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/BoundedBufferStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/BoundedBufferStorage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/UnboundedBufferStateMachine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnboundedBufferStateMachine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnboundedBufferStateMachine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnboundedBufferStateMachine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/UnboundedBufferStorage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnboundedBufferStorage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnboundedBufferStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnboundedBufferStorage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/AsyncChannel.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChannel.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChannel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncChannel.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/AsyncThrowingChannel.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingChannel.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingChannel~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncThrowingChannel.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/ChannelStateMachine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ChannelStateMachine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ChannelStateMachine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ChannelStateMachine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/ChannelStorage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ChannelStorage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ChannelStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ChannelStorage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/AsyncCombineLatest2Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCombineLatest2Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCombineLatest2Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCombineLatest2Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/AsyncCombineLatest3Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCombineLatest3Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCombineLatest3Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncCombineLatest3Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/CombineLatestStateMachine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/CombineLatestStateMachine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/CombineLatestStateMachine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/CombineLatestStateMachine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/CombineLatestStorage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/CombineLatestStorage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/CombineLatestStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/CombineLatestStorage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Debounce/AsyncDebounceSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncDebounceSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncDebounceSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncDebounceSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Debounce/DebounceStateMachine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/DebounceStateMachine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/DebounceStateMachine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/DebounceStateMachine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Debounce/DebounceStorage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/DebounceStorage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/DebounceStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/DebounceStorage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Dictionary.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Dictionary.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Dictionary~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Dictionary.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Interspersed/AsyncInterspersedSequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncInterspersedSequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncInterspersedSequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncInterspersedSequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Locking.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Locking.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Locking~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Locking.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/AsyncMerge2Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncMerge2Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncMerge2Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncMerge2Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/AsyncMerge3Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncMerge3Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncMerge3Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncMerge3Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/MergeStateMachine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/MergeStateMachine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/MergeStateMachine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/MergeStateMachine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/MergeStorage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/MergeStorage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/MergeStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/MergeStorage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/RangeReplaceableCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/RangeReplaceableCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/RangeReplaceableCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/RangeReplaceableCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Rethrow.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Rethrow.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Rethrow~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/Rethrow.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/SetAlgebra.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/SetAlgebra.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/SetAlgebra~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/SetAlgebra.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/UnsafeTransfer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnsafeTransfer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnsafeTransfer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/UnsafeTransfer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/AsyncZip2Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncZip2Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncZip2Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncZip2Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/AsyncZip3Sequence.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncZip3Sequence.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncZip3Sequence~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/AsyncZip3Sequence.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/ZipStateMachine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ZipStateMachine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ZipStateMachine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ZipStateMachine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/ZipStorage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ZipStorage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ZipStorage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/AsyncAlgorithms.build/ZipStorage.swiftdeps"}}