{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DGCharts.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DGCharts.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Animation/Animator.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Animator.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Animator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Animator.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Animation/ChartAnimationEasing.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartAnimationEasing.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartAnimationEasing~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartAnimationEasing.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BarChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BarLineChartViewBase.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineChartViewBase.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineChartViewBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineChartViewBase.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BubbleChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/CandleStickChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/ChartViewBase.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartViewBase.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartViewBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartViewBase.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/CombinedChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/HorizontalBarChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/LineChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/PieChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/PieRadarChartViewBase.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarChartViewBase.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarChartViewBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarChartViewBase.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/RadarChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/ScatterChartView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/AxisBase.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisBase.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisBase.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/ChartLimitLine.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartLimitLine.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartLimitLine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartLimitLine.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/ComponentBase.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ComponentBase.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ComponentBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ComponentBase.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Description.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Description.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Description~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Description.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Legend.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Legend.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Legend~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Legend.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/LegendEntry.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendEntry.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendEntry~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendEntry.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Marker.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Marker.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Marker~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Marker.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/MarkerImage.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerImage.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerImage~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerImage.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/MarkerView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MarkerView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/XAxis.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxis.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxis~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxis.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/YAxis.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxis.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxis~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxis.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/ChartBaseDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartBaseDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartBaseDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartBaseDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartDataEntry.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataEntry.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataEntry~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataEntry.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarLineScatterCandleBubbleChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarLineScatterCandleBubbleChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartDataEntry.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataEntry.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataEntry~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataEntry.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartDataEntry.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataEntry.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataEntry~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataEntry.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataEntry.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntry.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntry~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntry.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataEntryBase.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntryBase.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntryBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataEntryBase.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CombinedChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineRadarChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineScatterCandleRadarChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartDataEntry.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataEntry.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataEntry~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataEntry.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartDataEntry.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataEntry.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataEntry~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataEntry.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ScatterChartData.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartData.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartData~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartData.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ScatterChartDataSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BarChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BarLineScatterCandleBubbleChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BubbleChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/CandleChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/ChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineRadarChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineScatterCandleRadarChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/PieChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/RadarChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/ScatterChartDataSetProtocol.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSetProtocol.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSetProtocol~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataSetProtocol.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Filters/DataApproximator+N.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator+N.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator+N~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator+N.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Filters/DataApproximator.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataApproximator.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/AxisValueFormatter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisValueFormatter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisValueFormatter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisValueFormatter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultAxisValueFormatter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultAxisValueFormatter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultAxisValueFormatter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultAxisValueFormatter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultFillFormatter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultFillFormatter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultFillFormatter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultFillFormatter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultValueFormatter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultValueFormatter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultValueFormatter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DefaultValueFormatter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/FillFormatter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/FillFormatter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/FillFormatter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/FillFormatter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/IndexAxisValueFormatter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/IndexAxisValueFormatter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/IndexAxisValueFormatter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/IndexAxisValueFormatter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/ValueFormatter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ValueFormatter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ValueFormatter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ValueFormatter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/BarHighlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarHighlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarHighlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarHighlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/ChartHighlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartHighlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartHighlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartHighlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/CombinedHighlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedHighlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedHighlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedHighlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Highlight.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlight.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlight~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlight.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Highlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Highlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/HorizontalBarHighlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarHighlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarHighlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarHighlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/PieHighlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieHighlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieHighlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieHighlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/PieRadarHighlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarHighlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarHighlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieRadarHighlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/RadarHighlighter.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarHighlighter.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarHighlighter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarHighlighter.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Range.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Range.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Range~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Range.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BarChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BarLineScatterCandleBubbleChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BubbleChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/CandleChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/ChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/CombinedChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/LineChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/ScatterChartDataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartDataProvider.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedMoveViewJob.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedMoveViewJob.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedMoveViewJob~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedMoveViewJob.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedViewPortJob.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedViewPortJob.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedViewPortJob~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedViewPortJob.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedZoomViewJob.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedZoomViewJob.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedZoomViewJob~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AnimatedZoomViewJob.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/MoveViewJob.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MoveViewJob.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MoveViewJob~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/MoveViewJob.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/ViewPortJob.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortJob.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortJob~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortJob.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/ZoomViewJob.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ZoomViewJob.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ZoomViewJob~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ZoomViewJob.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/AxisRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/AxisRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BarChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BarLineScatterCandleBubbleRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BarLineScatterCandleBubbleRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BubbleChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/BubbleChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/CandleStickChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CandleStickChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/CombinedChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CombinedChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/DataRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DataRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/HorizontalBarChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/HorizontalBarChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LegendRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LegendRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineRadarRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineRadarRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineScatterCandleRadarRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/LineScatterCandleRadarRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/PieChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/PieChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/RadarChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/RadarChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Renderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Renderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Renderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Renderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ChevronDownShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronDownShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronDownShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronDownShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ChevronUpShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronUpShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronUpShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChevronUpShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/CircleShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CircleShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CircleShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CircleShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/CrossShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CrossShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CrossShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/CrossShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/SquareShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/SquareShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/SquareShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/SquareShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/TriangleShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TriangleShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TriangleShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TriangleShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/XShapeRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XShapeRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XShapeRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XShapeRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/ScatterChartRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ScatterChartRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRendererHorizontalBarChart.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererHorizontalBarChart.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererHorizontalBarChart~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererHorizontalBarChart.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRendererRadarChart.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererRadarChart.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererRadarChart~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/XAxisRendererRadarChart.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRenderer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRenderer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRenderer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRenderer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRendererHorizontalBarChart.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererHorizontalBarChart.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererHorizontalBarChart~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererHorizontalBarChart.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRendererRadarChart.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererRadarChart.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererRadarChart~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/YAxisRendererRadarChart.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ChartColorTemplates.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartColorTemplates.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartColorTemplates~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartColorTemplates.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ChartUtils.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartUtils.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartUtils~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ChartUtils.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Fill.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Fill.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Fill~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Fill.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Indexed.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Indexed.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Indexed~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Indexed.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Partition.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Partition.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Partition~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Partition.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Accessibility.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Accessibility.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Accessibility~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Accessibility.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Color.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Color.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Color~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Color.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Gestures.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Gestures.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Gestures~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Gestures.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Graphics.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Graphics.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Graphics~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Graphics.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Touch Handling.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Touch Handling.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Touch Handling~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform+Touch Handling.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Platform.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Sequence+KeyPath.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Sequence+KeyPath.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Sequence+KeyPath~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Sequence+KeyPath.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Transformer.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Transformer.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Transformer~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/Transformer.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/TransformerHorizontalBarChart.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TransformerHorizontalBarChart.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TransformerHorizontalBarChart~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/TransformerHorizontalBarChart.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ViewPortHandler.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortHandler.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortHandler~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/ViewPortHandler.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DerivedSources/resource_bundle_accessor.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/resource_bundle_accessor.swiftdeps"}}