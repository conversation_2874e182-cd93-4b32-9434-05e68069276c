{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/WOWMonitor.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/WOWMonitor.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/APIClient.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/APIClient.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/APIClient~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/APIClient.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/App.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/App.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/App~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/App.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/AppState.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/AppState.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/AppState~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/AppState.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ContentView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ContentView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ContentView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ContentView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/DashboardView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DashboardView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DashboardView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DashboardView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/DockerView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DockerView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DockerView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/DockerView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ExportManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ExportManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ExportManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ExportManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/KeyboardShortcutsManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/KeyboardShortcutsManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/KeyboardShortcutsManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/KeyboardShortcutsManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/LogsView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/LogsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/LogsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/LogsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/MenuBarView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/MenuBarView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/MenuBarView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/MenuBarView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/Models.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/Models.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/Models~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/Models.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/NotificationManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/NotificationsView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/NotificationsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/PreferencesManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/PreferencesManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/PreferencesManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/PreferencesManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/QuickSearchView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/QuickSearchView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/QuickSearchView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/QuickSearchView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ServicesView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ServicesView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ServicesView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ServicesView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/SettingsView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SettingsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SettingsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SettingsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/ShortcutsManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ShortcutsManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ShortcutsManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/ShortcutsManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/SocketManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SocketManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SocketManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SocketManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/SystemMetricsView.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SystemMetricsView.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SystemMetricsView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/SystemMetricsView.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/UpdateManager.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/UpdateManager.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/UpdateManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/UpdateManager.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/Sources/main.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/main.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/main~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/WOWMonitor.build/main.swiftdeps"}}