{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/CryptoBoringWrapper.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/CryptoBoringWrapper.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/CryptoBoringWrapper/AEAD/BoringSSLAEAD.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/BoringSSLAEAD.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/BoringSSLAEAD~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/BoringSSLAEAD.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/CryptoBoringWrapper/CryptoKitErrors_boring.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/CryptoKitErrors_boring.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/CryptoKitErrors_boring~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/CryptoKitErrors_boring.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/CryptoBoringWrapper/EC/EllipticCurve.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/EllipticCurve.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/EllipticCurve~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/EllipticCurve.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/CryptoBoringWrapper/EC/EllipticCurvePoint.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/EllipticCurvePoint.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/EllipticCurvePoint~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/EllipticCurvePoint.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/CryptoBoringWrapper/Util/ArbitraryPrecisionInteger.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/ArbitraryPrecisionInteger.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/ArbitraryPrecisionInteger~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/ArbitraryPrecisionInteger.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/CryptoBoringWrapper/Util/FiniteFieldArithmeticContext.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/FiniteFieldArithmeticContext.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/FiniteFieldArithmeticContext~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/FiniteFieldArithmeticContext.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/CryptoBoringWrapper/Util/RandomBytes.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/RandomBytes.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/RandomBytes~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/RandomBytes.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/DerivedSources/resource_bundle_accessor.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/CryptoBoringWrapper.build/resource_bundle_accessor.swiftdeps"}}