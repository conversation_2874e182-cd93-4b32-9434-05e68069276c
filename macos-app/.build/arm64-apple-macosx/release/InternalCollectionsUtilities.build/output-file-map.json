{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/InternalCollectionsUtilities.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/InternalCollectionsUtilities.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/FixedWidthInteger+roundUpToPowerOfTwo.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/FixedWidthInteger+roundUpToPowerOfTwo~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/FixedWidthInteger+roundUpToPowerOfTwo.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer rank.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Integer rank.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Integer rank~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Integer rank.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first and last set bit.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+first and last set bit.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+first and last set bit~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+first and last set bit.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+reversed.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+reversed~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+reversed.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+Index.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+Index~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+Index.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+_Word.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+_Word~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+_Word.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_SortedCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_SortedCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_SortedCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UniqueCollection.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UniqueCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UniqueCollection.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Debugging.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Debugging~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Debugging.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Descriptions.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Descriptions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Descriptions.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/RandomAccessCollection+Offsets.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/RandomAccessCollection+Offsets~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/RandomAccessCollection+Offsets.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeBufferPointer+Extras.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeBufferPointer+Extras~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeBufferPointer+Extras.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeMutableBufferPointer+Extras.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeMutableBufferPointer+Extras~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeMutableBufferPointer+Extras.swiftdeps"}}