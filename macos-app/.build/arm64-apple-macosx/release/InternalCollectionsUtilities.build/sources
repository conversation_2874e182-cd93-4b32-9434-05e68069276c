/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift
'/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer rank.swift'
'/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first and last set bit.swift'
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift
