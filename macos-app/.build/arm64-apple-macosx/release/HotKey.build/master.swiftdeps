version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "8838702dbbc465a7627c23eaaf74f01e655428f7c31d1227f87a2665a66c6f24"
build_start_time: [1750462324, 990240000]
build_end_time: [1750462345, 343145000]
inputs:
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/HotKey.swift": [1750462299, 225489591]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/HotKeysController.swift": [1750462299, 226255256]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/Key.swift": [1750462299, 227835504]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/KeyCombo+System.swift": [1750462299, 228044712]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/KeyCombo.swift": [1750462299, 228141503]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/HotKey/Sources/HotKey/NSEventModifierFlags+HotKey.swift": [1750462299, 228231920]
