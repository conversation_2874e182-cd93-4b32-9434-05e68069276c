{"": {"dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Logging.d", "object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Logging.o", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/master.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-log/Sources/Logging/Locks.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Locks.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Locks~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Locks.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-log/Sources/Logging/LogHandler.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/LogHandler.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/LogHandler~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/LogHandler.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-log/Sources/Logging/Logging.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Logging.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Logging~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/Logging.swiftdeps"}, "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-log/Sources/Logging/MetadataProvider.swift": {"object": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/MetadataProvider.swift.o", "swiftmodule": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/MetadataProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Logging.build/MetadataProvider.swiftdeps"}}