version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "60e0ec2f2c1fb83ba78bd5db12fb26b66f6b02e8a5923eae0522045c51022c39"
build_start_time: [1750462324, 686765000]
build_end_time: [1750462365, 949021000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/DerivedSources/resource_bundle_accessor.swift"
  : [1750462321, 565454240]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Alamofire.swift": [1750462299, 486049074]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/AFError.swift": [1750462299, 487775488]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataRequest.swift": [1750462299, 488022405]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataStreamRequest.swift": [1750462299, 488146780]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DownloadRequest.swift": [1750462299, 489616611]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPHeaders.swift": [1750462299, 489920610]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPMethod.swift": [1750462299, 490267901]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Notifications.swift": [1750462299, 490630109]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoder.swift": [1750462299, 491594441]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoding.swift": [1750462299, 493130480]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Protected.swift": [1750462299, 495361352]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Request.swift": [1750462299, 495884601]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/RequestTaskMap.swift": [1750462299, 496135351]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Response.swift": [1750462299, 496267684]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Session.swift": [1750462299, 496467517]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/SessionDelegate.swift": [1750462299, 496604350]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/URLConvertible+URLRequestConvertible.swift": [1750462299, 496733600]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/UploadRequest.swift": [1750462299, 496879475]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/WebSocketRequest.swift": [1750462299, 496993266]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/DispatchQueue+Alamofire.swift": [1750462299, 497170599]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/OperationQueue+Alamofire.swift": [1750462299, 502020509]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/Result+Alamofire.swift": [1750462299, 502531883]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/StringEncoding+Alamofire.swift": [1750462299, 502706174]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLRequest+Alamofire.swift": [1750462299, 502803341]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLSessionConfiguration+Alamofire.swift": [1750462299, 502919466]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AlamofireExtended.swift": [1750462299, 503093465]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AuthenticationInterceptor.swift": [1750462299, 503218674]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/CachedResponseHandler.swift": [1750462299, 506648794]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Combine.swift": [1750462299, 506879710]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Concurrency.swift": [1750462299, 507058251]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/EventMonitor.swift": [1750462299, 507194168]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartFormData.swift": [1750462299, 507320167]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartUpload.swift": [1750462299, 510563413]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/NetworkReachabilityManager.swift": [1750462299, 512458951]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RedirectHandler.swift": [1750462299, 512671826]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestCompression.swift": [1750462299, 512828326]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestInterceptor.swift": [1750462299, 512982117]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ResponseSerialization.swift": [1750462299, 513117825]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RetryPolicy.swift": [1750462299, 513248117]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ServerTrustEvaluation.swift": [1750462299, 513419283]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/URLEncodedFormEncoder.swift": [1750462299, 513924241]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Validation.swift": [1750462299, 514130574]
