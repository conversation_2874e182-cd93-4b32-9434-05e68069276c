/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Alamofire.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/AFError.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataRequest.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DataStreamRequest.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/DownloadRequest.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPHeaders.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/HTTPMethod.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Notifications.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoder.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/ParameterEncoding.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Protected.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Request.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/RequestTaskMap.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Response.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/Session.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/SessionDelegate.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/URLConvertible+URLRequestConvertible.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/UploadRequest.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Core/WebSocketRequest.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/DispatchQueue+Alamofire.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/OperationQueue+Alamofire.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/Result+Alamofire.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/StringEncoding+Alamofire.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLRequest+Alamofire.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Extensions/URLSessionConfiguration+Alamofire.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AlamofireExtended.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/AuthenticationInterceptor.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/CachedResponseHandler.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Combine.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Concurrency.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/EventMonitor.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartFormData.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/MultipartUpload.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/NetworkReachabilityManager.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RedirectHandler.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestCompression.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RequestInterceptor.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ResponseSerialization.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/RetryPolicy.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/ServerTrustEvaluation.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/URLEncodedFormEncoder.swift
/Users/<USER>/w-o-w/macos-app/.build/checkouts/Alamofire/Source/Features/Validation.swift
/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Alamofire.build/DerivedSources/resource_bundle_accessor.swift
