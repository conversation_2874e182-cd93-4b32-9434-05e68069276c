# Change Log

## [Unreleased](https://github.com/SwiftyJSON/SwiftyJSON/tree/HEAD)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/2.2.0...HEAD)

**Closed issues:**

- 156 compiler errors Mavericks + Xcode 6.2 [\#220](https://github.com/SwiftyJSON/SwiftyJSON/issues/220)

- 'AnyObject' is not convertible to 'String'; did you mean to use 'as!' to force downcast? [\#218](https://github.com/SwiftyJSON/SwiftyJSON/issues/218)

- pod -\> SwiftyJSON \(2.1.3\) is out-of-date if we compare it to the version mentioned in README.md file. [\#212](https://github.com/SwiftyJSON/SwiftyJSON/issues/212)

- 无法获取到 2.2版本的 [\#211](https://github.com/SwiftyJSON/SwiftyJSON/issues/211)

- Publish Podspec for version 2.2.0 [\#210](https://github.com/SwiftyJSON/SwiftyJSON/issues/210)

- dropping elements? or am I doing something wrong? [\#209](https://github.com/SwiftyJSON/SwiftyJSON/issues/209)

- Not working with Swift 1.2 [\#208](https://github.com/SwiftyJSON/SwiftyJSON/issues/208)

- 在 Mac 项目里用 Carthage 无法编译 [\#193](https://github.com/SwiftyJSON/SwiftyJSON/issues/193)

- 使用中发现解析效率比较低 [\#190](https://github.com/SwiftyJSON/SwiftyJSON/issues/190)

- Looks like it will require a change of "as"es to "as!" for Swift 1.2... [\#150](https://github.com/SwiftyJSON/SwiftyJSON/issues/150)

- No response appeared [\#118](https://github.com/SwiftyJSON/SwiftyJSON/issues/118)

- Swift Optional Values from JSON [\#116](https://github.com/SwiftyJSON/SwiftyJSON/issues/116)

- It seems not easy to manipulate an array or dictionary? [\#110](https://github.com/SwiftyJSON/SwiftyJSON/issues/110)

**Merged pull requests:**

- Fix for xcode 6.3..1 issue [\#224](https://github.com/SwiftyJSON/SwiftyJSON/pull/224) ([datomnurdin](https://github.com/datomnurdin))

- Update the first two examples snippets [\#223](https://github.com/SwiftyJSON/SwiftyJSON/pull/223) ([kmikael](https://github.com/kmikael))

- Allow .number to parse number from string instead of just numberValue [\#219](https://github.com/SwiftyJSON/SwiftyJSON/pull/219) ([yonaskolb](https://github.com/yonaskolb))

- Fixed spelling and grammar mistakes in README.md. Made some swift syntax... [\#214](https://github.com/SwiftyJSON/SwiftyJSON/pull/214) ([pRizz](https://github.com/pRizz))

- Added a function to deep merge a JSON object into another JSON object... [\#725](https://github.com/SwiftyJSON/SwiftyJSON/pull/725) ([danielkiedrowski](https://github.com/danielkiedrowski))

## [2.2.0](https://github.com/SwiftyJSON/SwiftyJSON/tree/2.2.0) (2015-04-13)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/2.1.3...2.2.0)

**Closed issues:**

- init doesn't set type correctly [\#206](https://github.com/SwiftyJSON/SwiftyJSON/issues/206)

- SwitfyJSON breaks with update to iOS 8.3 & Xcode 6.3 [\#200](https://github.com/SwiftyJSON/SwiftyJSON/issues/200)

- 'NSString?' is not convertible to 'String?' error with Swift 1.2 [\#198](https://github.com/SwiftyJSON/SwiftyJSON/issues/198)

- I can't install it by carthage [\#181](https://github.com/SwiftyJSON/SwiftyJSON/issues/181)

- Can't compare JSON to Float [\#171](https://github.com/SwiftyJSON/SwiftyJSON/issues/171)

- extend data to results [\#160](https://github.com/SwiftyJSON/SwiftyJSON/issues/160)

- Create JSON From String [\#159](https://github.com/SwiftyJSON/SwiftyJSON/issues/159)

- No Cocoapods support for iOS 7 [\#151](https://github.com/SwiftyJSON/SwiftyJSON/issues/151)

- Update to Swift 1.2 [\#148](https://github.com/SwiftyJSON/SwiftyJSON/issues/148)

- Url slashes ' / ' are being replaced with ' \/ ' [\#145](https://github.com/SwiftyJSON/SwiftyJSON/issues/145)

- Issues when using carthage [\#144](https://github.com/SwiftyJSON/SwiftyJSON/issues/144)

- Can not convert \[JSON\] to JSON [\#143](https://github.com/SwiftyJSON/SwiftyJSON/issues/143)

- \[!\] Unable to find a specification for `SwiftyJSON \(= 2.1.3\)` [\#141](https://github.com/SwiftyJSON/SwiftyJSON/issues/141)

- Deployment target iOS 7 or iOS 8? [\#131](https://github.com/SwiftyJSON/SwiftyJSON/issues/131)

- Cocoapods support [\#126](https://github.com/SwiftyJSON/SwiftyJSON/issues/126)

**Merged pull requests:**

- Only building tests for testing [\#207](https://github.com/SwiftyJSON/SwiftyJSON/pull/207) ([spanage](https://github.com/spanage))

- Added compatibility with Swift 1.2. [\#204](https://github.com/SwiftyJSON/SwiftyJSON/pull/204) ([jankaltoun](https://github.com/jankaltoun))

- Fix for issue \#200 [\#203](https://github.com/SwiftyJSON/SwiftyJSON/pull/203) ([chschu](https://github.com/chschu))

- Updated to Swift 1.2 [\#202](https://github.com/SwiftyJSON/SwiftyJSON/pull/202) ([scottdelly](https://github.com/scottdelly))

- Updated to Swift 1.2 [\#201](https://github.com/SwiftyJSON/SwiftyJSON/pull/201) ([scottdelly](https://github.com/scottdelly))

- Update to Swift 1.2 [\#199](https://github.com/SwiftyJSON/SwiftyJSON/pull/199) ([kimdv](https://github.com/kimdv))

- Should not get subscript from AnyObject. [\#196](https://github.com/SwiftyJSON/SwiftyJSON/pull/196) ([Candyroot](https://github.com/Candyroot))

- Update for Swift 1.2 [\#195](https://github.com/SwiftyJSON/SwiftyJSON/pull/195) ([justinmakaila](https://github.com/justinmakaila))

- Update README.md [\#194](https://github.com/SwiftyJSON/SwiftyJSON/pull/194) ([manijshrestha](https://github.com/manijshrestha))

- Optimize the code to avoid useless casts to swift arrays. [\#188](https://github.com/SwiftyJSON/SwiftyJSON/pull/188) ([mirion](https://github.com/mirion))

- Fixed the buildable name in the OSX scheme [\#187](https://github.com/SwiftyJSON/SwiftyJSON/pull/187) ([cnoon](https://github.com/cnoon))

- Updated code signing identities for OSX target and tests [\#186](https://github.com/SwiftyJSON/SwiftyJSON/pull/186) ([cnoon](https://github.com/cnoon))

- Fix int overflow compile error [\#178](https://github.com/SwiftyJSON/SwiftyJSON/pull/178) ([mono0926](https://github.com/mono0926))

- Fixed a bug when accessing a value directly via a string subscript when the current object is a dictionary [\#176](https://github.com/SwiftyJSON/SwiftyJSON/pull/176) ([JosephDuffy](https://github.com/JosephDuffy))

- Better support for carthage users [\#174](https://github.com/SwiftyJSON/SwiftyJSON/pull/174) ([rromanchuk](https://github.com/rromanchuk))

- Fixes a 32bit/64bit issue. [\#172](https://github.com/SwiftyJSON/SwiftyJSON/pull/172) ([enhorn](https://github.com/enhorn))

- Update README for new Cocoapods [\#170](https://github.com/SwiftyJSON/SwiftyJSON/pull/170) ([joelparkerhenderson](https://github.com/joelparkerhenderson))

- Fixed a crash when entering json\["NotExistPath"\] [\#167](https://github.com/SwiftyJSON/SwiftyJSON/pull/167) ([ybeapps](https://github.com/ybeapps))

- Adding Swift 1.2 support [\#158](https://github.com/SwiftyJSON/SwiftyJSON/pull/158) ([Jasdev](https://github.com/Jasdev))

- Fix issues with the OS X target and scheme [\#156](https://github.com/SwiftyJSON/SwiftyJSON/pull/156) ([rastersize](https://github.com/rastersize))

- Fix issues with the OS X target and scheme [\#155](https://github.com/SwiftyJSON/SwiftyJSON/pull/155) ([rastersize](https://github.com/rastersize))

- Remove SwiftJSON.xcodeproj/xcuserdata [\#154](https://github.com/SwiftyJSON/SwiftyJSON/pull/154) ([rastersize](https://github.com/rastersize))

- Change to not build test when building iOS framework target \[Xcode 6.3 + external tools\] [\#153](https://github.com/SwiftyJSON/SwiftyJSON/pull/153) ([rastersize](https://github.com/rastersize))

- Fix tests not building for 32-bit \[Xcode 6.3\] [\#152](https://github.com/SwiftyJSON/SwiftyJSON/pull/152) ([rastersize](https://github.com/rastersize))

- Swift 1.2 compatibility fixes [\#149](https://github.com/SwiftyJSON/SwiftyJSON/pull/149) ([darrarski](https://github.com/darrarski))

- \[README.md\] Setter for JSON array should use arrayObject not array  [\#146](https://github.com/SwiftyJSON/SwiftyJSON/pull/146) ([lwu](https://github.com/lwu))

- add missing ` in comments [\#142](https://github.com/SwiftyJSON/SwiftyJSON/pull/142) ([zhxnlai](https://github.com/zhxnlai))

- Fix README.md nested example [\#139](https://github.com/SwiftyJSON/SwiftyJSON/pull/139) ([watsonbox](https://github.com/watsonbox))

- Shared OSX Scheme. OSX target fixes. [\#138](https://github.com/SwiftyJSON/SwiftyJSON/pull/138) ([haveahennessy](https://github.com/haveahennessy))

- Casting to NSDictionary instead of \[String : AnyObject\] [\#137](https://github.com/SwiftyJSON/SwiftyJSON/pull/137) ([clwkct](https://github.com/clwkct))

- Update README.md [\#136](https://github.com/SwiftyJSON/SwiftyJSON/pull/136) ([esbenvb](https://github.com/esbenvb))

- Update README.md [\#135](https://github.com/SwiftyJSON/SwiftyJSON/pull/135) ([esbenvb](https://github.com/esbenvb))

- Fixed the broken Carthage OS X Support. [\#134](https://github.com/SwiftyJSON/SwiftyJSON/pull/134) ([remaerd](https://github.com/remaerd))

- Prefixed "SequenceType" extension with Module name [\#124](https://github.com/SwiftyJSON/SwiftyJSON/pull/124) ([ravero](https://github.com/ravero))

- Code cleaning [\#123](https://github.com/SwiftyJSON/SwiftyJSON/pull/123) ([wiruzx](https://github.com/wiruzx))

## [2.1.3](https://github.com/SwiftyJSON/SwiftyJSON/tree/2.1.3) (2015-01-10)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/2.1.2...2.1.3)

**Closed issues:**

- Cannot install using Carthage [\#122](https://github.com/SwiftyJSON/SwiftyJSON/issues/122)

- Use of unresolved identifier 'dataFromNetworking' [\#112](https://github.com/SwiftyJSON/SwiftyJSON/issues/112)

- I can't parse out the string like "{a:5}" [\#109](https://github.com/SwiftyJSON/SwiftyJSON/issues/109)

- Cocoapods integration [\#108](https://github.com/SwiftyJSON/SwiftyJSON/issues/108)

- Compile Error In Loop Array [\#107](https://github.com/SwiftyJSON/SwiftyJSON/issues/107)

- Support for Carthage [\#105](https://github.com/SwiftyJSON/SwiftyJSON/issues/105)

**Merged pull requests:**

- Minor grammar fixes to README [\#128](https://github.com/SwiftyJSON/SwiftyJSON/pull/128) ([johngoren](https://github.com/johngoren))

- Updated because podspec is now available... [\#127](https://github.com/SwiftyJSON/SwiftyJSON/pull/127) ([johngoren](https://github.com/johngoren))

- Fix access modificator of isEmpty property [\#121](https://github.com/SwiftyJSON/SwiftyJSON/pull/121) ([wiruzx](https://github.com/wiruzx))

- Make framework extension friendly [\#119](https://github.com/SwiftyJSON/SwiftyJSON/pull/119) ([technomage](https://github.com/technomage))

- add a new way to access Json [\#117](https://github.com/SwiftyJSON/SwiftyJSON/pull/117) ([zhanghao111111111](https://github.com/zhanghao111111111))

- fix the typos on the code snippets and the links in the TOC on README [\#115](https://github.com/SwiftyJSON/SwiftyJSON/pull/115) ([floydpink](https://github.com/floydpink))

- Use Mac' codesign identities for OSX targets [\#114](https://github.com/SwiftyJSON/SwiftyJSON/pull/114) ([max-potapov](https://github.com/max-potapov))

## [2.1.2](https://github.com/SwiftyJSON/SwiftyJSON/tree/2.1.2) (2014-12-13)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/2.1.1...2.1.2)

**Closed issues:**

- Why can't we parse a rawString back to json object? [\#101](https://github.com/SwiftyJSON/SwiftyJSON/issues/101)

- Have a Piece of Code that might be of Value to SwiftyJSon [\#97](https://github.com/SwiftyJSON/SwiftyJSON/issues/97)

- build osx application \(command line tool\) with swiftyjson error [\#96](https://github.com/SwiftyJSON/SwiftyJSON/issues/96)

- 这个应该是bug吧，支持的不是很够 [\#95](https://github.com/SwiftyJSON/SwiftyJSON/issues/95)

- Length of an array [\#90](https://github.com/SwiftyJSON/SwiftyJSON/issues/90)

- Compilation error [\#89](https://github.com/SwiftyJSON/SwiftyJSON/issues/89)

- Can't set value [\#88](https://github.com/SwiftyJSON/SwiftyJSON/issues/88)

- Examples with AFHTTPSessionManager? [\#86](https://github.com/SwiftyJSON/SwiftyJSON/issues/86)

**Merged pull requests:**

- Update README.md to add Carthage instructions [\#113](https://github.com/SwiftyJSON/SwiftyJSON/pull/113) ([justinmakaila](https://github.com/justinmakaila))

- Improve init performance for dictionaries and arrays [\#111](https://github.com/SwiftyJSON/SwiftyJSON/pull/111) ([avorobjov](https://github.com/avorobjov))

- Fix NSNumber != func [\#106](https://github.com/SwiftyJSON/SwiftyJSON/pull/106) ([briankracoff](https://github.com/briankracoff))

- Carthage Support [\#104](https://github.com/SwiftyJSON/SwiftyJSON/pull/104) ([justinmakaila](https://github.com/justinmakaila))

- Added read option for date strings [\#103](https://github.com/SwiftyJSON/SwiftyJSON/pull/103) ([Dschee](https://github.com/Dschee))

- Add Podspec \(Correct mblsha podspec\) [\#100](https://github.com/SwiftyJSON/SwiftyJSON/pull/100) ([ValCapri](https://github.com/ValCapri))

- Add podspec + make it work as a framework [\#99](https://github.com/SwiftyJSON/SwiftyJSON/pull/99) ([mblsha](https://github.com/mblsha))

- Adding new Feature: JsonMapper [\#98](https://github.com/SwiftyJSON/SwiftyJSON/pull/98) ([Drogenix](https://github.com/Drogenix))

- Change recommendation for Alamofire integration [\#92](https://github.com/SwiftyJSON/SwiftyJSON/pull/92) ([JonathanPorta](https://github.com/JonathanPorta))

## [2.1.1](https://github.com/SwiftyJSON/SwiftyJSON/tree/2.1.1) (2014-11-12)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/2.1.0...2.1.1)

**Closed issues:**

- NSDictionary to json string to json object [\#93](https://github.com/SwiftyJSON/SwiftyJSON/issues/93)

- Error: use of unresolved identifier dataFromNetworking [\#82](https://github.com/SwiftyJSON/SwiftyJSON/issues/82)

- Type \[SubscriptType\] Does not conform to protocol 'StringLiteralConvertible' [\#81](https://github.com/SwiftyJSON/SwiftyJSON/issues/81)

- Doesn't conform literal protocols [\#80](https://github.com/SwiftyJSON/SwiftyJSON/issues/80)

- iOS 8.1 compatability [\#79](https://github.com/SwiftyJSON/SwiftyJSON/issues/79)

- Xcode 6.1 Compatibility [\#78](https://github.com/SwiftyJSON/SwiftyJSON/issues/78)

- Problem with xCode 6.1 [\#76](https://github.com/SwiftyJSON/SwiftyJSON/issues/76)

- Compilation errors [\#75](https://github.com/SwiftyJSON/SwiftyJSON/issues/75)

**Merged pull requests:**

- Renamed type 'Unknow' to 'Unknown' [\#94](https://github.com/SwiftyJSON/SwiftyJSON/pull/94) ([franklsf95](https://github.com/franklsf95))

- Added OSX target. [\#91](https://github.com/SwiftyJSON/SwiftyJSON/pull/91) ([carloslozano](https://github.com/carloslozano))

- Add date ,dateValue [\#87](https://github.com/SwiftyJSON/SwiftyJSON/pull/87) ([muukii0803](https://github.com/muukii0803))

- Add parse JSON Date [\#85](https://github.com/SwiftyJSON/SwiftyJSON/pull/85) ([ShineWu](https://github.com/ShineWu))

- Update README.md [\#84](https://github.com/SwiftyJSON/SwiftyJSON/pull/84) ([johngoren](https://github.com/johngoren))

- Update README.md for typos [\#83](https://github.com/SwiftyJSON/SwiftyJSON/pull/83) ([johngoren](https://github.com/johngoren))

## [2.1.0](https://github.com/SwiftyJSON/SwiftyJSON/tree/2.1.0) (2014-10-19)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/2.0.0...2.1.0)

**Closed issues:**

- 32bit test failures [\#71](https://github.com/SwiftyJSON/SwiftyJSON/issues/71)

- Trouble getting string representation [\#70](https://github.com/SwiftyJSON/SwiftyJSON/issues/70)

- JSON keep null [\#69](https://github.com/SwiftyJSON/SwiftyJSON/issues/69)

- Update .pbxproj to Deployment Target 8.0 [\#66](https://github.com/SwiftyJSON/SwiftyJSON/issues/66)

- Looping not working [\#64](https://github.com/SwiftyJSON/SwiftyJSON/issues/64)

**Merged pull requests:**

- Get number from string in JSON.number [\#74](https://github.com/SwiftyJSON/SwiftyJSON/pull/74) ([yonaskolb](https://github.com/yonaskolb))

- Update SwiftyJSON.swift [\#73](https://github.com/SwiftyJSON/SwiftyJSON/pull/73) ([MaddTheSane](https://github.com/MaddTheSane))

- Generating Raw JSON Strings [\#72](https://github.com/SwiftyJSON/SwiftyJSON/pull/72) ([lesmuc](https://github.com/lesmuc))

- Making SourceKit not freak out about self.object.count being called [\#68](https://github.com/SwiftyJSON/SwiftyJSON/pull/68) ([Noobish1](https://github.com/Noobish1))

- Added support for Xcode 6.1 GM Seed 2. [\#65](https://github.com/SwiftyJSON/SwiftyJSON/pull/65) ([rosskimes](https://github.com/rosskimes))

## [2.0.0](https://github.com/SwiftyJSON/SwiftyJSON/tree/2.0.0) (2014-10-08)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/1.1.0...2.0.0)

**Closed issues:**

- JSON to NSData [\#62](https://github.com/SwiftyJSON/SwiftyJSON/issues/62)

- Updating a json [\#60](https://github.com/SwiftyJSON/SwiftyJSON/issues/60)

**Merged pull requests:**

- Update for new features \[Issue \#60\] [\#63](https://github.com/SwiftyJSON/SwiftyJSON/pull/63) ([tangplin](https://github.com/tangplin))

## [1.1.0](https://github.com/SwiftyJSON/SwiftyJSON/tree/1.1.0) (2014-10-02)

[Full Changelog](https://github.com/SwiftyJSON/SwiftyJSON/compare/1.0.0...1.1.0)

**Closed issues:**

- Long time to parse this json [\#57](https://github.com/SwiftyJSON/SwiftyJSON/issues/57)

**Merged pull requests:**

- Merge develop [\#59](https://github.com/SwiftyJSON/SwiftyJSON/pull/59) ([tangplin](https://github.com/tangplin))

- Added SwiftyJSON lazy wrapping [\#58](https://github.com/SwiftyJSON/SwiftyJSON/pull/58) ([k06a](https://github.com/k06a))

## [1.0.0](https://github.com/SwiftyJSON/SwiftyJSON/tree/1.0.0) (2014-09-26)

**Implemented enhancements:**

- JNumber should be Number not double [\#8](https://github.com/SwiftyJSON/SwiftyJSON/issues/8)

- Separate implementations of protocols [\#5](https://github.com/SwiftyJSON/SwiftyJSON/issues/5)

**Fixed bugs:**

- JNumber should be Number not double [\#8](https://github.com/SwiftyJSON/SwiftyJSON/issues/8)

- Fails to compile on Beta2 [\#1](https://github.com/SwiftyJSON/SwiftyJSON/issues/1)

**Closed issues:**

- No such module "SwiftyJSON" [\#49](https://github.com/SwiftyJSON/SwiftyJSON/issues/49)

- how to transfer JSONValue object to Dictionary object?  [\#48](https://github.com/SwiftyJSON/SwiftyJSON/issues/48)

- JSONValue in @objc [\#47](https://github.com/SwiftyJSON/SwiftyJSON/issues/47)

- SwiftyJSON.swift:331:22: Use of undeclared type 'BooleanType' [\#46](https://github.com/SwiftyJSON/SwiftyJSON/issues/46)

- Problem converting JSONValue to AnyObject [\#44](https://github.com/SwiftyJSON/SwiftyJSON/issues/44)

- Can't use SwiftyJSON as part of a public API within a framework [\#42](https://github.com/SwiftyJSON/SwiftyJSON/issues/42)

- how to add JSONValue object into exist JSONValue [\#40](https://github.com/SwiftyJSON/SwiftyJSON/issues/40)

- Doesn't work in BETA 6 [\#39](https://github.com/SwiftyJSON/SwiftyJSON/issues/39)

- Can't access property [\#38](https://github.com/SwiftyJSON/SwiftyJSON/issues/38)

- Couldn't Compile and Run  [\#37](https://github.com/SwiftyJSON/SwiftyJSON/issues/37)

- Array index out of range [\#35](https://github.com/SwiftyJSON/SwiftyJSON/issues/35)

- Iterating through a JSON response [\#32](https://github.com/SwiftyJSON/SwiftyJSON/issues/32)

- NSNull in an array is discarded [\#25](https://github.com/SwiftyJSON/SwiftyJSON/issues/25)

- Updating Dictionary [\#24](https://github.com/SwiftyJSON/SwiftyJSON/issues/24)

- SourcekitService Terminated Issue [\#22](https://github.com/SwiftyJSON/SwiftyJSON/issues/22)

- Code does not compile in iOS 8 Beta 3. [\#17](https://github.com/SwiftyJSON/SwiftyJSON/issues/17)

- How to use .count [\#14](https://github.com/SwiftyJSON/SwiftyJSON/issues/14)

- Add to cocoapods [\#12](https://github.com/SwiftyJSON/SwiftyJSON/issues/12)

- String parsing [\#9](https://github.com/SwiftyJSON/SwiftyJSON/issues/9)

- Cocoapods integration [\#4](https://github.com/SwiftyJSON/SwiftyJSON/issues/4)

- How do I verify SwiftyJSON workS? [\#2](https://github.com/SwiftyJSON/SwiftyJSON/issues/2)

**Merged pull requests:**

- Revert "Added rawObject method for unwrapping JSONValue enum to objects" [\#56](https://github.com/SwiftyJSON/SwiftyJSON/pull/56) ([tangplin](https://github.com/tangplin))

- set the default JSONReadingOptions to .AllowFragments [\#55](https://github.com/SwiftyJSON/SwiftyJSON/pull/55) ([tangplin](https://github.com/tangplin))

- Fix Unit Test [\#54](https://github.com/SwiftyJSON/SwiftyJSON/pull/54) ([lingoer](https://github.com/lingoer))

- Add NSError to Null type [\#53](https://github.com/SwiftyJSON/SwiftyJSON/pull/53) ([lingoer](https://github.com/lingoer))

- Refactor! [\#51](https://github.com/SwiftyJSON/SwiftyJSON/pull/51) ([tangplin](https://github.com/tangplin))

- Rename LISCENSE to LICENSE [\#50](https://github.com/SwiftyJSON/SwiftyJSON/pull/50) ([fixe](https://github.com/fixe))

- Added rawObject method for unwrapping JSONValue enum to objects [\#45](https://github.com/SwiftyJSON/SwiftyJSON/pull/45) ([k06a](https://github.com/k06a))

- made JSONValue public for usage in framework APIs [\#43](https://github.com/SwiftyJSON/SwiftyJSON/pull/43) ([Dschee](https://github.com/Dschee))

- Adding public/private modifiers so that SwiftyJSON can be used as a framework [\#41](https://github.com/SwiftyJSON/SwiftyJSON/pull/41) ([jansabbe](https://github.com/jansabbe))

- Rename LISCENSE to LICENSE [\#36](https://github.com/SwiftyJSON/SwiftyJSON/pull/36) ([kriswallsmith](https://github.com/kriswallsmith))

- Fix for Xcode 6 beta 5 changes [\#34](https://github.com/SwiftyJSON/SwiftyJSON/pull/34) ([FahimF](https://github.com/FahimF))

- Use BooleanType instead of LogicValue for Beta 5 [\#33](https://github.com/SwiftyJSON/SwiftyJSON/pull/33) ([venables](https://github.com/venables))

- Support for JSON as string [\#31](https://github.com/SwiftyJSON/SwiftyJSON/pull/31) ([bsvingen](https://github.com/bsvingen))

- Support building JSON messages in code. [\#30](https://github.com/SwiftyJSON/SwiftyJSON/pull/30) ([johnno1962](https://github.com/johnno1962))

- Update project to include access modifiers from Xcode beta 4. [\#29](https://github.com/SwiftyJSON/SwiftyJSON/pull/29) ([Baltoli](https://github.com/Baltoli))

- jsonvalue now conforms to sequence protocol for array values [\#28](https://github.com/SwiftyJSON/SwiftyJSON/pull/28) ([NatashaTheRobot](https://github.com/NatashaTheRobot))

- updated array and dictionary syntax for beta3 [\#27](https://github.com/SwiftyJSON/SwiftyJSON/pull/27) ([NatashaTheRobot](https://github.com/NatashaTheRobot))

- Update for Beta 4: exposing JSONValue with public [\#26](https://github.com/SwiftyJSON/SwiftyJSON/pull/26) ([NachoSoto](https://github.com/NachoSoto))

- SourceKitService Termination issue fix [\#23](https://github.com/SwiftyJSON/SwiftyJSON/pull/23) ([ipraba](https://github.com/ipraba))

- Add percent escaping to URL string [\#21](https://github.com/SwiftyJSON/SwiftyJSON/pull/21) ([romanroibu](https://github.com/romanroibu))

- Converting JSON objects to string is fixed [\#20](https://github.com/SwiftyJSON/SwiftyJSON/pull/20) ([bkase](https://github.com/bkase))

- JSONValue can be inited via string [\#19](https://github.com/SwiftyJSON/SwiftyJSON/pull/19) ([bkase](https://github.com/bkase))

- Updated to remove errors in Xcode Beta 3 [\#18](https://github.com/SwiftyJSON/SwiftyJSON/pull/18) ([krishpop](https://github.com/krishpop))

- add first and last in JSONValue, add string to double, int etc. [\#16](https://github.com/SwiftyJSON/SwiftyJSON/pull/16) ([tangplin](https://github.com/tangplin))

- add a "url" property to JSONValue [\#15](https://github.com/SwiftyJSON/SwiftyJSON/pull/15) ([kyoh](https://github.com/kyoh))

- Some typo fixes [\#13](https://github.com/SwiftyJSON/SwiftyJSON/pull/13) ([gregbarbosa](https://github.com/gregbarbosa))

- Separate protocols implementation, refactor prettyString composing [\#6](https://github.com/SwiftyJSON/SwiftyJSON/pull/6) ([garnett](https://github.com/garnett))

- Add project with both OSX/iOS module targets [\#3](https://github.com/SwiftyJSON/SwiftyJSON/pull/3) ([garnett](https://github.com/garnett))



\* *This Change Log was automatically generated by [github_changelog_generator](https://github.com/skywinder/Github-Changelog-Generator)*