<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="11201" systemVersion="16A319" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="UBk-MQ-XCB">
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="11161"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="HelveticaNeue.ttc">
            <string>HelveticaNeue</string>
        </array>
    </customFonts>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="47d-Pt-u8a">
            <objects>
                <navigationController id="UBk-MQ-XCB" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" id="lvE-eD-FyM">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="yoh-b6-vfD" kind="relationship" relationship="rootViewController" id="o6q-xz-i46"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="haX-6x-iNc" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-103" y="-17"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="DTx-dr-ptp">
            <objects>
                <tableViewController id="yoh-b6-vfD" customClass="ViewController" customModule="Example" customModuleProvider="target" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" sectionIndexMinimumDisplayRowCount="9999" rowHeight="44" sectionHeaderHeight="22" sectionFooterHeight="22" id="NOM-19-CHr">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <prototypes>
                            <tableViewCell contentMode="scaleToFill" selectionStyle="default" accessoryType="disclosureIndicator" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="JSONCell" textLabel="hWf-y9-WjD" detailTextLabel="52W-qf-qmY" style="IBUITableViewCellStyleValue1" id="OnB-ua-KwN">
                                <rect key="frame" x="0.0" y="86" width="375" height="44"/>
                                <autoresizingMask key="autoresizingMask"/>
                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="OnB-ua-KwN" id="dt7-WG-Czg">
                                    <frame key="frameInset" width="342" height="44"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                    <subviews>
                                        <label opaque="NO" multipleTouchEnabled="YES" contentMode="left" text=" " lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="hWf-y9-WjD">
                                            <frame key="frameInset" minX="15" minY="13" width="4.5" height="19.5"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                            <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" multipleTouchEnabled="YES" contentMode="left" text=" " textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="52W-qf-qmY">
                                            <frame key="frameInset" minX="336" minY="15" width="4" height="16.5"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                            <color key="textColor" red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </tableViewCellContentView>
                                <connections>
                                    <segue destination="yoh-b6-vfD" kind="show" id="9x2-N4-tQp"/>
                                </connections>
                            </tableViewCell>
                        </prototypes>
                        <sections/>
                        <connections>
                            <outlet property="dataSource" destination="yoh-b6-vfD" id="Xly-yf-8xp"/>
                            <outlet property="delegate" destination="yoh-b6-vfD" id="Zq2-0k-TIX"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" id="cah-Bi-nki">
                        <nil key="title"/>
                    </navigationItem>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="eXg-Bd-TT0" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1092" y="-18"/>
        </scene>
    </scenes>
    <inferredMetricsTieBreakers>
        <segue reference="o6q-xz-i46"/>
    </inferredMetricsTieBreakers>
</document>
