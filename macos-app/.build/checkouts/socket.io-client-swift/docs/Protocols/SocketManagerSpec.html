<!DOCTYPE html>
<html lang="en">
  <head>
    <title>SocketManagerSpec Protocol Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Protocol/SocketManagerSpec" class="dashAnchor"></a>

    <a title="SocketManagerSpec Protocol Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          SocketIO 16.0.0 Docs
        </a>
         (100% documented)
      </p>
    
      <p class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </p>
    
    
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">SocketIO Reference</a>
      <img class="carat" src="../img/carat.png" />
      SocketManagerSpec Protocol Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Guides.html">Guides</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../12to13.html">12to13</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../15to16.html">15to16</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../faq.html">FAQ</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/OnAckCallback.html">OnAckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAckEmitter.html">SocketAckEmitter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAnyEvent.html">SocketAnyEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketEngine.html">SocketEngine</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketIOClient.html">SocketIOClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketManager.html">SocketManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawAckView.html">SocketRawAckView</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawView.html">SocketRawView</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketAckStatus.html">SocketAckStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketClientEvent.html">SocketClientEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketEnginePacketType.html">SocketEnginePacketType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOClientOption.html">SocketIOClientOption</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOStatus.html">SocketIOStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOVersion.html">SocketIOVersion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketParsableError.html">SocketParsableError</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sb">Bool</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation4DataV">Data</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SD">Dictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sd">Double</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Si">Int</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSArray">NSArray</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSData">NSData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSDictionary">NSDictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSNull">NSNull</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSString">NSString</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SS">String</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ConfigSettable.html">ConfigSettable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketData.html">SocketData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketDataBufferable.html">SocketDataBufferable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineClient.html">SocketEngineClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEnginePollable.html">SocketEnginePollable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineWebsocket.html">SocketEngineWebsocket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketIOClientSpec.html">SocketIOClientSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketLogger.html">SocketLogger</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketManagerSpec.html">SocketManagerSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketParsable.html">SocketParsable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketEventHandler.html">SocketEventHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketIOClientConfiguration.html">SocketIOClientConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket.html">SocketPacket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket/PacketType.html">– PacketType</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO11AckCallbacka">AckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO4Posta">Post</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>SocketManagerSpec</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">protocol</span> <span class="kt">SocketManagerSpec</span> <span class="p">:</span> <span class="kt">AnyObject</span><span class="p">,</span> <span class="kt"><a href="../Protocols/SocketEngineClient.html">SocketEngineClient</a></span></code></pre>

                </div>
              </div>
            <p>A manager for a socket.io connection.</p>

<p>A <code>SocketManagerSpec</code> is responsible for multiplexing multiple namespaces through a single <code><a href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a></code>.</p>

<p>Example with <code><a href="../Classes/SocketManager.html">SocketManager</a></code>:</p>
<pre class="highlight swift"><code><span class="k">let</span> <span class="nv">manager</span> <span class="o">=</span> <span class="kt">SocketManager</span><span class="p">(</span><span class="nv">socketURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">(</span><span class="nv">string</span><span class="p">:</span><span class="s">"http://localhost:8080/"</span><span class="p">)</span><span class="o">!</span><span class="p">)</span>
<span class="k">let</span> <span class="nv">defaultNamespaceSocket</span> <span class="o">=</span> <span class="n">manager</span><span class="o">.</span><span class="n">defaultSocket</span>
<span class="k">let</span> <span class="nv">swiftSocket</span> <span class="o">=</span> <span class="n">manager</span><span class="o">.</span><span class="nf">socket</span><span class="p">(</span><span class="nv">forNamespace</span><span class="p">:</span> <span class="s">"/swift"</span><span class="p">)</span>

<span class="c1">// defaultNamespaceSocket and swiftSocket both share a single connection to the server</span>
</code></pre>

<p>Sockets created through the manager are retained by the manager. So at the very least, a single strong reference
to the manager must be maintained to keep sockets alive.</p>

<p>To disconnect a socket and remove it from the manager, either call <code><a href="../Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC10disconnectyyF">SocketIOClient.disconnect()</a></code> on the socket,
or call one of the <code>disconnectSocket</code> methods on this class.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Properties"></a>
                <a name="//apple_ref/swift/Section/Properties" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Properties"></a>
                  <h3 class="section-name"><p>Properties</p>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP07defaultA0AA0A8IOClientCvp"></a>
                    <a name="//apple_ref/swift/Property/defaultSocket" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP07defaultA0AA0A8IOClientCvp">defaultSocket</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns the socket associated with the default namespace (&ldquo;/&rdquo;).</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">defaultSocket</span><span class="p">:</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP6engineAA0a6EngineD0_pSgvp"></a>
                    <a name="//apple_ref/swift/Property/engine" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP6engineAA0a6EngineD0_pSgvp">engine</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The engine for this manager.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">engine</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a></span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP8forceNewSbvp"></a>
                    <a name="//apple_ref/swift/Property/forceNew" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP8forceNewSbvp">forceNew</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>If <code>true</code> then every time <code>connect</code> is called, a new engine will be created.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">forceNew</span><span class="p">:</span> <span class="kt">Bool</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP11handleQueueSo17OS_dispatch_queueCvp"></a>
                    <a name="//apple_ref/swift/Property/handleQueue" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP11handleQueueSo17OS_dispatch_queueCvp">handleQueue</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The queue that all interaction with the client should occur on. This is the queue that event handlers are
called on.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">handleQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP4nspsSDySSAA0A8IOClientCGvp"></a>
                    <a name="//apple_ref/swift/Property/nsps" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP4nspsSDySSAA0A8IOClientCGvp">nsps</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The sockets in this manager indexed by namespace.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">nsps</span><span class="p">:</span> <span class="p">[</span><span class="kt">String</span> <span class="p">:</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP10reconnectsSbvp"></a>
                    <a name="//apple_ref/swift/Property/reconnects" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP10reconnectsSbvp">reconnects</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>If <code>true</code>, this manager will try and reconnect on any disconnects.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">reconnects</span><span class="p">:</span> <span class="kt">Bool</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP13reconnectWaitSivp"></a>
                    <a name="//apple_ref/swift/Property/reconnectWait" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP13reconnectWaitSivp">reconnectWait</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The minimum number of seconds to wait before attempting to reconnect.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">reconnectWait</span><span class="p">:</span> <span class="kt">Int</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP16reconnectWaitMaxSivp"></a>
                    <a name="//apple_ref/swift/Property/reconnectWaitMax" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP16reconnectWaitMaxSivp">reconnectWaitMax</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The maximum number of seconds to wait before attempting to reconnect.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">reconnectWaitMax</span><span class="p">:</span> <span class="kt">Int</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP19randomizationFactorSdvp"></a>
                    <a name="//apple_ref/swift/Property/randomizationFactor" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP19randomizationFactorSdvp">randomizationFactor</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The randomization factor for calculating reconnect jitter.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">randomizationFactor</span><span class="p">:</span> <span class="kt">Double</span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP9socketURL10Foundation0F0Vvp"></a>
                    <a name="//apple_ref/swift/Property/socketURL" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP9socketURL10Foundation0F0Vvp">socketURL</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The URL of the socket.io server.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">socketURL</span><span class="p">:</span> <span class="kt">URL</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP6statusAA0A8IOStatusOvp"></a>
                    <a name="//apple_ref/swift/Property/status" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP6statusAA0A8IOStatusOvp">status</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The status of this manager.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">status</span><span class="p">:</span> <span class="kt"><a href="../Enums/SocketIOStatus.html">SocketIOStatus</a></span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP7versionAA0A9IOVersionOvp"></a>
                    <a name="//apple_ref/swift/Property/version" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP7versionAA0A9IOVersionOvp">version</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The version of socket.io in use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">version</span><span class="p">:</span> <span class="kt"><a href="../Enums/SocketIOVersion.html">SocketIOVersion</a></span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Methods"></a>
                <a name="//apple_ref/swift/Section/Methods" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Methods"></a>
                  <h3 class="section-name"><p>Methods</p>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP7connectyyF"></a>
                    <a name="//apple_ref/swift/Method/connect()" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP7connectyyF">connect()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Connects the underlying transport.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">connect</span><span class="p">()</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP07connectA0_11withPayloadyAA0A8IOClientC_SDySSypGSgtF"></a>
                    <a name="//apple_ref/swift/Method/connectSocket(_:withPayload:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP07connectA0_11withPayloadyAA0A8IOClientC_SDySSypGSgtF">connectSocket(_:<wbr>withPayload:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Connects a socket through this manager&rsquo;s engine.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">connectSocket</span><span class="p">(</span><span class="n">_</span> <span class="nv">socket</span><span class="p">:</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span><span class="p">,</span> <span class="nv">withPayload</span><span class="p">:</span> <span class="p">[</span><span class="kt">String</span> <span class="p">:</span> <span class="kt">Any</span><span class="p">]?)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>socket</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The socket who we should connect through this manager.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>withPayload</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Optional payload to send on connect</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP13didDisconnect6reasonySS_tF"></a>
                    <a name="//apple_ref/swift/Method/didDisconnect(reason:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP13didDisconnect6reasonySS_tF">didDisconnect(reason:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Called when the manager has disconnected from socket.io.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">didDisconnect</span><span class="p">(</span><span class="nv">reason</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>reason</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The reason for the disconnection.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP10disconnectyyF"></a>
                    <a name="//apple_ref/swift/Method/disconnect()" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP10disconnectyyF">disconnect()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Disconnects the manager and all associated sockets.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">disconnect</span><span class="p">()</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP010disconnectA0yyAA0A8IOClientCF"></a>
                    <a name="//apple_ref/swift/Method/disconnectSocket(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP010disconnectA0yyAA0A8IOClientCF">disconnectSocket(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Disconnects the given socket.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">disconnectSocket</span><span class="p">(</span><span class="n">_</span> <span class="nv">socket</span><span class="p">:</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>socket</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The socket to disconnect.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP010disconnectA012forNamespaceySS_tF"></a>
                    <a name="//apple_ref/swift/Method/disconnectSocket(forNamespace:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP010disconnectA012forNamespaceySS_tF">disconnectSocket(forNamespace:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Disconnects the socket associated with <code>forNamespace</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">disconnectSocket</span><span class="p">(</span><span class="n">forNamespace</span> <span class="nv">nsp</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>nsp</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The namespace to disconnect from.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP7emitAllyySS_AA0A4Data_pdtF"></a>
                    <a name="//apple_ref/swift/Method/emitAll(_:_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP7emitAllyySS_AA0A4Data_pdtF">emitAll(_:<wbr>_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sends an event to the server on all namespaces in this manager.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">emitAll</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="n">_</span> <span class="nv">items</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketData.html">SocketData</a></span><span class="o">...</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to send.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>items</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The data to send with this event.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP9reconnectyyF"></a>
                    <a name="//apple_ref/swift/Method/reconnect()" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP9reconnectyyF">reconnect()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Tries to reconnect to the server.</p>

<p>This will cause a <code>disconnect</code> event to be emitted, as well as an <code>reconnectAttempt</code> event.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">reconnect</span><span class="p">()</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP06removeA0yAA0A8IOClientCSgAFF"></a>
                    <a name="//apple_ref/swift/Method/removeSocket(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP06removeA0yAA0A8IOClientCSgAFF">removeSocket(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Removes the socket from the manager&rsquo;s control.
After calling this method the socket should no longer be considered usable.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">func</span> <span class="nf">removeSocket</span><span class="p">(</span><span class="n">_</span> <span class="nv">socket</span><span class="p">:</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span><span class="p">?</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>socket</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The socket to remove.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The socket removed, if it was owned by the manager.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A11ManagerSpecP6socket12forNamespaceAA0A8IOClientCSS_tF"></a>
                    <a name="//apple_ref/swift/Method/socket(forNamespace:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A11ManagerSpecP6socket12forNamespaceAA0A8IOClientCSS_tF">socket(forNamespace:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns a <code><a href="../Classes/SocketIOClient.html">SocketIOClient</a></code> for the given namespace. This socket shares a transport with the manager.</p>

<p>Calling multiple times returns the same socket.</p>

<p>Sockets created from this method are retained by the manager.
Call one of the <code>disconnectSocket</code> methods on the implementing class to remove the socket from manager control.
Or call <code><a href="../Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC10disconnectyyF">SocketIOClient.disconnect()</a></code> on the client.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">func</span> <span class="nf">socket</span><span class="p">(</span><span class="n">forNamespace</span> <span class="nv">nsp</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>nsp</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The namespace for the socket.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>A <code><a href="../Classes/SocketIOClient.html">SocketIOClient</a></code> for the given namespace.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2021 <a class="link" href="https://github.com/socketio/socket.io-client-swift" target="_blank" rel="external">Erik</a>. All rights reserved. (Last updated: 2021-02-01)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external">jazzy ♪♫ v0.13.6</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external">Realm</a> project.</p>
    </section>
  </body>
</div>
</html>
