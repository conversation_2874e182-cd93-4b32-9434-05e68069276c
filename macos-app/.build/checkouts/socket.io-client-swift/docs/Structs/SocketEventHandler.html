<!DOCTYPE html>
<html lang="en">
  <head>
    <title>SocketEventHandler Structure Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Struct/SocketEventHandler" class="dashAnchor"></a>

    <a title="SocketEventHandler Structure Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          SocketIO 16.0.0 Docs
        </a>
         (100% documented)
      </p>
    
      <p class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </p>
    
    
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">SocketIO Reference</a>
      <img class="carat" src="../img/carat.png" />
      SocketEventHandler Structure Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Guides.html">Guides</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../12to13.html">12to13</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../15to16.html">15to16</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../faq.html">FAQ</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/OnAckCallback.html">OnAckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAckEmitter.html">SocketAckEmitter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAnyEvent.html">SocketAnyEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketEngine.html">SocketEngine</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketIOClient.html">SocketIOClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketManager.html">SocketManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawAckView.html">SocketRawAckView</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawView.html">SocketRawView</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketAckStatus.html">SocketAckStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketClientEvent.html">SocketClientEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketEnginePacketType.html">SocketEnginePacketType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOClientOption.html">SocketIOClientOption</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOStatus.html">SocketIOStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOVersion.html">SocketIOVersion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketParsableError.html">SocketParsableError</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sb">Bool</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation4DataV">Data</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SD">Dictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sd">Double</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Si">Int</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSArray">NSArray</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSData">NSData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSDictionary">NSDictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSNull">NSNull</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSString">NSString</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SS">String</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ConfigSettable.html">ConfigSettable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketData.html">SocketData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketDataBufferable.html">SocketDataBufferable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineClient.html">SocketEngineClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEnginePollable.html">SocketEnginePollable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineWebsocket.html">SocketEngineWebsocket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketIOClientSpec.html">SocketIOClientSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketLogger.html">SocketLogger</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketManagerSpec.html">SocketManagerSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketParsable.html">SocketParsable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketEventHandler.html">SocketEventHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketIOClientConfiguration.html">SocketIOClientConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket.html">SocketPacket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket/PacketType.html">– PacketType</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO11AckCallbacka">AckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO4Posta">Post</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>SocketEventHandler</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">SocketEventHandler</span></code></pre>

                </div>
              </div>
            <p>A wrapper around a handler.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Properties"></a>
                <a name="//apple_ref/swift/Section/Properties" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Properties"></a>
                  <h3 class="section-name"><p>Properties</p>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A12EventHandlerV5eventSSvp"></a>
                    <a name="//apple_ref/swift/Property/event" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A12EventHandlerV5eventSSvp">event</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The event for this handler.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A12EventHandlerV2id10Foundation4UUIDVvp"></a>
                    <a name="//apple_ref/swift/Property/id" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A12EventHandlerV2id10Foundation4UUIDVvp">id</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A unique identifier for this handler.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">id</span><span class="p">:</span> <span class="kt">UUID</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A12EventHandlerV8callbackyySayypG_AA0A10AckEmitterCtcvp"></a>
                    <a name="//apple_ref/swift/Property/callback" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A12EventHandlerV8callbackyySayypG_AA0A10AckEmitterCtcvp">callback</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The actual handler function.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">callback</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Methods"></a>
                <a name="//apple_ref/swift/Section/Methods" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Methods"></a>
                  <h3 class="section-name"><p>Methods</p>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A12EventHandlerV15executeCallback4with0G3Ack0gA0ySayypG_SiAA0A8IOClientCtF"></a>
                    <a name="//apple_ref/swift/Method/executeCallback(with:withAck:withSocket:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A12EventHandlerV15executeCallback4with0G3Ack0gA0ySayypG_SiAA0A8IOClientCtF">executeCallback(with:<wbr>withAck:<wbr>withSocket:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Causes this handler to be executed.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">executeCallback</span><span class="p">(</span><span class="n">with</span> <span class="nv">items</span><span class="p">:</span> <span class="p">[</span><span class="kt">Any</span><span class="p">],</span> <span class="n">withAck</span> <span class="nv">ack</span><span class="p">:</span> <span class="kt">Int</span><span class="p">,</span> <span class="n">withSocket</span> <span class="nv">socket</span><span class="p">:</span> <span class="kt"><a href="../Classes/SocketIOClient.html">SocketIOClient</a></span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>with</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The data that this handler should be called with.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>withAck</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The ack number that this event expects. Pass -1 to say this event doesn&rsquo;t expect an ack.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>withSocket</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The socket that is calling this event.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2021 <a class="link" href="https://github.com/socketio/socket.io-client-swift" target="_blank" rel="external">Erik</a>. All rights reserved. (Last updated: 2021-02-01)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external">jazzy ♪♫ v0.13.6</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external">Realm</a> project.</p>
    </section>
  </body>
</div>
</html>
