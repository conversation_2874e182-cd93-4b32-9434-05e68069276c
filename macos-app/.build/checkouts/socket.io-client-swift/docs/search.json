{"Typealiases.html#/s:8SocketIO11AckCallbacka": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": "<p>A typealias for an ack callback.</p>"}, "Typealiases.html#/s:8SocketIO14NormalCallbacka": {"name": "NormalCallback", "abstract": "<p>A typealias for a normal callback.</p>"}, "Typealiases.html#/s:8SocketIO4Posta": {"name": "Post", "abstract": "<p>A typealias for a queued POST</p>"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO7connectyA2EmF": {"name": "connect", "abstract": "<p>Connect: 0</p>", "parent_name": "PacketType"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO10disconnectyA2EmF": {"name": "disconnect", "abstract": "<p>Disconnect: 1</p>", "parent_name": "PacketType"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO5eventyA2EmF": {"name": "event", "abstract": "<p>Event: 2</p>", "parent_name": "PacketType"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO3ackyA2EmF": {"name": "ack", "abstract": "<p>Ack: 3</p>", "parent_name": "PacketType"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO5erroryA2EmF": {"name": "error", "abstract": "<p>Error: 4</p>", "parent_name": "PacketType"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO11binaryEventyA2EmF": {"name": "binaryEvent", "abstract": "<p>Binary Event: 5</p>", "parent_name": "PacketType"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO9binaryAckyA2EmF": {"name": "binaryAck", "abstract": "<p>Binary Ack: 6</p>", "parent_name": "PacketType"}, "Structs/SocketPacket/PacketType.html#/s:8SocketIO0A6PacketV0C4TypeO8isBinarySbvp": {"name": "isBinary", "abstract": "<p>Whether or not this type is binary</p>", "parent_name": "PacketType"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV3nspSSvp": {"name": "nsp", "abstract": "<p>The namespace for this packet.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV2idSivp": {"name": "id", "abstract": "<p>If &gt; 0 then this packet is using acking.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV4typeAC0C4TypeOvp": {"name": "type", "abstract": "<p>The type of this packet.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV6binarySay10Foundation4DataVGvp": {"name": "binary", "abstract": "<p>An array of binary data for this packet.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV4dataSayypGvp": {"name": "data", "abstract": "<p>The data for this event.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV4argsSayypGvp": {"name": "args", "abstract": "<p>Returns the payload for this packet, minus the event name if this is an event or binaryEvent type packet.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV11descriptionSSvp": {"name": "description", "abstract": "<p>A string representation of this packet.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV5eventSSvp": {"name": "event", "abstract": "<p>The event name for this packet.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket.html#/s:8SocketIO0A6PacketV12packetStringSSvp": {"name": "packetString", "abstract": "<p>A string representation of this packet.</p>", "parent_name": "SocketPacket"}, "Structs/SocketPacket/PacketType.html": {"name": "PacketType", "abstract": "<p>The type of packets.</p>", "parent_name": "SocketPacket"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV7Elementa": {"name": "Element", "abstract": "<p>Type of element stored.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV5Indexa": {"name": "Index", "abstract": "<p>Index type.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV8Iteratora": {"name": "Iterator", "abstract": "<p>Iterator type.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV11SubSequencea": {"name": "SubSequence", "abstract": "<p>SubSequence type.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV10startIndexSivp": {"name": "startIndex", "abstract": "<p>The start index of this collection.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV8endIndexSivp": {"name": "endIndex", "abstract": "<p>The end index of this collection.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV7isEmptySbvp": {"name": "isEmpty", "abstract": "<p>Whether this collection is empty.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV5countSivp": {"name": "count", "abstract": "<p>The number of elements stored in this collection.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV5firstAA0aC6OptionOSgvp": {"name": "first", "abstract": "<p>The first element in this collection.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:Sly7ElementQz5IndexQzcip": {"name": "subscript(_:)", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:Sly11SubSequenceQzSny5IndexQzGcip": {"name": "subscript(_:)", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV12arrayLiteralAcA0aC6OptionOd_tcfc": {"name": "init(arrayLiteral:)", "abstract": "<p>Creates a new <code>SocketIOClientConfiguration</code> from an array literal.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV12makeIterators08IndexingF0VySayAA0aC6OptionOGGyF": {"name": "makeIterator()", "abstract": "<p>Creates an iterator for this collection.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV5index5afterS2i_tF": {"name": "index(after:)", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketIOClientConfiguration.html#/s:8SocketIO0A21IOClientConfigurationV6insert_9replacingyAA0aC6OptionO_SbtF": {"name": "insert(_:replacing:)", "abstract": "<p>Special method that inserts <code>element</code> into the collection, replacing any other instances of <code>element</code>.</p>", "parent_name": "SocketIOClientConfiguration"}, "Structs/SocketEventHandler.html#/s:8SocketIO0A12EventHandlerV5eventSSvp": {"name": "event", "abstract": "<p>The event for this handler.</p>", "parent_name": "SocketEventHandler"}, "Structs/SocketEventHandler.html#/s:8SocketIO0A12EventHandlerV2id10Foundation4UUIDVvp": {"name": "id", "abstract": "<p>A unique identifier for this handler.</p>", "parent_name": "SocketEventHandler"}, "Structs/SocketEventHandler.html#/s:8SocketIO0A12EventHandlerV8callbackyySayypG_AA0A10AckEmitterCtcvp": {"name": "callback", "abstract": "<p>The actual handler function.</p>", "parent_name": "SocketEventHandler"}, "Structs/SocketEventHandler.html#/s:8SocketIO0A12EventHandlerV15executeCallback4with0G3Ack0gA0ySayypG_SiAA0A8IOClientCtF": {"name": "executeCallback(with:withAck:withSocket:)", "abstract": "<p>Causes this handler to be executed.</p>", "parent_name": "SocketEventHandler"}, "Structs/SocketEventHandler.html": {"name": "SocketEventHandler", "abstract": "<p>A wrapper around a handler.</p>"}, "Structs/SocketIOClientConfiguration.html": {"name": "SocketIOClientConfiguration", "abstract": "<p>An array-like type that holds <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/SocketIOClientOption.html\">SocketIOClientOption</a></code>s</p>"}, "Structs/SocketPacket.html": {"name": "SocketPacket", "abstract": "<p>A struct that represents a socket.io packet.</p>"}, "Protocols/SocketData.html#/s:8SocketIO0A4DataP20socketRepresentationAaB_pyKF": {"name": "socketRepresentation()", "abstract": "<p>A representation of self that can sent over socket.io.</p>", "parent_name": "SocketData"}, "Protocols/SocketLogger.html#/s:8SocketIO0A6LoggerP3logSbvp": {"name": "log", "abstract": "<p>Whether to log or not</p>", "parent_name": "<PERSON><PERSON>Logger"}, "Protocols/SocketLogger.html#/s:8SocketIO0A6LoggerP3log_4typeySSyXK_SStF": {"name": "log(_:type:)", "abstract": "<p>Normal log messages</p>", "parent_name": "<PERSON><PERSON>Logger"}, "Protocols/SocketLogger.html#/s:8SocketIO0A6LoggerP5error_4typeySSyXK_SStF": {"name": "error(_:type:)", "abstract": "<p>Error Messages</p>", "parent_name": "<PERSON><PERSON>Logger"}, "Protocols/SocketDataBufferable.html#/s:8SocketIO0A14DataBufferableP14waitingPacketsSayAA0A6PacketVGvp": {"name": "waitingPackets", "abstract": "<p>A list of packets that are waiting for binary data.</p>", "parent_name": "SocketDataBufferable"}, "Protocols/SocketParsable.html#/s:8SocketIO0A8ParsableP15parseBinaryDatayAA0A6PacketVSg10Foundation0F0VF": {"name": "parseBinaryData(_:)", "abstract": "<p>Called when the engine has received some binary data that should be attached to a packet.</p>", "parent_name": "SocketParsable"}, "Protocols/SocketParsable.html#/s:8SocketIO0A8ParsableP05parseA7MessageyAA0A6PacketVSgSSF": {"name": "parseSocketMessage(_:)", "abstract": "<p>Called when the engine has received a string that should be parsed into a socket.io packet.</p>", "parent_name": "SocketParsable"}, "Protocols/SocketParsable.html#/s:8SocketIO0A8ParsablePA2A0A14DataBufferableRzAA0A11ManagerSpecRzrlE05parseA7MessageyAA0A6PacketVSgSSF": {"name": "parseSocketMessage(_:)", "parent_name": "SocketParsable"}, "Protocols/SocketParsable.html#/s:8SocketIO0A8ParsablePA2A0A14DataBufferableRzAA0A11ManagerSpecRzrlE011parseBinaryD0yAA0A6PacketVSg10Foundation0D0VF": {"name": "parseBinaryData(_:)", "parent_name": "SocketParsable"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP07defaultA0AA0A8IOClientCvp": {"name": "defaultSocket", "abstract": "<p>Returns the socket associated with the default namespace (&ldquo;/&rdquo;).</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP6engineAA0a6EngineD0_pSgvp": {"name": "engine", "abstract": "<p>The engine for this manager.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP8forceNewSbvp": {"name": "forceNew", "abstract": "<p>If <code>true</code> then every time <code>connect</code> is called, a new engine will be created.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP11handleQueueSo17OS_dispatch_queueCvp": {"name": "handleQueue", "abstract": "<p>The queue that all interaction with the client should occur on. This is the queue that event handlers are", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP4nspsSDySSAA0A8IOClientCGvp": {"name": "nsps", "abstract": "<p>The sockets in this manager indexed by namespace.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP10reconnectsSbvp": {"name": "reconnects", "abstract": "<p>If <code>true</code>, this manager will try and reconnect on any disconnects.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP13reconnectWaitSivp": {"name": "reconnectWait", "abstract": "<p>The minimum number of seconds to wait before attempting to reconnect.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP16reconnectWaitMaxSivp": {"name": "reconnectWaitMax", "abstract": "<p>The maximum number of seconds to wait before attempting to reconnect.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP19randomizationFactorSdvp": {"name": "randomizationFactor", "abstract": "<p>The randomization factor for calculating reconnect jitter.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP9socketURL10Foundation0F0Vvp": {"name": "socketURL", "abstract": "<p>The URL of the socket.io server.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP6statusAA0A8IOStatusOvp": {"name": "status", "abstract": "<p>The status of this manager.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP7versionAA0A9IOVersionOvp": {"name": "version", "abstract": "<p>The version of socket.io in use.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP7connectyyF": {"name": "connect()", "abstract": "<p>Connects the underlying transport.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP07connectA0_11withPayloadyAA0A8IOClientC_SDySSypGSgtF": {"name": "connectSocket(_:withPayload:)", "abstract": "<p>Connects a socket through this manager&rsquo;s engine.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP13didDisconnect6reasonySS_tF": {"name": "didDisconnect(reason:)", "abstract": "<p>Called when the manager has disconnected from socket.io.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP10disconnectyyF": {"name": "disconnect()", "abstract": "<p>Disconnects the manager and all associated sockets.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP010disconnectA0yyAA0A8IOClientCF": {"name": "disconnectSocket(_:)", "abstract": "<p>Disconnects the given socket.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP010disconnectA012forNamespaceySS_tF": {"name": "disconnectSocket(forNamespace:)", "abstract": "<p>Disconnects the socket associated with <code>forNamespace</code>.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP7emitAllyySS_AA0A4Data_pdtF": {"name": "emitAll(_:_:)", "abstract": "<p>Sends an event to the server on all namespaces in this manager.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP9reconnectyyF": {"name": "reconnect()", "abstract": "<p>Tries to reconnect to the server.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP06removeA0yAA0A8IOClientCSgAFF": {"name": "removeSocket(_:)", "abstract": "<p>Removes the socket from the manager&rsquo;s control.", "parent_name": "SocketManagerSpec"}, "Protocols/SocketManagerSpec.html#/s:8SocketIO0A11ManagerSpecP6socket12forNamespaceAA0A8IOClientCSS_tF": {"name": "socket(forNamespace:)", "abstract": "<p>Returns a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/SocketIOClient.html\">SocketIOClient</a></code> for the given namespace. This socket shares a transport with the manager.</p>", "parent_name": "SocketManagerSpec"}, "Protocols/SocketEngineWebsocket.html#/s:8SocketIO0A15EngineWebsocketP11wsConnectedSbvp": {"name": "wsConnected", "abstract": "<p>Whether or not the ws is connected</p>", "parent_name": "SocketEngineWebsocket"}, "Protocols/SocketEngineWebsocket.html#/s:8SocketIO0A15EngineWebsocketP07sendWebA7Message_8withType0H4Data10completionySS_AA0ac6PacketI0OSay10Foundation0J0VGyycSgtF": {"name": "sendWebSocketMessage(_:withType:withData:completion:)", "abstract": "<p>Sends an engine.io message through the WebSocket transport.</p>", "parent_name": "SocketEngineWebsocket"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP6clientAA0aC6Client_pSgvp": {"name": "client", "abstract": "<p>The client for this engine.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP6closedSbvp": {"name": "closed", "abstract": "<p><code>true</code> if this engine is closed.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP8compressSbvp": {"name": "compress", "abstract": "<p>If <code>true</code> the engine will attempt to use WebSocket compression.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP9connectedSbvp": {"name": "connected", "abstract": "<p><code>true</code> if this engine is connected. Connected means that the initial poll connect has succeeded.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP13connectParamsSDySSypGSgvp": {"name": "connectParams", "abstract": "<p>The connect parameters sent during a connect.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP7cookiesSaySo12NSHTTPCookieCGSgvp": {"name": "cookies", "abstract": "<p>An array of HTTPCookies that are sent during the connection.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP11engineQueueSo17OS_dispatch_queueCvp": {"name": "engineQueue", "abstract": "<p>The queue that all engine actions take place on.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP12extraHeadersSDyS2SGSgvp": {"name": "extraHeaders", "abstract": "<p>A dictionary of extra http headers that will be set during connection.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP11fastUpgradeSbvp": {"name": "fastUpgrade", "abstract": "<p>When <code>true</code>, the engine is in the process of switching to WebSockets.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP12forcePollingSbvp": {"name": "forcePolling", "abstract": "<p>When <code>true</code>, the engine will only use HTTP long-polling as a transport.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP15forceWebsocketsSbvp": {"name": "forceWebsockets", "abstract": "<p>When <code>true</code>, the engine will only use WebSockets as a transport.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP7pollingSbvp": {"name": "polling", "abstract": "<p>If <code>true</code>, the engine is currently in HTTP long-polling mode.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP7probingSbvp": {"name": "probing", "abstract": "<p>If <code>true</code>, the engine is currently seeing whether it can upgrade to WebSockets.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP3sidSSvp": {"name": "sid", "abstract": "<p>The session id for this engine.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP10socketPathSSvp": {"name": "socketPath", "abstract": "<p>The path to engine.io.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP10urlPolling10Foundation3URLVvp": {"name": "url<PERSON><PERSON>ing", "abstract": "<p>The url for polling.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP06urlWebA010Foundation3URLVvp": {"name": "urlWebSocket", "abstract": "<p>The url for WebSockets.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP7versionAA0A9IOVersionOvp": {"name": "version", "abstract": "<p>The version of engine.io being used. Default is three.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP9websocketSbvp": {"name": "websocket", "abstract": "<p>If <code>true</code>, then the engine is currently in WebSockets mode.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP2ws10Starscream03WebA0CSgvp": {"name": "ws", "abstract": "<p>The WebSocket for this engine.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP6client3url7optionsxAA0aC6Client_p_10Foundation3URLVSDySSypGSgtcfc": {"name": "init(client:url:options:)", "abstract": "<p>Creates a new engine.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP7connectyyF": {"name": "connect()", "abstract": "<p>Starts the connection to the server.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP8didError6reasonySS_tF": {"name": "did<PERSON><PERSON><PERSON>(reason:)", "abstract": "<p>Called when an error happens during execution. Causes a disconnection.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP10disconnect6reasonySS_tF": {"name": "disconnect(reason:)", "abstract": "<p>Disconnects from the server.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP13doFastUpgradeyyF": {"name": "doFastUpgrade()", "abstract": "<p>Called to switch from HTTP long-polling to WebSockets. After calling this method the engine will be in", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP024flushWaitingForPostToWebA0yyF": {"name": "flushWaitingForPostToWebSocket()", "abstract": "<p>Causes any packets that were waiting for POSTing to be sent through the WebSocket. This happens because when", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP05parseC4Datayy10Foundation0F0VF": {"name": "parseEngineData(_:)", "abstract": "<p>Parses raw binary received from engine.io.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP05parseC7MessageyySSF": {"name": "parseEngineMessage(_:)", "abstract": "<p>Parses a raw engine.io packet.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEngineSpec.html#/s:8SocketIO0A10EngineSpecP5write_8withType0F4Data10completionySS_AA0ac6PacketG0OSay10Foundation0H0VGyycSgtF": {"name": "write(_:withType:withData:completion:)", "abstract": "<p>Writes a message to engine.io, independent of transport.</p>", "parent_name": "SocketEngineSpec"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP11invalidatedSbvp": {"name": "invalidated", "abstract": "<p><code>true</code> If engine&rsquo;s session has been invalidated.</p>", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP8postWaitSaySS3msg_yycSg10completiontGvp": {"name": "postWait", "abstract": "<p>A queue of engine.io messages waiting for POSTing</p>", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP7sessionSo12NSURLSessionCSgvp": {"name": "session", "abstract": "<p>The URLSession that will be used for polling.</p>", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP14waitingForPollSbvp": {"name": "waitingForPoll", "abstract": "<p><code>true</code> if there is an outstanding poll. Trying to poll before the first is done will cause socket.io to", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP14waitingForPostSbvp": {"name": "waitingForPost", "abstract": "<p><code>true</code> if there is an outstanding post. Trying to post before the first is done will cause socket.io to", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP6doPollyyF": {"name": "doPoll()", "abstract": "<p>Call to send a long-polling request.</p>", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP15sendPollMessage_8withType0H4Data10completionySS_AA0ac6PacketI0OSay10Foundation0J0VGyycSgtF": {"name": "sendPollMessage(_:withType:withData:completion:)", "abstract": "<p>Sends an engine.io message through the polling transport.</p>", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEnginePollable.html#/s:8SocketIO0A14EnginePollableP11stopPollingyyF": {"name": "stopPolling()", "abstract": "<p>Call to stop polling and invalidate the URLSession.</p>", "parent_name": "SocketEnginePollable"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidErrorWithReason:": {"name": "engineDidError(reason:)", "abstract": "<p>Called when the engine errors.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidCloseWithReason:": {"name": "engineDidClose(reason:)", "abstract": "<p>Called when the engine closes.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidOpenWithReason:": {"name": "engineDidOpen(reason:)", "abstract": "<p>Called when the engine opens.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidReceivePing": {"name": "engineDidReceivePing()", "abstract": "<p>Called when the engine receives a ping message. Only called in socket.io &gt;3.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidReceivePong": {"name": "engineDidReceivePong()", "abstract": "<p>Called when the engine receives a pong message. Only called in socket.io 2.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidSendPing": {"name": "engineDidSendPing()", "abstract": "<p>Called when the engine sends a ping to the server. Only called in socket.io 2.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidSendPong": {"name": "engineDidSendPong()", "abstract": "<p>Called when the engine sends a pong to the server. Only called in socket.io &gt;3.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)parseEngineMessage:": {"name": "parseEngineMessage(_:)", "abstract": "<p>Called when the engine has a message that must be parsed.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)parseEngineBinaryData:": {"name": "parseEngineBinaryData(_:)", "abstract": "<p>Called when the engine receives binary data.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketEngineClient.html#/c:@M@SocketIO@objc(pl)SocketEngineClient(im)engineDidWebsocketUpgradeWithHeaders:": {"name": "engineDidWebsocketUpgrade(headers:)", "abstract": "<p>Called when when upgrading the http connection to a websocket connection.</p>", "parent_name": "SocketEngineClient"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP10anyHandleryAA0A8AnyEventCcSgvp": {"name": "<PERSON><PERSON><PERSON><PERSON>", "abstract": "<p>A handler that will be called on any event.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP8handlersSayAA0A12EventHandlerVGvp": {"name": "handlers", "abstract": "<p>The array of handlers for this socket.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP7managerAA0a7ManagerD0_pSgvp": {"name": "manager", "abstract": "<p>The manager for this socket.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP3nspSSvp": {"name": "nsp", "abstract": "<p>The namespace that this socket is currently connected to.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP11rawEmitViewAA0a3RawG0Cvp": {"name": "rawEmitView", "abstract": "<p>A view into this socket where emits do not check for binary data.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP3sidSSSgvp": {"name": "sid", "abstract": "<p>The id of this socket.io connect. This is different from the sid of the engine.io connection.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP6statusAA0A8IOStatusOvp": {"name": "status", "abstract": "<p>The status of this client.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP7connect11withPayloadySDySSypGSg_tF": {"name": "connect(withPayload:)", "abstract": "<p>Connect to the server. The same as calling <code>connect(timeoutAfter:with<PERSON><PERSON><PERSON>:)</code> with a timeout of 0.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP7connect11withPayload12timeoutAfter0F7HandlerySDySSypGSg_SdyycSgtF": {"name": "connect(withPayload:timeoutAfter:withHandler:)", "abstract": "<p>Connect to the server. If we aren&rsquo;t connected after <code>timeoutAfter</code> seconds, then <code>with<PERSON><PERSON><PERSON></code> is called.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP10didConnect11toNamespace7payloadySS_SDySSypGSgtF": {"name": "didConnect(toNamespace:payload:)", "abstract": "<p>Called when the client connects to a namespace. If the client was created with a namespace upfront,", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP13didDisconnect6reasonySS_tF": {"name": "didDisconnect(reason:)", "abstract": "<p>Called when the client has disconnected from socket.io.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP8didError6reasonySS_tF": {"name": "did<PERSON><PERSON><PERSON>(reason:)", "abstract": "<p>Called when the client encounters an error.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP10disconnectyyF": {"name": "disconnect()", "abstract": "<p>Disconnects the socket.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP4emit__10completionySS_AA0A4Data_pdyycSgtF": {"name": "emit(_:_:completion:)", "abstract": "<p>Send an event to the server, with optional data items and optional write completion handler.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP7emitAck_4withySi_SayypGtF": {"name": "emitAck(_:with:)", "abstract": "<p>Call when you wish to tell the server that you&rsquo;ve received the event for <code>ack</code>.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP11emitWithAckyAA02OnG8CallbackCSS_AA0A4Data_pdtF": {"name": "emitWithAck(_:_:)", "abstract": "<p>Sends a message to the server, requesting an ack.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP9handleAck_4dataySi_SayypGtF": {"name": "handleAck(_:data:)", "abstract": "<p>Called when socket.io has acked one of our emits. Causes the corresponding ack callback to be called.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP17handleClientEvent_4datayAA0afG0O_SayypGtF": {"name": "handleClientEvent(_:data:)", "abstract": "<p>Called on socket.io specific events.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP11handleEvent_4data17isInternalMessage7withAckySS_SayypGSbSitF": {"name": "handleEvent(_:data:isInternalMessage:withAck:)", "abstract": "<p>Called when we get an event from socket.io.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP12handlePacketyyAA0aF0VF": {"name": "handlePacket(_:)", "abstract": "<p>Causes a client to handle a socket.io packet. The namespace for the packet must match the namespace of the", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP14leaveNamespaceyyF": {"name": "leaveNamespace()", "abstract": "<p>Call when you wish to leave a namespace and disconnect this socket.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP13joinNamespace11withPayloadySDySSypGSg_tF": {"name": "joinNamespace(withPayload:)", "abstract": "<p>Joins <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP3nspSSvp\">nsp</a></code>. You shouldn&rsquo;t need to call this directly, instead call <code>connect</code>.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP3off11clientEventyAA0a6ClientG0O_tF": {"name": "off(clientEvent:)", "abstract": "<p>Removes handler(s) for a client event.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP3offyySSF": {"name": "off(_:)", "abstract": "<p>Removes handler(s) based on an event name.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP3off2idy10Foundation4UUIDV_tF": {"name": "off(id:)", "abstract": "<p>Removes a handler with the specified UUID gotten from an <code>on</code> or <code>once</code></p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP2on_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF": {"name": "on(_:callback:)", "abstract": "<p>Adds a handler for an event.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP2on11clientEvent8callback10Foundation4UUIDVAA0a6ClientG0O_ySayypG_AA0A10AckEmitterCtctF": {"name": "on(clientEvent:callback:)", "abstract": "<p>Adds a handler for a client event.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP4once11clientEvent8callback10Foundation4UUIDVAA0a6ClientG0O_ySayypG_AA0A10AckEmitterCtctF": {"name": "once(clientEvent:callback:)", "abstract": "<p>Adds a single-use handler for a client event.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP4once_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF": {"name": "once(_:callback:)", "abstract": "<p>Adds a single-use handler for an event.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP5onAnyyyyAA0aF5EventCcF": {"name": "onAny(_:)", "abstract": "<p>Adds a handler that will be called on every event.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP17removeAllHandlersyyF": {"name": "removeAllHandlers()", "abstract": "<p>Removes all handlers.</p>", "parent_name": "SocketIOClientSpec"}, "Protocols/SocketIOClientSpec.html#/s:8SocketIO0A12IOClientSpecP15setReconnecting6reasonySS_tF": {"name": "setReconnecting(reason:)", "abstract": "<p>Puts the socket back into the connecting state.", "parent_name": "SocketIOClientSpec"}, "Protocols/ConfigSettable.html#/s:8SocketIO14ConfigSettableP10setConfigsyyAA0A21IOClientConfigurationVF": {"name": "setConfigs(_:)", "abstract": "<p>Called when an <code>ConfigSettable</code> should set/update its configs from a given configuration.</p>", "parent_name": "ConfigSettable"}, "Protocols/ConfigSettable.html": {"name": "ConfigSettable", "abstract": "<p>Declares that a type can set configs from a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/SocketIOClientConfiguration.html\">SocketIOClientConfiguration</a></code>.</p>"}, "Protocols/SocketIOClientSpec.html": {"name": "SocketIOClientSpec", "abstract": "<p>Defines the interface for a SocketIOClient.</p>"}, "Protocols/SocketEngineClient.html": {"name": "SocketEngineClient", "abstract": "<p>Declares that a type will be a delegate to an engine.</p>"}, "Protocols/SocketEnginePollable.html": {"name": "SocketEnginePollable", "abstract": "<p>Protocol that is used to implement socket.io polling support</p>"}, "Protocols/SocketEngineSpec.html": {"name": "SocketEngineSpec", "abstract": "<p>Specifies a SocketEngine.</p>"}, "Protocols/SocketEngineWebsocket.html": {"name": "SocketEngineWebsocket", "abstract": "<p>Protocol that is used to implement socket.io WebSocket support</p>"}, "Protocols/SocketManagerSpec.html": {"name": "SocketManagerSpec", "abstract": "<p>A manager for a socket.io connection.</p>"}, "Protocols/SocketParsable.html": {"name": "SocketParsable", "abstract": "<p>Defines that a type will be able to parse socket.io-protocol messages.</p>"}, "Protocols/SocketDataBufferable.html": {"name": "SocketDataBufferable", "abstract": "<p>Says that a type will be able to buffer binary data before all data for an event has come in.</p>"}, "Protocols/SocketLogger.html": {"name": "<PERSON><PERSON>Logger", "abstract": "<p>Represents a class will log client events.</p>"}, "Protocols/SocketData.html": {"name": "SocketData", "abstract": "<p>A marking protocol that says a type can be represented in a socket.io packet.</p>"}, "Extensions.html#/s:Sa": {"name": "Array"}, "Extensions.html#/s:Sb": {"name": "Bool"}, "Extensions.html#/s:SD": {"name": "Dictionary"}, "Extensions.html#/s:Sd": {"name": "Double"}, "Extensions.html#/s:Si": {"name": "Int"}, "Extensions.html#/c:objc(cs)NSArray": {"name": "NSA<PERSON>y"}, "Extensions.html#/s:10Foundation4DataV": {"name": "Data"}, "Extensions.html#/c:objc(cs)NSData": {"name": "NSData"}, "Extensions.html#/c:objc(cs)NSDictionary": {"name": "NSDictionary"}, "Extensions.html#/c:objc(cs)NSString": {"name": "NSString"}, "Extensions.html#/c:objc(cs)NSNull": {"name": "NSNull"}, "Extensions.html#/s:SS": {"name": "String"}, "Enums/SocketParsableError.html#/s:8SocketIO0A13ParsableErrorO16invalidDataArrayyA2CmF": {"name": "invalid<PERSON><PERSON><PERSON><PERSON>y", "abstract": "<p>Thrown when a packet received has an invalid data array, or is missing the data array.</p>", "parent_name": "SocketParsableError"}, "Enums/SocketParsableError.html#/s:8SocketIO0A13ParsableErrorO13invalidPacketyA2CmF": {"name": "invalidPacket", "abstract": "<p>Thrown when an malformed packet is received.</p>", "parent_name": "SocketParsableError"}, "Enums/SocketParsableError.html#/s:8SocketIO0A13ParsableErrorO17invalidPacketTypeyA2CmF": {"name": "invalidPacketType", "abstract": "<p>Thrown when the parser receives an unknown packet type.</p>", "parent_name": "SocketParsableError"}, "Enums/SocketEnginePacketType.html#/c:@M@SocketIO@E@SocketEnginePacketType@SocketEnginePacketTypeOpen": {"name": "open", "abstract": "<p>Open message.</p>", "parent_name": "SocketEnginePacketType"}, "Enums/SocketEnginePacketType.html#/c:@M@SocketIO@E@SocketEnginePacketType@SocketEnginePacketTypeClose": {"name": "close", "abstract": "<p>Close message.</p>", "parent_name": "SocketEnginePacketType"}, "Enums/SocketEnginePacketType.html#/c:@M@SocketIO@E@SocketEnginePacketType@SocketEnginePacketTypePing": {"name": "ping", "abstract": "<p>Ping message.</p>", "parent_name": "SocketEnginePacketType"}, "Enums/SocketEnginePacketType.html#/c:@M@SocketIO@E@SocketEnginePacketType@SocketEnginePacketTypePong": {"name": "pong", "abstract": "<p>Pong message.</p>", "parent_name": "SocketEnginePacketType"}, "Enums/SocketEnginePacketType.html#/c:@M@SocketIO@E@SocketEnginePacketType@SocketEnginePacketTypeMessage": {"name": "message", "abstract": "<p>Regular message.</p>", "parent_name": "SocketEnginePacketType"}, "Enums/SocketEnginePacketType.html#/c:@M@SocketIO@E@SocketEnginePacketType@SocketEnginePacketTypeUpgrade": {"name": "upgrade", "abstract": "<p>Upgrade message.</p>", "parent_name": "SocketEnginePacketType"}, "Enums/SocketEnginePacketType.html#/c:@M@SocketIO@E@SocketEnginePacketType@SocketEnginePacketTypeNoop": {"name": "noop", "abstract": "<p>NOOP.</p>", "parent_name": "SocketEnginePacketType"}, "Enums/SocketIOStatus.html#/c:@M@SocketIO@E@SocketIOStatus@SocketIOStatusNotConnected": {"name": "notConnected", "abstract": "<p>The client/manager has never been connected. Or the client has been reset.</p>", "parent_name": "SocketIOStatus"}, "Enums/SocketIOStatus.html#/c:@M@SocketIO@E@SocketIOStatus@SocketIOStatusDisconnected": {"name": "disconnected", "abstract": "<p>The client/manager was once connected, but not anymore.</p>", "parent_name": "SocketIOStatus"}, "Enums/SocketIOStatus.html#/c:@M@SocketIO@E@SocketIOStatus@SocketIOStatusConnecting": {"name": "connecting", "abstract": "<p>The client/manager is in the process of connecting.</p>", "parent_name": "SocketIOStatus"}, "Enums/SocketIOStatus.html#/c:@M@SocketIO@E@SocketIOStatus@SocketIOStatusConnected": {"name": "connected", "abstract": "<p>The client/manager is currently connected.</p>", "parent_name": "SocketIOStatus"}, "Enums/SocketIOStatus.html#/s:8SocketIO0A8IOStatusO6activeSbvp": {"name": "active", "parent_name": "SocketIOStatus"}, "Enums/SocketIOStatus.html#/s:s23CustomStringConvertibleP11descriptionSSvp": {"name": "description", "parent_name": "SocketIOStatus"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO7connectyA2CmF": {"name": "connect", "abstract": "<p>Emitted when the client connects. This is also called on a successful reconnection. A connect event gets one", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO10disconnectyA2CmF": {"name": "disconnect", "abstract": "<p>Emitted when the socket has disconnected and will not attempt to try to reconnect.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO5erroryA2CmF": {"name": "error", "abstract": "<p>Emitted when an error occurs.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO4pingyA2CmF": {"name": "ping", "abstract": "<p>Emitted whenever the engine sends a ping.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO4pongyA2CmF": {"name": "pong", "abstract": "<p>Emitted whenever the engine gets a pong.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO9reconnectyA2CmF": {"name": "reconnect", "abstract": "<p>Emitted when the client begins the reconnection process.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO16reconnectAttemptyA2CmF": {"name": "reconnectAttempt", "abstract": "<p>Emitted each time the client tries to reconnect to the server.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO12statusChangeyA2CmF": {"name": "statusChange", "abstract": "<p>Emitted every time there is a change in the client&rsquo;s status.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO16websocketUpgradeyA2CmF": {"name": "websocketUpgrade", "abstract": "<p>Emitted when when upgrading the http connection to a websocket connection.</p>", "parent_name": "SocketClientEvent"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO8compressyA2CmF": {"name": "compress", "abstract": "<p>If given, the WebSocket transport will attempt to use compression.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO13connectParamsyACSDySSypGcACmF": {"name": "connectParams(_:)", "abstract": "<p>A dictionary of GET parameters that will be included in the connect url.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO7cookiesyACSaySo12NSHTTPCookieCGcACmF": {"name": "cookies(_:)", "abstract": "<p>An array of cookies that will be sent during the initial connection.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO12extraHeadersyACSDyS2SGcACmF": {"name": "extraHeaders(_:)", "abstract": "<p>Any extra HTTP headers that should be sent during the initial connection.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO8forceNewyACSbcACmF": {"name": "forceNew(_:)", "abstract": "<p>If passed <code>true</code>, will cause the client to always create a new engine. Useful for debugging,", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO12forcePollingyACSbcACmF": {"name": "forcePolling(_:)", "abstract": "<p>If passed <code>true</code>, the only transport that will be used will be HTTP long-polling.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO15forceWebsocketsyACSbcACmF": {"name": "forceWebsockets(_:)", "abstract": "<p>If passed <code>true</code>, the only transport that will be used will be WebSockets.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO16enableSOCKSProxyyACSbcACmF": {"name": "enableSOCKSProxy(_:)", "abstract": "<p>If passed <code>true</code>, the WebSocket stream will be configured with the enableSOCKSProxy <code>true</code>.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO11handleQueueyACSo17OS_dispatch_queueCcACmF": {"name": "handleQueue(_:)", "abstract": "<p>The queue that all interaction with the client should occur on. This is the queue that event handlers are", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO3logyACSbcACmF": {"name": "log(_:)", "abstract": "<p>If passed <code>true</code>, the client will log debug information. This should be turned off in production code.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO6loggeryAcA0A6Logger_pcACmF": {"name": "logger(_:)", "abstract": "<p>Used to pass in a custom logger.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO4pathyACSScACmF": {"name": "path(_:)", "abstract": "<p>A custom path to socket.io. Only use this if the socket.io server is configured to look for this path.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO10reconnectsyACSbcACmF": {"name": "reconnects(_:)", "abstract": "<p>If passed <code>false</code>, the client will not reconnect when it loses connection. Useful if you want full control", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO17reconnectAttemptsyACSicACmF": {"name": "reconnectAttempts(_:)", "abstract": "<p>The number of times to try and reconnect before giving up. Pass <code>-1</code> to <a href=\"https://www.youtube.com/watch?v=dQw4w9WgXcQ\">never give up</a>.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO13reconnectWaityACSicACmF": {"name": "reconnectWait(_:)", "abstract": "<p>The minimum number of seconds to wait before reconnect attempts.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO16reconnectWaitMaxyACSicACmF": {"name": "reconnectWaitMax(_:)", "abstract": "<p>The maximum number of seconds to wait before reconnect attempts.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO19randomizationFactoryACSdcACmF": {"name": "randomizationFactor(_:)", "abstract": "<p>The randomization factor for calculating reconnect jitter.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO6secureyACSbcACmF": {"name": "secure(_:)", "abstract": "<p>Set <code>true</code> if your server is using secure transports.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO8securityyAC10Starscream18CertificatePinning_pcACmF": {"name": "security(_:)", "abstract": "<p>Allows you to set which certs are valid. Useful for SSL pinning.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO10selfSignedyACSbcACmF": {"name": "selfSigned(_:)", "abstract": "<p>If you&rsquo;re using a self-signed set. Only use for development.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO15sessionDelegateyACSo012NSURLSessionF0_pcACmF": {"name": "sessionDelegate(_:)", "abstract": "<p>Sets an NSURLSessionDelegate for the underlying engine. Useful if you need to handle self-signed certs.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO7versionyAcA0A9IOVersionOcACmF": {"name": "version(_:)", "abstract": "<p>The version of socket.io being used. This should match the server version. Default is 3.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO11descriptionSSvp": {"name": "description", "abstract": "<p>The description of this option.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOClientOption.html#/s:8SocketIO0A14IOClientOptionO2eeoiySbAC_ACtFZ": {"name": "==(_:_:)", "abstract": "<p>Compares whether two options are the same.</p>", "parent_name": "SocketIOClientOption"}, "Enums/SocketIOVersion.html#/s:8SocketIO0A9IOVersionO3twoyA2CmF": {"name": "two", "abstract": "<p>socket.io 2, engine.io 3</p>", "parent_name": "SocketIOVersion"}, "Enums/SocketIOVersion.html#/s:8SocketIO0A9IOVersionO5threeyA2CmF": {"name": "three", "abstract": "<p>socket.io 3, engine.io 4</p>", "parent_name": "SocketIOVersion"}, "Enums/SocketAckStatus.html#/s:8SocketIO0A9AckStatusO02noC0yA2CmF": {"name": "noAck", "abstract": "<p>The ack timed out.</p>", "parent_name": "SocketAckStatus"}, "Enums/SocketAckStatus.html#/s:8SocketIO0A9AckStatusO2eeoiySbSS_ACtFZ": {"name": "==(_:_:)", "abstract": "<p>Tests whether a string is equal to a given SocketAckStatus</p>", "parent_name": "SocketAckStatus"}, "Enums/SocketAckStatus.html#/s:8SocketIO0A9AckStatusO2eeoiySbAC_SStFZ": {"name": "==(_:_:)", "abstract": "<p>Tests whether a string is equal to a given SocketAckStatus</p>", "parent_name": "SocketAckStatus"}, "Enums/SocketAckStatus.html": {"name": "SocketAckStatus", "abstract": "<p>The status of an ack.</p>"}, "Enums/SocketIOVersion.html": {"name": "SocketIOVersion", "abstract": "<p>The socket.io version being used.</p>"}, "Enums/SocketIOClientOption.html": {"name": "SocketIOClientOption", "abstract": "<p>The options for a client.</p>"}, "Enums/SocketClientEvent.html": {"name": "SocketClientEvent", "abstract": "<p>The set of events that are generated by the client.</p>"}, "Enums/SocketIOStatus.html": {"name": "SocketIOStatus", "abstract": "<p>Represents state of a manager or client.</p>"}, "Enums/SocketEnginePacketType.html": {"name": "SocketEnginePacketType", "abstract": "<p>Represents the type of engine.io packet types.</p>"}, "Enums/SocketParsableError.html": {"name": "SocketParsableError", "abstract": "<p>Errors that can be thrown during parsing.</p>"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC07defaultA0AA0A8IOClientCvp": {"name": "defaultSocket", "abstract": "<p>The socket associated with the default namespace (&ldquo;/&rdquo;).</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC9socketURL10Foundation0E0Vvp": {"name": "socketURL", "abstract": "<p>The URL of the socket.io server.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC6configAA0A21IOClientConfigurationVvp": {"name": "config", "abstract": "<p>The configuration for this client.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC6engineAA0A10EngineSpec_pSgvp": {"name": "engine", "abstract": "<p>The engine for this manager.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC8forceNewSbvp": {"name": "forceNew", "abstract": "<p>If <code>true</code> then every time <code>connect</code> is called, a new engine will be created.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC11handleQueueSo17OS_dispatch_queueCvp": {"name": "handleQueue", "abstract": "<p>The queue that all interaction with the client should occur on. This is the queue that event handlers are", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC4nspsSDySSAA0A8IOClientCGvp": {"name": "nsps", "abstract": "<p>The sockets in this manager indexed by namespace.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC10reconnectsSbvp": {"name": "reconnects", "abstract": "<p>If <code>true</code>, this client will try and reconnect on any disconnects.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC13reconnectWaitSivp": {"name": "reconnectWait", "abstract": "<p>The minimum number of seconds to wait before attempting to reconnect.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC16reconnectWaitMaxSivp": {"name": "reconnectWaitMax", "abstract": "<p>The maximum number of seconds to wait before attempting to reconnect.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC19randomizationFactorSdvp": {"name": "randomizationFactor", "abstract": "<p>The randomization factor for calculating reconnect jitter.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC6statusAA0A8IOStatusOvp": {"name": "status", "abstract": "<p>The status of this manager.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A11ManagerSpecP7versionAA0A9IOVersionOvp": {"name": "version", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC14waitingPacketsSayAA0A6PacketVGvp": {"name": "waitingPackets", "abstract": "<p>A list of packets that are waiting for binary data.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC9socketURL6configAC10Foundation0E0V_AA0A21IOClientConfigurationVtcfc": {"name": "init(socketURL:config:)", "abstract": "<p>Type safe way to create a new SocketIOClient. <code>opts</code> can be omitted.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)initWithSocketURL:config:": {"name": "init(socketURL:config:)", "abstract": "<p>Not so type safe way to create a SocketIOClient, meant for Objective-C compatiblity.", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC7connectyyF": {"name": "connect()", "abstract": "<p>Connects the underlying transport and the default namespace socket.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC07connectA0_11withPayloadyAA0A8IOClientC_SDySSypGSgtF": {"name": "connectSocket(_:withPayload:)", "abstract": "<p>Connects a socket through this manager&rsquo;s engine.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC13didDisconnect6reasonySS_tF": {"name": "didDisconnect(reason:)", "abstract": "<p>Called when the manager has disconnected from socket.io.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC10disconnectyyF": {"name": "disconnect()", "abstract": "<p>Disconnects the manager and all associated sockets.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC010disconnectA0yyAA0A8IOClientCF": {"name": "disconnectSocket(_:)", "abstract": "<p>Disconnects the given socket.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC010disconnectA012forNamespaceySS_tF": {"name": "disconnectSocket(forNamespace:)", "abstract": "<p>Disconnects the socket associated with <code>forNamespace</code>.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC7emitAll11clientEvent4datayAA0a6ClientG0O_SayypGtF": {"name": "emitAll(clientEvent:data:)", "abstract": "<p>Sends a client event to all sockets in <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/SocketManager.html#/s:8SocketIO0A7ManagerC4nspsSDySSAA0A8IOClientCGvp\">nsps</a></code></p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC7emitAllyySS_AA0A4Data_pdtF": {"name": "emitAll(_:_:)", "abstract": "<p>Sends an event to the server on all namespaces in this manager.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidCloseWithReason:": {"name": "engineDidClose(reason:)", "abstract": "<p>Called when the engine closes.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidErrorWithReason:": {"name": "engineDidError(reason:)", "abstract": "<p>Called when the engine errors.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidOpenWithReason:": {"name": "engineDidOpen(reason:)", "abstract": "<p>Called when the engine opens.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidReceivePing": {"name": "engineDidReceivePing()", "abstract": "<p>Called when the engine receives a ping message.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidSendPing": {"name": "engineDidSendPing()", "abstract": "<p>Called when the sends a ping to the server.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidReceivePong": {"name": "engineDidReceivePong()", "abstract": "<p>Called when the engine receives a pong message.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidSendPong": {"name": "engineDidSendPong()", "abstract": "<p>Called when the sends a pong to the server.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)engineDidWebsocketUpgradeWithHeaders:": {"name": "engineDidWebsocketUpgrade(headers:)", "abstract": "<p>Called when when upgrading the http connection to a websocket connection.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)parseEngineMessage:": {"name": "parseEngineMessage(_:)", "abstract": "<p>Called when the engine has a message that must be parsed.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/c:@M@SocketIO@objc(cs)SocketManager(im)parseEngineBinaryData:": {"name": "parseEngineBinaryData(_:)", "abstract": "<p>Called when the engine receives binary data.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC9reconnectyyF": {"name": "reconnect()", "abstract": "<p>Tries to reconnect to the server.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC06removeA0yAA0A8IOClientCSgAFF": {"name": "removeSocket(_:)", "abstract": "<p>Removes the socket from the manager&rsquo;s control. One of the disconnect methods should be called before calling this", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC10setConfigsyyAA0A21IOClientConfigurationVF": {"name": "setConfigs(_:)", "abstract": "<p>Sets manager specific configs.</p>", "parent_name": "SocketManager"}, "Classes/SocketManager.html#/s:8SocketIO0A7ManagerC6socket12forNamespaceAA0A8IOClientCSS_tF": {"name": "socket(forNamespace:)", "abstract": "<p>Returns a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/SocketIOClient.html\">SocketIOClient</a></code> for the given namespace. This socket shares a transport with the manager.</p>", "parent_name": "SocketManager"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC11engineQueueSo17OS_dispatch_queueCvp": {"name": "engineQueue", "abstract": "<p>The queue that all engine actions take place on.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC13connectParamsSDySSypGSgvp": {"name": "connectParams", "abstract": "<p>The connect parameters sent during a connect.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC12extraHeadersSDyS2SGSgvp": {"name": "extraHeaders", "abstract": "<p>A dictionary of extra http headers that will be set during connection.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC8postWaitSaySS3msg_yycSg10completiontGvp": {"name": "postWait", "abstract": "<p>A queue of engine.io messages waiting for POSTing</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC14waitingForPollSbvp": {"name": "waitingForPoll", "abstract": "<p><code>true</code> if there is an outstanding poll. Trying to poll before the first is done will cause socket.io to", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC14waitingForPostSbvp": {"name": "waitingForPost", "abstract": "<p><code>true</code> if there is an outstanding post. Trying to post before the first is done will cause socket.io to", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC6closedSbvp": {"name": "closed", "abstract": "<p><code>true</code> if this engine is closed.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC8compressSbvp": {"name": "compress", "abstract": "<p>If <code>true</code> the engine will attempt to use WebSocket compression.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC9connectedSbvp": {"name": "connected", "abstract": "<p><code>true</code> if this engine is connected. Connected means that the initial poll connect has succeeded.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC7cookiesSaySo12NSHTTPCookieCGSgvp": {"name": "cookies", "abstract": "<p>An array of HTTPCookies that are sent during the connection.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC11fastUpgradeSbvp": {"name": "fastUpgrade", "abstract": "<p>When <code>true</code>, the engine is in the process of switching to WebSockets.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC12forcePollingSbvp": {"name": "forcePolling", "abstract": "<p>When <code>true</code>, the engine will only use HTTP long-polling as a transport.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC15forceWebsocketsSbvp": {"name": "forceWebsockets", "abstract": "<p>When <code>true</code>, the engine will only use WebSockets as a transport.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC11invalidatedSbvp": {"name": "invalidated", "abstract": "<p><code>true</code> If engine&rsquo;s session has been invalidated.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC7pollingSbvp": {"name": "polling", "abstract": "<p>If <code>true</code>, the engine is currently in HTTP long-polling mode.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC7probingSbvp": {"name": "probing", "abstract": "<p>If <code>true</code>, the engine is currently seeing whether it can upgrade to WebSockets.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC7sessionSo12NSURLSessionCSgvp": {"name": "session", "abstract": "<p>The URLSession that will be used for polling.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC3sidSSvp": {"name": "sid", "abstract": "<p>The session id for this engine.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC10socketPathSSvp": {"name": "socketPath", "abstract": "<p>The path to engine.io.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC10urlPolling10Foundation3URLVvp": {"name": "url<PERSON><PERSON>ing", "abstract": "<p>The url for polling.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC06urlWebA010Foundation3URLVvp": {"name": "urlWebSocket", "abstract": "<p>The url for WebSockets.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC7versionAA0A9IOVersionOvp": {"name": "version", "abstract": "<p>The version of engine.io being used. Default is three.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC9websocketSbvp": {"name": "websocket", "abstract": "<p>If <code>true</code>, then the engine is currently in WebSockets mode.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC16enableSOCKSProxySbvp": {"name": "enableSOCKSProxy", "abstract": "<p>When <code>true</code>, the WebSocket <code>stream</code> will be configured with the enableSOCKSProxy <code>true</code>.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC2ws10Starscream03WebA0CSgvp": {"name": "ws", "abstract": "<p>The WebSocket for this engine.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC11wsConnectedSbvp": {"name": "wsConnected", "abstract": "<p>Whether or not the WebSocket is currently connected.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC6clientAA0aC6Client_pSgvp": {"name": "client", "abstract": "<p>The client for this engine.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC6client3url6configAcA0aC6Client_p_10Foundation3URLVAA0A21IOClientConfigurationVtcfc": {"name": "init(client:url:config:)", "abstract": "<p>Creates a new engine.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC6client3url7optionsAcA0aC6Client_p_10Foundation3URLVSDySSypGSgtcfc": {"name": "init(client:url:options:)", "abstract": "<p>Creates a new engine.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC7connectyyF": {"name": "connect()", "abstract": "<p>Starts the connection to the server.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC8didError6reasonySS_tF": {"name": "did<PERSON><PERSON><PERSON>(reason:)", "abstract": "<p>Called when an error happens during execution. Causes a disconnection.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC10disconnect6reasonySS_tF": {"name": "disconnect(reason:)", "abstract": "<p>Disconnects from the server.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC13doFastUpgradeyyF": {"name": "doFastUpgrade()", "abstract": "<p>Called to switch from HTTP long-polling to WebSockets. After calling this method the engine will be in", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC024flushWaitingForPostToWebA0yyF": {"name": "flushWaitingForPostToWebSocket()", "abstract": "<p>Causes any packets that were waiting for POSTing to be sent through the WebSocket. This happens because when", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC05parseC4Datayy10Foundation0E0VF": {"name": "parseEngineData(_:)", "abstract": "<p>Parses raw binary received from engine.io.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC05parseC7MessageyySSF": {"name": "parseEngineMessage(_:)", "abstract": "<p>Parses a raw engine.io packet.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC10setConfigsyyAA0A21IOClientConfigurationVF": {"name": "setConfigs(_:)", "abstract": "<p>Called when the engine should set/update its configs from a given configuration.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC5write_8withType0E4Data10completionySS_AA0ac6PacketF0OSay10Foundation0G0VGyycSgtF": {"name": "write(_:withType:withData:completion:)", "abstract": "<p>Writes a message to engine.io, independent of transport.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC10URLSession7session25didBecomeInvalidWithErrorySo12NSURLSessionC_So7NSErrorCSgtF": {"name": "URLSession(session:didBecomeInvalidWithError:)", "abstract": "<p>Delegate called when the session becomes invalid.</p>", "parent_name": "SocketEngine"}, "Classes/SocketEngine.html#/s:8SocketIO0A6EngineC10didReceive5event6clienty10Starscream03WebA5EventO_AG0iA0CtF": {"name": "didReceive(event:client:)", "abstract": "<p>Delegate method for WebSocketDelegate.</p>", "parent_name": "SocketEngine"}, "Classes/SocketRawAckView.html#/s:8SocketIO0A10RawAckViewC4withyyAA0A4Data_pd_tF": {"name": "with(_:)", "abstract": "<p>Call to ack receiving this event.</p>", "parent_name": "SocketRawAckView"}, "Classes/SocketRawAckView.html#/c:@M@SocketIO@objc(cs)SocketRawAckView(im)with:": {"name": "with(_:)", "abstract": "<p>Call to ack receiving this event.</p>", "parent_name": "SocketRawAckView"}, "Classes/SocketRawView.html#/s:8SocketIO0A7RawViewC4emityySS_AA0A4Data_pdtF": {"name": "emit(_:_:)", "abstract": "<p>Send an event to the server, with optional data items.</p>", "parent_name": "SocketRawView"}, "Classes/SocketRawView.html#/c:@M@SocketIO@objc(cs)SocketRawView(im)emit:with:": {"name": "emit(_:with:)", "abstract": "<p>Same as emit, but meant for Objective-C</p>", "parent_name": "SocketRawView"}, "Classes/SocketRawView.html#/s:8SocketIO0A7RawViewC11emitWithAckyAA02OnG8CallbackCSS_AA0A4Data_pdtF": {"name": "emitWithAck(_:_:)", "abstract": "<p>Sends a message to the server, requesting an ack.</p>", "parent_name": "SocketRawView"}, "Classes/SocketRawView.html#/c:@M@SocketIO@objc(cs)SocketRawView(im)emitWithAck:with:": {"name": "emitWithAck(_:with:)", "abstract": "<p>Same as emitWithAck, but for Objective-C</p>", "parent_name": "SocketRawView"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3nspSSvp": {"name": "nsp", "abstract": "<p>The namespace that this socket is currently connected to.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC10anyHandleryAA0A8AnyEventCcSgvp": {"name": "<PERSON><PERSON><PERSON><PERSON>", "abstract": "<p>A handler that will be called on any event.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC8handlersSayAA0A12EventHandlerVGvp": {"name": "handlers", "abstract": "<p>The array of handlers for this socket.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC7managerAA0A11ManagerSpec_pSgvp": {"name": "manager", "abstract": "<p>The manager for this socket.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC11rawEmitViewAA0a3RawF0Cvp": {"name": "rawEmitView", "abstract": "<p>A view into this socket where emits do not check for binary data.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC6statusAA0A8IOStatusOvp": {"name": "status", "abstract": "<p>The status of this client.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3sidSSSgvp": {"name": "sid", "abstract": "<p>The id of this socket.io connect. This is different from the sid of the engine.io connection.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC7manager3nspAcA0A11ManagerSpec_p_SStcfc": {"name": "init(manager:nsp:)", "abstract": "<p>Type safe way to create a new SocketIOClient. <code>opts</code> can be omitted.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC7connect11withPayloadySDySSypGSg_tF": {"name": "connect(withPayload:)", "abstract": "<p>Connect to the server. The same as calling <code>connect(timeoutAfter:with<PERSON><PERSON><PERSON>:)</code> with a timeout of 0.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC7connect11withPayload12timeoutAfter0E7HandlerySDySSypGSg_SdyycSgtF": {"name": "connect(withPayload:timeoutAfter:withHandler:)", "abstract": "<p>Connect to the server. If we aren&rsquo;t connected after <code>timeoutAfter</code> seconds, then <code>with<PERSON><PERSON><PERSON></code> is called.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC10didConnect11toNamespace7payloadySS_SDySSypGSgtF": {"name": "didConnect(toNamespace:payload:)", "abstract": "<p>Called when the client connects to a namespace. If the client was created with a namespace upfront,", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC13didDisconnect6reasonySS_tF": {"name": "didDisconnect(reason:)", "abstract": "<p>Called when the client has disconnected from socket.io.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC10disconnectyyF": {"name": "disconnect()", "abstract": "<p>Disconnects the socket.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC4emit__10completionySS_AA0A4Data_pdyycSgtF": {"name": "emit(_:_:completion:)", "abstract": "<p>Send an event to the server, with optional data items and optional write completion handler.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC11emitWithAckyAA02OnF8CallbackCSS_AA0A4Data_pdtF": {"name": "emitWithAck(_:_:)", "abstract": "<p>Sends a message to the server, requesting an ack.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC7emitAck_4withySi_SayypGtF": {"name": "emitAck(_:with:)", "abstract": "<p>Call when you wish to tell the server that you&rsquo;ve received the event for <code>ack</code>.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC9handleAck_4dataySi_SayypGtF": {"name": "handleAck(_:data:)", "abstract": "<p>Called when socket.io has acked one of our emits. Causes the corresponding ack callback to be called.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC17handleClientEvent_4datayAA0aeF0O_SayypGtF": {"name": "handleClientEvent(_:data:)", "abstract": "<p>Called on socket.io specific events.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC11handleEvent_4data17isInternalMessage7withAckySS_SayypGSbSitF": {"name": "handleEvent(_:data:isInternalMessage:withAck:)", "abstract": "<p>Called when we get an event from socket.io.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC12handlePacketyyAA0aE0VF": {"name": "handlePacket(_:)", "abstract": "<p>Causes a client to handle a socket.io packet. The namespace for the packet must match the namespace of the", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC14leaveNamespaceyyF": {"name": "leaveNamespace()", "abstract": "<p>Call when you wish to leave a namespace and disconnect this socket.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC13joinNamespace11withPayloadySDySSypGSg_tF": {"name": "joinNamespace(withPayload:)", "abstract": "<p>Joins <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/SocketIOClient.html#/s:8SocketIO0A8IOClientC3nspSSvp\">nsp</a></code>. You shouldn&rsquo;t need to call this directly, instead call <code>connect</code>.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3off11clientEventyAA0a6ClientF0O_tF": {"name": "off(clientEvent:)", "abstract": "<p>Removes handler(s) for a client event.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3offyySSF": {"name": "off(_:)", "abstract": "<p>Removes handler(s) based on an event name.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3off2idy10Foundation4UUIDV_tF": {"name": "off(id:)", "abstract": "<p>Removes a handler with the specified UUID gotten from an <code>on</code> or <code>once</code></p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC2on_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF": {"name": "on(_:callback:)", "abstract": "<p>Adds a handler for an event.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC2on11clientEvent8callback10Foundation4UUIDVAA0a6ClientF0O_ySayypG_AA0A10AckEmitterCtctF": {"name": "on(clientEvent:callback:)", "abstract": "<p>Adds a handler for a client event.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC4once11clientEvent8callback10Foundation4UUIDVAA0a6ClientF0O_ySayypG_AA0A10AckEmitterCtctF": {"name": "once(clientEvent:callback:)", "abstract": "<p>Adds a single-use handler for a client event.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC4once_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF": {"name": "once(_:callback:)", "abstract": "<p>Adds a single-use handler for an event.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC5onAnyyyyAA0aE5EventCcF": {"name": "onAny(_:)", "abstract": "<p>Adds a handler that will be called on every event.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC17removeAllHandlersyyF": {"name": "removeAllHandlers()", "abstract": "<p>Removes all handlers.</p>", "parent_name": "SocketIOClient"}, "Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC15setReconnecting6reasonySS_tF": {"name": "setReconnecting(reason:)", "abstract": "<p>Puts the socket back into the connecting state.", "parent_name": "SocketIOClient"}, "Classes/SocketAnyEvent.html#/c:@M@SocketIO@objc(cs)SocketAnyEvent(py)event": {"name": "event", "abstract": "<p>The event name.</p>", "parent_name": "SocketAnyEvent"}, "Classes/SocketAnyEvent.html#/c:@M@SocketIO@objc(cs)SocketAnyEvent(py)items": {"name": "items", "abstract": "<p>The data items for this event.</p>", "parent_name": "SocketAnyEvent"}, "Classes/SocketAnyEvent.html#/c:@M@SocketIO@objc(cs)SocketAnyEvent(py)description": {"name": "description", "abstract": "<p>The description of this event.</p>", "parent_name": "SocketAnyEvent"}, "Classes/OnAckCallback.html#/c:@M@SocketIO@objc(cs)OnAckCallback(im)timingOutAfter:callback:": {"name": "timingOut(after:callback:)", "abstract": "<p>Completes an emitWithAck. If this isn&rsquo;t called, the emit never happens.</p>", "parent_name": "OnAckCallback"}, "Classes/SocketAckEmitter.html#/c:@M@SocketIO@objc(cs)SocketAckEmitter(py)rawEmitView": {"name": "rawEmitView", "abstract": "<p>A view into this emitter where emits do not check for binary data.</p>", "parent_name": "SocketAckEmitter"}, "Classes/SocketAckEmitter.html#/s:8SocketIO0A10AckEmitterC8expectedSbvp": {"name": "expected", "abstract": "<p>If true, this handler is expecting to be acked. Call <code>with(_: SocketData...)</code> to ack.</p>", "parent_name": "SocketAckEmitter"}, "Classes/SocketAckEmitter.html#/s:8SocketIO0A10AckEmitterC6socket6ackNumAcA0A8IOClientC_Sitcfc": {"name": "init(socket:ackNum:)", "abstract": "<p>Creates a new <code>SocketAckEmitter</code>.</p>", "parent_name": "SocketAckEmitter"}, "Classes/SocketAckEmitter.html#/s:8SocketIO0A10AckEmitterC4withyyAA0A4Data_pd_tF": {"name": "with(_:)", "abstract": "<p>Call to ack receiving this event.</p>", "parent_name": "SocketAckEmitter"}, "Classes/SocketAckEmitter.html#/c:@M@SocketIO@objc(cs)SocketAckEmitter(im)with:": {"name": "with(_:)", "abstract": "<p>Call to ack receiving this event.</p>", "parent_name": "SocketAckEmitter"}, "Classes/SocketAckEmitter.html": {"name": "SocketAckEmitter", "abstract": "<p>A class that represents a waiting ack call.</p>"}, "Classes/OnAckCallback.html": {"name": "OnAckCallback", "abstract": "<p>A class that represents an emit that will request an ack that has not yet been sent."}, "Classes/SocketAnyEvent.html": {"name": "SocketAnyEvent", "abstract": "<p>Represents some event that was received.</p>"}, "Classes/SocketIOClient.html": {"name": "SocketIOClient", "abstract": "<p>Represents a socket.io-client.</p>"}, "Classes/SocketRawView.html": {"name": "SocketRawView", "abstract": "<p>Class that gives a backwards compatible way to cause an emit not to recursively check for Data objects.</p>"}, "Classes/SocketRawAckView.html": {"name": "SocketRawAckView", "abstract": "<p>Class that gives a backwards compatible way to cause an emit not to recursively check for Data objects.</p>"}, "Classes/SocketEngine.html": {"name": "SocketEngine", "abstract": "<p>The class that handles the engine.io protocol and transports."}, "Classes/SocketManager.html": {"name": "SocketManager", "abstract": "<p>A manager for a socket.io connection.</p>"}, "15to16.html": {"name": "15to16"}, "faq.html": {"name": "FAQ"}, "12to13.html": {"name": "12to13"}, "Guides.html": {"name": "Guides", "abstract": "<p>The following guides are available globally.</p>"}, "Classes.html": {"name": "Classes", "abstract": "<p>The following classes are available globally.</p>"}, "Enums.html": {"name": "Enumerations", "abstract": "<p>The following enumerations are available globally.</p>"}, "Extensions.html": {"name": "Extensions", "abstract": "<p>The following extensions are available globally.</p>"}, "Protocols.html": {"name": "Protocols", "abstract": "<p>The following protocols are available globally.</p>"}, "Structs.html": {"name": "Structures", "abstract": "<p>The following structures are available globally.</p>"}, "Typealiases.html": {"name": "Type Aliases", "abstract": "<p>The following type aliases are available globally.</p>"}}