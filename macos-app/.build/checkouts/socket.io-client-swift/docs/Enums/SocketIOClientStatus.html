<!DOCTYPE html>
<html lang="en">
  <head>
    <title>SocketIOClientStatus Enumeration Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Enum/SocketIOClientStatus" class="dashAnchor"></a>

    <a title="SocketIOClientStatus Enumeration Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          SocketIO Docs
        </a>
         (100% documented)
      </p>
    
      <p class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </p>
    
    
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">SocketIO Reference</a>
      <img class="carat" src="../img/carat.png" />
      SocketIOClientStatus Enumeration Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Guides.html">Guides</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../faq.html">FAQ</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/OnAckCallback.html">OnAckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SSLSecurity.html">SSLSecurity</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAckEmitter.html">SocketAckEmitter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAnyEvent.html">SocketAnyEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketClientManager.html">SocketClientManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketEngine.html">SocketEngine</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketIOClient.html">SocketIOClient</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketAckStatus.html">SocketAckStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketClientEvent.html">SocketClientEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketEnginePacketType.html">SocketEnginePacketType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOClientOption.html">SocketIOClientOption</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOClientStatus.html">SocketIOClientStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketParsableError.html">SocketParsableError</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketData.html">SocketData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineClient.html">SocketEngineClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEnginePollable.html">SocketEnginePollable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineWebsocket.html">SocketEngineWebsocket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketIOClientSpec.html">SocketIOClientSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketLogger.html">SocketLogger</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketParsable.html">SocketParsable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketEventHandler.html">SocketEventHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketIOClientConfiguration.html">SocketIOClientConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket.html">SocketPacket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket/PacketType.html">– PacketType</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO11AckCallbacka">AckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content">
            <h1>SocketIOClientStatus</h1>
              <div class="declaration">
                <div class="language">
                  <pre class="highlight"><code><span class="kd">@objc</span> <span class="kd">public</span> <span class="kd">enum</span> <span class="kt">SocketIOClientStatus</span> <span class="p">:</span> <span class="kt">Int</span></code></pre>

                </div>
              </div>
            <p>Represents the state of the client.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusNotConnected"></a>
                    <a name="//apple_ref/swift/Element/notConnected" class="dashAnchor"></a>
                    <a class="token" href="#/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusNotConnected">notConnected</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The client has never been connected. Or the client has been reset.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight"><code><span class="k">case</span> <span class="n">notConnected</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusDisconnected"></a>
                    <a name="//apple_ref/swift/Element/disconnected" class="dashAnchor"></a>
                    <a class="token" href="#/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusDisconnected">disconnected</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The client was once connected, but not anymore.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight"><code><span class="k">case</span> <span class="n">disconnected</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusConnecting"></a>
                    <a name="//apple_ref/swift/Element/connecting" class="dashAnchor"></a>
                    <a class="token" href="#/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusConnecting">connecting</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The client is in the process of connecting.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight"><code><span class="k">case</span> <span class="n">connecting</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusConnected"></a>
                    <a name="//apple_ref/swift/Element/connected" class="dashAnchor"></a>
                    <a class="token" href="#/c:@M@SocketIO@E@SocketIOClientStatus@SocketIOClientStatusConnected">connected</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The client is currently connected.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight"><code><span class="k">case</span> <span class="n">connected</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2017 <a class="link" href="https://github.com/socketio/socket.io-client-swift" target="_blank" rel="external">Erik</a>. All rights reserved. (Last updated: 2017-10-05)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external">jazzy ♪♫ v0.8.4</a>, a <a class="link" href="http://realm.io" target="_blank" rel="external">Realm</a> project.</p>
    </section>
  </body>
</div>
</html>
