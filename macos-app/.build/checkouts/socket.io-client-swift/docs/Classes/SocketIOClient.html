<!DOCTYPE html>
<html lang="en">
  <head>
    <title>SocketIOClient Class Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Class/SocketIOClient" class="dashAnchor"></a>

    <a title="SocketIOClient Class Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          SocketIO 16.0.0 Docs
        </a>
         (100% documented)
      </p>
    
      <p class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </p>
    
    
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">SocketIO Reference</a>
      <img class="carat" src="../img/carat.png" />
      SocketIOClient Class Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Guides.html">Guides</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../12to13.html">12to13</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../15to16.html">15to16</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../faq.html">FAQ</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/OnAckCallback.html">OnAckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAckEmitter.html">SocketAckEmitter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAnyEvent.html">SocketAnyEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketEngine.html">SocketEngine</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketIOClient.html">SocketIOClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketManager.html">SocketManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawAckView.html">SocketRawAckView</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawView.html">SocketRawView</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketAckStatus.html">SocketAckStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketClientEvent.html">SocketClientEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketEnginePacketType.html">SocketEnginePacketType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOClientOption.html">SocketIOClientOption</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOStatus.html">SocketIOStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOVersion.html">SocketIOVersion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketParsableError.html">SocketParsableError</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sb">Bool</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation4DataV">Data</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SD">Dictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sd">Double</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Si">Int</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSArray">NSArray</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSData">NSData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSDictionary">NSDictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSNull">NSNull</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSString">NSString</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SS">String</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ConfigSettable.html">ConfigSettable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketData.html">SocketData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketDataBufferable.html">SocketDataBufferable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineClient.html">SocketEngineClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEnginePollable.html">SocketEnginePollable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineWebsocket.html">SocketEngineWebsocket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketIOClientSpec.html">SocketIOClientSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketLogger.html">SocketLogger</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketManagerSpec.html">SocketManagerSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketParsable.html">SocketParsable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketEventHandler.html">SocketEventHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketIOClientConfiguration.html">SocketIOClientConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket.html">SocketPacket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket/PacketType.html">– PacketType</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO11AckCallbacka">AckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO4Posta">Post</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>SocketIOClient</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">SocketIOClient</span> <span class="p">:</span> <span class="kt">NSObject</span><span class="p">,</span> <span class="kt"><a href="../Protocols/SocketIOClientSpec.html">SocketIOClientSpec</a></span></code></pre>

                </div>
              </div>
            <p>Represents a socket.io-client.</p>

<p>Clients are created through a <code><a href="../Classes/SocketManager.html">SocketManager</a></code>, which owns the <code><a href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a></code> that controls the connection to the server.</p>

<p>For example:</p>
<pre class="highlight swift"><code><span class="c1">// Create a socket for the /swift namespace</span>
<span class="k">let</span> <span class="nv">socket</span> <span class="o">=</span> <span class="n">manager</span><span class="o">.</span><span class="nf">socket</span><span class="p">(</span><span class="nv">forNamespace</span><span class="p">:</span> <span class="s">"/swift"</span><span class="p">)</span>

<span class="c1">// Add some handlers and connect</span>
</code></pre>

<p><strong>NOTE</strong>: The client is not thread/queue safe, all interaction with the socket should be done on the <code>manager.handleQueue</code></p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Properties"></a>
                <a name="//apple_ref/swift/Section/Properties" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Properties"></a>
                  <h3 class="section-name"><p>Properties</p>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC3nspSSvp"></a>
                    <a name="//apple_ref/swift/Property/nsp" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC3nspSSvp">nsp</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The namespace that this socket is currently connected to.</p>

<p><strong>Must</strong> start with a <code>/</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">nsp</span><span class="p">:</span> <span class="kt">String</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC10anyHandleryAA0A8AnyEventCcSgvp"></a>
                    <a name="//apple_ref/swift/Property/anyHandler" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC10anyHandleryAA0A8AnyEventCcSgvp">anyHandler</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A handler that will be called on any event.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">private(set)</span> <span class="k">var</span> <span class="nv">anyHandler</span><span class="p">:</span> <span class="p">((</span><span class="kt"><a href="../Classes/SocketAnyEvent.html">SocketAnyEvent</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="p">())?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC8handlersSayAA0A12EventHandlerVGvp"></a>
                    <a name="//apple_ref/swift/Property/handlers" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC8handlersSayAA0A12EventHandlerVGvp">handlers</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The array of handlers for this socket.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">private(set)</span> <span class="k">var</span> <span class="nv">handlers</span><span class="p">:</span> <span class="p">[</span><span class="kt"><a href="../Structs/SocketEventHandler.html">SocketEventHandler</a></span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC7managerAA0A11ManagerSpec_pSgvp"></a>
                    <a name="//apple_ref/swift/Property/manager" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC7managerAA0A11ManagerSpec_pSgvp">manager</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The manager for this socket.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">private(set)</span> <span class="k">weak</span> <span class="k">var</span> <span class="nv">manager</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketManagerSpec.html">SocketManagerSpec</a></span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC11rawEmitViewAA0a3RawF0Cvp"></a>
                    <a name="//apple_ref/swift/Property/rawEmitView" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC11rawEmitViewAA0a3RawF0Cvp">rawEmitView</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A view into this socket where emits do not check for binary data.</p>

<p>Usage:</p>
<pre class="highlight swift"><code><span class="n">socket</span><span class="o">.</span><span class="n">rawEmitView</span><span class="o">.</span><span class="nf">emit</span><span class="p">(</span><span class="s">"myEvent"</span><span class="p">,</span> <span class="n">myObject</span><span class="p">)</span>
</code></pre>

<p><strong>NOTE</strong>: It is not safe to hold on to this view beyond the life of the socket.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">private(set)</span> <span class="kd">lazy</span> <span class="k">var</span> <span class="nv">rawEmitView</span><span class="p">:</span> <span class="kt"><a href="../Classes/SocketRawView.html">SocketRawView</a></span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC6statusAA0A8IOStatusOvp"></a>
                    <a name="//apple_ref/swift/Property/status" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC6statusAA0A8IOStatusOvp">status</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The status of this client.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">private(set)</span> <span class="k">var</span> <span class="nv">status</span><span class="p">:</span> <span class="kt"><a href="../Enums/SocketIOStatus.html">SocketIOStatus</a></span> <span class="p">{</span> <span class="k">get</span> <span class="k">set</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC3sidSSSgvp"></a>
                    <a name="//apple_ref/swift/Property/sid" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC3sidSSSgvp">sid</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The id of this socket.io connect. This is different from the sid of the engine.io connection.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">private(set)</span> <span class="k">var</span> <span class="nv">sid</span><span class="p">:</span> <span class="kt">String</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Initializers"></a>
                <a name="//apple_ref/swift/Section/Initializers" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Initializers"></a>
                  <h3 class="section-name"><p>Initializers</p>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC7manager3nspAcA0A11ManagerSpec_p_SStcfc"></a>
                    <a name="//apple_ref/swift/Method/init(manager:nsp:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC7manager3nspAcA0A11ManagerSpec_p_SStcfc">init(manager:<wbr>nsp:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Type safe way to create a new SocketIOClient. <code>opts</code> can be omitted.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="nf">init</span><span class="p">(</span><span class="nv">manager</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketManagerSpec.html">SocketManagerSpec</a></span><span class="p">,</span> <span class="nv">nsp</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>manager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The manager for this socket.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>nsp</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The namespace of the socket.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Methods"></a>
                <a name="//apple_ref/swift/Section/Methods" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Methods"></a>
                  <h3 class="section-name"><p>Methods</p>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC7connect11withPayloadySDySSypGSg_tF"></a>
                    <a name="//apple_ref/swift/Method/connect(withPayload:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC7connect11withPayloadySDySSypGSg_tF">connect(withPayload:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Connect to the server. The same as calling <code>connect(timeoutAfter:withHandler:)</code> with a timeout of 0.</p>

<p>Only call after adding your event listeners, unless you know what you&rsquo;re doing.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">connect</span><span class="p">(</span><span class="n">withPayload</span> <span class="nv">payload</span><span class="p">:</span> <span class="p">[</span><span class="kt">String</span> <span class="p">:</span> <span class="kt">Any</span><span class="p">]?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>withPayload</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>An optional payload sent on connect</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC7connect11withPayload12timeoutAfter0E7HandlerySDySSypGSg_SdyycSgtF"></a>
                    <a name="//apple_ref/swift/Method/connect(withPayload:timeoutAfter:withHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC7connect11withPayload12timeoutAfter0E7HandlerySDySSypGSg_SdyycSgtF">connect(withPayload:<wbr>timeoutAfter:<wbr>withHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Connect to the server. If we aren&rsquo;t connected after <code>timeoutAfter</code> seconds, then <code>withHandler</code> is called.</p>

<p>Only call after adding your event listeners, unless you know what you&rsquo;re doing.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">connect</span><span class="p">(</span><span class="n">withPayload</span> <span class="nv">payload</span><span class="p">:</span> <span class="p">[</span><span class="kt">String</span> <span class="p">:</span> <span class="kt">Any</span><span class="p">]?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span> <span class="nv">timeoutAfter</span><span class="p">:</span> <span class="kt">Double</span><span class="p">,</span> <span class="n">withHandler</span> <span class="nv">handler</span><span class="p">:</span> <span class="p">(()</span> <span class="o">-&gt;</span> <span class="p">())?)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>withPayload</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>An optional payload sent on connect</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>timeoutAfter</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The number of seconds after which if we are not connected we assume the connection
                      has failed. Pass 0 to never timeout.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The handler to call when the client fails to connect.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC10didConnect11toNamespace7payloadySS_SDySSypGSgtF"></a>
                    <a name="//apple_ref/swift/Method/didConnect(toNamespace:payload:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC10didConnect11toNamespace7payloadySS_SDySSypGSgtF">didConnect(toNamespace:<wbr>payload:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Called when the client connects to a namespace. If the client was created with a namespace upfront,
then this is only called when the client connects to that namespace.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">didConnect</span><span class="p">(</span><span class="n">toNamespace</span> <span class="nv">namespace</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="nv">payload</span><span class="p">:</span> <span class="p">[</span><span class="kt">String</span> <span class="p">:</span> <span class="kt">Any</span><span class="p">]?)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>toNamespace</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The namespace that was connected to.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC13didDisconnect6reasonySS_tF"></a>
                    <a name="//apple_ref/swift/Method/didDisconnect(reason:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC13didDisconnect6reasonySS_tF">didDisconnect(reason:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Called when the client has disconnected from socket.io.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">didDisconnect</span><span class="p">(</span><span class="nv">reason</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>reason</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The reason for the disconnection.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC10disconnectyyF"></a>
                    <a name="//apple_ref/swift/Method/disconnect()" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC10disconnectyyF">disconnect()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Disconnects the socket.</p>

<p>This will cause the socket to leave the namespace it is associated to, as well as remove itself from the
<code><a href="../Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC7managerAA0A11ManagerSpec_pSgvp">manager</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">disconnect</span><span class="p">()</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC4emit__10completionySS_AA0A4Data_pdyycSgtF"></a>
                    <a name="//apple_ref/swift/Method/emit(_:_:completion:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC4emit__10completionySS_AA0A4Data_pdyycSgtF">emit(_:<wbr>_:<wbr>completion:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Send an event to the server, with optional data items and optional write completion handler.</p>

<p>If an error occurs trying to transform <code>items</code> into their socket representation, a <code><a href="../Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO5erroryA2CmF">SocketClientEvent.error</a></code>
will be emitted. The structure of the error data is <code>[eventName, items, theError]</code></p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">emit</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="n">_</span> <span class="nv">items</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketData.html">SocketData</a></span><span class="o">...</span><span class="p">,</span> <span class="nv">completion</span><span class="p">:</span> <span class="p">(()</span> <span class="o">-&gt;</span> <span class="p">())?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to send.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>items</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The items to send with this event. May be left out.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completion</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Callback called on transport write completion.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC11emitWithAckyAA02OnF8CallbackCSS_AA0A4Data_pdtF"></a>
                    <a name="//apple_ref/swift/Method/emitWithAck(_:_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC11emitWithAckyAA02OnF8CallbackCSS_AA0A4Data_pdtF">emitWithAck(_:<wbr>_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sends a message to the server, requesting an ack.</p>

<p><strong>NOTE</strong>: It is up to the server send an ack back, just calling this method does not mean the server will ack.
Check that your server&rsquo;s api will ack the event being sent.</p>

<p>If an error occurs trying to transform <code>items</code> into their socket representation, a <code><a href="../Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO5erroryA2CmF">SocketClientEvent.error</a></code>
will be emitted. The structure of the error data is <code>[eventName, items, theError]</code></p>

<p>Example:</p>
<pre class="highlight swift"><code><span class="n">socket</span><span class="o">.</span><span class="nf">emitWithAck</span><span class="p">(</span><span class="s">"myEvent"</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span><span class="o">.</span><span class="nf">timingOut</span><span class="p">(</span><span class="nv">after</span><span class="p">:</span> <span class="mi">1</span><span class="p">)</span> <span class="p">{</span><span class="n">data</span> <span class="k">in</span>
    <span class="o">...</span>
<span class="p">}</span>
</code></pre>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">emitWithAck</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="n">_</span> <span class="nv">items</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketData.html">SocketData</a></span><span class="o">...</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/OnAckCallback.html">OnAckCallback</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to send.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>items</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The items to send with this event. May be left out.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>An <code><a href="../Classes/OnAckCallback.html">OnAckCallback</a></code>. You must call the <code>timingOut(after:)</code> method before the event will be sent.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC7emitAck_4withySi_SayypGtF"></a>
                    <a name="//apple_ref/swift/Method/emitAck(_:with:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC7emitAck_4withySi_SayypGtF">emitAck(_:<wbr>with:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Call when you wish to tell the server that you&rsquo;ve received the event for <code>ack</code>.</p>

<p><strong>You shouldn&rsquo;t need to call this directly.</strong> Instead use an <code><a href="../Classes/SocketAckEmitter.html">SocketAckEmitter</a></code> that comes in an event callback.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">emitAck</span><span class="p">(</span><span class="n">_</span> <span class="nv">ack</span><span class="p">:</span> <span class="kt">Int</span><span class="p">,</span> <span class="n">with</span> <span class="nv">items</span><span class="p">:</span> <span class="p">[</span><span class="kt">Any</span><span class="p">])</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>ack</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The ack number.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>with</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The data for this ack.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC9handleAck_4dataySi_SayypGtF"></a>
                    <a name="//apple_ref/swift/Method/handleAck(_:data:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC9handleAck_4dataySi_SayypGtF">handleAck(_:<wbr>data:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Called when socket.io has acked one of our emits. Causes the corresponding ack callback to be called.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">handleAck</span><span class="p">(</span><span class="n">_</span> <span class="nv">ack</span><span class="p">:</span> <span class="kt">Int</span><span class="p">,</span> <span class="nv">data</span><span class="p">:</span> <span class="p">[</span><span class="kt">Any</span><span class="p">])</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>ack</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The number for this ack.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The data sent back with this ack.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC17handleClientEvent_4datayAA0aeF0O_SayypGtF"></a>
                    <a name="//apple_ref/swift/Method/handleClientEvent(_:data:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC17handleClientEvent_4datayAA0aeF0O_SayypGtF">handleClientEvent(_:<wbr>data:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Called on socket.io specific events.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">handleClientEvent</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt"><a href="../Enums/SocketClientEvent.html">SocketClientEvent</a></span><span class="p">,</span> <span class="nv">data</span><span class="p">:</span> <span class="p">[</span><span class="kt">Any</span><span class="p">])</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Enums/SocketClientEvent.html">SocketClientEvent</a></code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The data for this event.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC11handleEvent_4data17isInternalMessage7withAckySS_SayypGSbSitF"></a>
                    <a name="//apple_ref/swift/Method/handleEvent(_:data:isInternalMessage:withAck:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC11handleEvent_4data17isInternalMessage7withAckySS_SayypGSbSitF">handleEvent(_:<wbr>data:<wbr>isInternalMessage:<wbr>withAck:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Called when we get an event from socket.io.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">handleEvent</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="nv">data</span><span class="p">:</span> <span class="p">[</span><span class="kt">Any</span><span class="p">],</span> <span class="nv">isInternalMessage</span><span class="p">:</span> <span class="kt">Bool</span><span class="p">,</span> <span class="n">withAck</span> <span class="nv">ack</span><span class="p">:</span> <span class="kt">Int</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The name of the event.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The data that was sent with this event.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>isInternalMessage</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Whether this event was sent internally. If <code>true</code> it is always sent to handlers.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>ack</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>If &gt; 0 then this event expects to get an ack back from the client.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC12handlePacketyyAA0aE0VF"></a>
                    <a name="//apple_ref/swift/Method/handlePacket(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC12handlePacketyyAA0aE0VF">handlePacket(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Causes a client to handle a socket.io packet. The namespace for the packet must match the namespace of the
socket.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">handlePacket</span><span class="p">(</span><span class="n">_</span> <span class="nv">packet</span><span class="p">:</span> <span class="kt"><a href="../Structs/SocketPacket.html">SocketPacket</a></span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>packet</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The packet to handle.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC14leaveNamespaceyyF"></a>
                    <a name="//apple_ref/swift/Method/leaveNamespace()" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC14leaveNamespaceyyF">leaveNamespace()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Call when you wish to leave a namespace and disconnect this socket.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">leaveNamespace</span><span class="p">()</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC13joinNamespace11withPayloadySDySSypGSg_tF"></a>
                    <a name="//apple_ref/swift/Method/joinNamespace(withPayload:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC13joinNamespace11withPayloadySDySSypGSg_tF">joinNamespace(withPayload:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Joins <code><a href="../Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3nspSSvp">nsp</a></code>. You shouldn&rsquo;t need to call this directly, instead call <code>connect</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">joinNamespace</span><span class="p">(</span><span class="n">withPayload</span> <span class="nv">payload</span><span class="p">:</span> <span class="p">[</span><span class="kt">String</span> <span class="p">:</span> <span class="kt">Any</span><span class="p">]?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>withPayload</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>An optional payload sent on connect</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC3off11clientEventyAA0a6ClientF0O_tF"></a>
                    <a name="//apple_ref/swift/Method/off(clientEvent:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC3off11clientEventyAA0a6ClientF0O_tF">off(clientEvent:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Removes handler(s) for a client event.</p>

<p>If you wish to remove a client event handler, call the <code><a href="../Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3off2idy10Foundation4UUIDV_tF">off(id:)</a></code> with the UUID received from its <code>on</code> call.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">off</span><span class="p">(</span><span class="n">clientEvent</span> <span class="nv">event</span><span class="p">:</span> <span class="kt"><a href="../Enums/SocketClientEvent.html">SocketClientEvent</a></span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>clientEvent</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to remove handlers for.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC3offyySSF"></a>
                    <a name="//apple_ref/swift/Method/off(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC3offyySSF">off(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Removes handler(s) based on an event name.</p>

<p>If you wish to remove a specific event, call the <code><a href="../Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3off2idy10Foundation4UUIDV_tF">off(id:)</a></code> with the UUID received from its <code>on</code> call.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">off</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to remove handlers for.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC3off2idy10Foundation4UUIDV_tF"></a>
                    <a name="//apple_ref/swift/Method/off(id:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC3off2idy10Foundation4UUIDV_tF">off(id:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Removes a handler with the specified UUID gotten from an <code>on</code> or <code>once</code></p>

<p>If you want to remove all events for an event, call the off <code><a href="../Classes/SocketIOClient.html#/s:8SocketIO0A8IOClientC3offyySSF">off(_:)</a></code> method with the event name.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">off</span><span class="p">(</span><span class="nv">id</span><span class="p">:</span> <span class="kt">UUID</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>id</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The UUID of the handler you wish to remove.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC2on_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF"></a>
                    <a name="//apple_ref/swift/Method/on(_:callback:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC2on_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF">on(_:<wbr>callback:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler for an event.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">open</span> <span class="kd">func</span> <span class="nf">on</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="nv">callback</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">UUID</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event name for this handler.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>callback</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The callback that will execute when this event is received.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>A unique id for the handler that can be used to remove it.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC2on11clientEvent8callback10Foundation4UUIDVAA0a6ClientF0O_ySayypG_AA0A10AckEmitterCtctF"></a>
                    <a name="//apple_ref/swift/Method/on(clientEvent:callback:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC2on11clientEvent8callback10Foundation4UUIDVAA0a6ClientF0O_ySayypG_AA0A10AckEmitterCtctF">on(clientEvent:<wbr>callback:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler for a client event.</p>

<p>Example:</p>
<pre class="highlight swift"><code><span class="n">socket</span><span class="o">.</span><span class="nf">on</span><span class="p">(</span><span class="nv">clientEvent</span><span class="p">:</span> <span class="o">.</span><span class="n">connect</span><span class="p">)</span> <span class="p">{</span><span class="n">data</span><span class="p">,</span> <span class="n">ack</span> <span class="k">in</span>
    <span class="o">...</span>
<span class="p">}</span>
</code></pre>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">open</span> <span class="kd">func</span> <span class="nf">on</span><span class="p">(</span><span class="n">clientEvent</span> <span class="nv">event</span><span class="p">:</span> <span class="kt"><a href="../Enums/SocketClientEvent.html">SocketClientEvent</a></span><span class="p">,</span> <span class="nv">callback</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">UUID</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event for this handler.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>callback</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The callback that will execute when this event is received.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>A unique id for the handler that can be used to remove it.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC4once11clientEvent8callback10Foundation4UUIDVAA0a6ClientF0O_ySayypG_AA0A10AckEmitterCtctF"></a>
                    <a name="//apple_ref/swift/Method/once(clientEvent:callback:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC4once11clientEvent8callback10Foundation4UUIDVAA0a6ClientF0O_ySayypG_AA0A10AckEmitterCtctF">once(clientEvent:<wbr>callback:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a single-use handler for a client event.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">open</span> <span class="kd">func</span> <span class="nf">once</span><span class="p">(</span><span class="n">clientEvent</span> <span class="nv">event</span><span class="p">:</span> <span class="kt"><a href="../Enums/SocketClientEvent.html">SocketClientEvent</a></span><span class="p">,</span> <span class="nv">callback</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">UUID</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>clientEvent</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event for this handler.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>callback</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The callback that will execute when this event is received.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>A unique id for the handler that can be used to remove it.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC4once_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF"></a>
                    <a name="//apple_ref/swift/Method/once(_:callback:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC4once_8callback10Foundation4UUIDVSS_ySayypG_AA0A10AckEmitterCtctF">once(_:<wbr>callback:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a single-use handler for an event.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">open</span> <span class="kd">func</span> <span class="nf">once</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="nv">callback</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">UUID</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event name for this handler.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>callback</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The callback that will execute when this event is received.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>A unique id for the handler that can be used to remove it.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC5onAnyyyyAA0aE5EventCcF"></a>
                    <a name="//apple_ref/swift/Method/onAny(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC5onAnyyyyAA0aE5EventCcF">onAny(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler that will be called on every event.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">onAny</span><span class="p">(</span><span class="n">_</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="p">(</span><span class="kt"><a href="../Classes/SocketAnyEvent.html">SocketAnyEvent</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="p">())</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The callback that will execute whenever an event is received.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC17removeAllHandlersyyF"></a>
                    <a name="//apple_ref/swift/Method/removeAllHandlers()" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC17removeAllHandlersyyF">removeAllHandlers()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Removes all handlers.</p>

<p>Can be used after disconnecting to break any potential remaining retain cycles.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">removeAllHandlers</span><span class="p">()</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A8IOClientC15setReconnecting6reasonySS_tF"></a>
                    <a name="//apple_ref/swift/Method/setReconnecting(reason:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A8IOClientC15setReconnecting6reasonySS_tF">setReconnecting(reason:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Puts the socket back into the connecting state.
Called when the manager detects a broken connection, or when a manual reconnect is triggered.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">setReconnecting</span><span class="p">(</span><span class="nv">reason</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>reason</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The reason this socket is reconnecting.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2021 <a class="link" href="https://github.com/socketio/socket.io-client-swift" target="_blank" rel="external">Erik</a>. All rights reserved. (Last updated: 2021-02-01)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external">jazzy ♪♫ v0.13.6</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external">Realm</a> project.</p>
    </section>
  </body>
</div>
</html>
