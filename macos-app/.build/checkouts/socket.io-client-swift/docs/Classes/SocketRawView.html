<!DOCTYPE html>
<html lang="en">
  <head>
    <title>SocketRawView Class Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Class/SocketRawView" class="dashAnchor"></a>

    <a title="SocketRawView Class Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          SocketIO 16.0.0 Docs
        </a>
         (100% documented)
      </p>
    
      <p class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </p>
    
    
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">SocketIO Reference</a>
      <img class="carat" src="../img/carat.png" />
      SocketRawView Class Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Guides.html">Guides</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../12to13.html">12to13</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../15to16.html">15to16</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../faq.html">FAQ</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/OnAckCallback.html">OnAckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAckEmitter.html">SocketAckEmitter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketAnyEvent.html">SocketAnyEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketEngine.html">SocketEngine</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketIOClient.html">SocketIOClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketManager.html">SocketManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawAckView.html">SocketRawAckView</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SocketRawView.html">SocketRawView</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketAckStatus.html">SocketAckStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketClientEvent.html">SocketClientEvent</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketEnginePacketType.html">SocketEnginePacketType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOClientOption.html">SocketIOClientOption</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOStatus.html">SocketIOStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketIOVersion.html">SocketIOVersion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/SocketParsableError.html">SocketParsableError</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sb">Bool</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation4DataV">Data</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SD">Dictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sd">Double</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Si">Int</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSArray">NSArray</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSData">NSData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSDictionary">NSDictionary</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSNull">NSNull</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSString">NSString</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:SS">String</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ConfigSettable.html">ConfigSettable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketData.html">SocketData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketDataBufferable.html">SocketDataBufferable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineClient.html">SocketEngineClient</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEnginePollable.html">SocketEnginePollable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineSpec.html">SocketEngineSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketEngineWebsocket.html">SocketEngineWebsocket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketIOClientSpec.html">SocketIOClientSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketLogger.html">SocketLogger</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketManagerSpec.html">SocketManagerSpec</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/SocketParsable.html">SocketParsable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketEventHandler.html">SocketEventHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketIOClientConfiguration.html">SocketIOClientConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket.html">SocketPacket</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/SocketPacket/PacketType.html">– PacketType</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO11AckCallbacka">AckCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO14NormalCallbacka">NormalCallback</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:8SocketIO4Posta">Post</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>SocketRawView</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">SocketRawView</span> <span class="p">:</span> <span class="kt">NSObject</span></code></pre>

                </div>
              </div>
            <p>Class that gives a backwards compatible way to cause an emit not to recursively check for Data objects.</p>

<p>Usage:</p>
<pre class="highlight swift"><code><span class="n">socket</span><span class="o">.</span><span class="n">rawEmitView</span><span class="o">.</span><span class="nf">emit</span><span class="p">(</span><span class="s">"myEvent"</span><span class="p">,</span> <span class="n">myObject</span><span class="p">)</span>
</code></pre>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A7RawViewC4emityySS_AA0A4Data_pdtF"></a>
                    <a name="//apple_ref/swift/Method/emit(_:_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A7RawViewC4emityySS_AA0A4Data_pdtF">emit(_:<wbr>_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Send an event to the server, with optional data items.</p>

<p>If an error occurs trying to transform <code>items</code> into their socket representation, a <code><a href="../Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO5erroryA2CmF">SocketClientEvent.error</a></code>
will be emitted. The structure of the error data is <code>[eventName, items, theError]</code></p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">emit</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="n">_</span> <span class="nv">items</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketData.html">SocketData</a></span><span class="o">...</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to send.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>items</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The items to send with this event. May be left out.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@M@SocketIO@objc(cs)SocketRawView(im)emit:with:"></a>
                    <a name="//apple_ref/swift/Method/emit(_:with:)" class="dashAnchor"></a>
                    <a class="token" href="#/c:@M@SocketIO@objc(cs)SocketRawView(im)emit:with:">emit(_:<wbr>with:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Same as emit, but meant for Objective-C</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@objc</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">emit</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="n">with</span> <span class="nv">items</span><span class="p">:</span> <span class="p">[</span><span class="kt">Any</span><span class="p">])</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to send.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>items</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The items to send with this event. Send an empty array to send no data.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:8SocketIO0A7RawViewC11emitWithAckyAA02OnG8CallbackCSS_AA0A4Data_pdtF"></a>
                    <a name="//apple_ref/swift/Method/emitWithAck(_:_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:8SocketIO0A7RawViewC11emitWithAckyAA02OnG8CallbackCSS_AA0A4Data_pdtF">emitWithAck(_:<wbr>_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sends a message to the server, requesting an ack.</p>

<p><strong>NOTE</strong>: It is up to the server send an ack back, just calling this method does not mean the server will ack.
Check that your server&rsquo;s api will ack the event being sent.</p>

<p>If an error occurs trying to transform <code>items</code> into their socket representation, a <code><a href="../Enums/SocketClientEvent.html#/s:8SocketIO0A11ClientEventO5erroryA2CmF">SocketClientEvent.error</a></code>
will be emitted. The structure of the error data is <code>[eventName, items, theError]</code></p>

<p>Example:</p>
<pre class="highlight swift"><code><span class="n">socket</span><span class="o">.</span><span class="nf">emitWithAck</span><span class="p">(</span><span class="s">"myEvent"</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span><span class="o">.</span><span class="nf">timingOut</span><span class="p">(</span><span class="nv">after</span><span class="p">:</span> <span class="mi">1</span><span class="p">)</span> <span class="p">{</span><span class="n">data</span> <span class="k">in</span>
    <span class="o">...</span>
<span class="p">}</span>
</code></pre>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">emitWithAck</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="n">_</span> <span class="nv">items</span><span class="p">:</span> <span class="kt"><a href="../Protocols/SocketData.html">SocketData</a></span><span class="o">...</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/OnAckCallback.html">OnAckCallback</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to send.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>items</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The items to send with this event. May be left out.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>An <code><a href="../Classes/OnAckCallback.html">OnAckCallback</a></code>. You must call the <code>timingOut(after:)</code> method before the event will be sent.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@M@SocketIO@objc(cs)SocketRawView(im)emitWithAck:with:"></a>
                    <a name="//apple_ref/swift/Method/emitWithAck(_:with:)" class="dashAnchor"></a>
                    <a class="token" href="#/c:@M@SocketIO@objc(cs)SocketRawView(im)emitWithAck:with:">emitWithAck(_:<wbr>with:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Same as emitWithAck, but for Objective-C</p>

<p><strong>NOTE</strong>: It is up to the server send an ack back, just calling this method does not mean the server will ack.
Check that your server&rsquo;s api will ack the event being sent.</p>

<p>Example:</p>
<pre class="highlight swift"><code><span class="n">socket</span><span class="o">.</span><span class="nf">emitWithAck</span><span class="p">(</span><span class="s">"myEvent"</span><span class="p">,</span> <span class="nv">with</span><span class="p">:</span> <span class="p">[</span><span class="mi">1</span><span class="p">])</span><span class="o">.</span><span class="nf">timingOut</span><span class="p">(</span><span class="nv">after</span><span class="p">:</span> <span class="mi">1</span><span class="p">)</span> <span class="p">{</span><span class="n">data</span> <span class="k">in</span>
    <span class="o">...</span>
<span class="p">}</span>
</code></pre>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@objc</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">emitWithAck</span><span class="p">(</span><span class="n">_</span> <span class="nv">event</span><span class="p">:</span> <span class="kt">String</span><span class="p">,</span> <span class="n">with</span> <span class="nv">items</span><span class="p">:</span> <span class="p">[</span><span class="kt">Any</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/OnAckCallback.html">OnAckCallback</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>event</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The event to send.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>items</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The items to send with this event. Use <code>[]</code> to send nothing.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>An <code><a href="../Classes/OnAckCallback.html">OnAckCallback</a></code>. You must call the <code>timingOut(after:)</code> method before the event will be sent.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2021 <a class="link" href="https://github.com/socketio/socket.io-client-swift" target="_blank" rel="external">Erik</a>. All rights reserved. (Last updated: 2021-02-01)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external">jazzy ♪♫ v0.13.6</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external">Realm</a> project.</p>
    </section>
  </body>
</div>
</html>
