// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1C6572803D7E252A77A86E5F /* SocketManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C65763817782DFAC67BE05C /* SocketManager.swift */; };
		1C6573B22DC9423CDFC32F05 /* SocketRawView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C657533E849FC3E4342C602 /* SocketRawView.swift */; };
		1C657CDE5D510E8E2E573E39 /* utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C6577B639C34EE1C8829D9A /* utils.swift */; };
		1C657FBB3F670261780FD72E /* SocketManagerSpec.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C6574AF9687A213814753E4 /* SocketManagerSpec.swift */; };
		1C686BE21F869AFD007D8627 /* SocketIOClientConfigurationTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C686BD21F869AF1007D8627 /* SocketIOClientConfigurationTest.swift */; };
		1C686BE31F869AFD007D8627 /* SocketEngineTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C686BD31F869AF1007D8627 /* SocketEngineTest.swift */; };
		1C686BE41F869AFD007D8627 /* SocketSideEffectTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C686BD41F869AF1007D8627 /* SocketSideEffectTest.swift */; };
		1C686BE51F869AFD007D8627 /* SocketBasicPacketTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C686BD51F869AF1007D8627 /* SocketBasicPacketTest.swift */; };
		1C686BE61F869AFD007D8627 /* SocketAckManagerTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C686BD61F869AF1007D8627 /* SocketAckManagerTest.swift */; };
		1C686BE71F869AFD007D8627 /* SocketParserTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C686BD71F869AF1007D8627 /* SocketParserTest.swift */; };
		1C686BE81F869AFD007D8627 /* SocketNamespacePacketTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C686BD81F869AF1007D8627 /* SocketNamespacePacketTest.swift */; };
		572EF2431B51F18A00EEBB58 /* SocketIO.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 572EF2381B51F18A00EEBB58 /* SocketIO.framework */; };
		579C7D4C2731B487009F8A2F /* Starscream.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 579C7D4B2731B487009F8A2F /* Starscream.xcframework */; };
		6CA08A981D615C0B0061FD2A /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CA08A971D615C0B0061FD2A /* Security.framework */; };
		74BF53581F894326004972D8 /* SocketIO.h in Headers */ = {isa = PBXBuildFile; fileRef = 572EF23C1B51F18A00EEBB58 /* SocketIO.h */; settings = {ATTRIBUTES = (Public, ); }; };
		74DA21741F09440F009C19EE /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 74DA21731F09440F009C19EE /* libz.tbd */; };
		74DA217C1F09457B009C19EE /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 74DA21731F09440F009C19EE /* libz.tbd */; };
		DD52B048C71D724ABBD18C71 /* SocketTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BDC9E66AADA2CC5E8246 /* SocketTypes.swift */; };
		DD52B11AF936352BAE30B2C8 /* SocketStringReader.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BA240D139F72633D4159 /* SocketStringReader.swift */; };
		DD52B1F8BA0455EBE7C1B93E /* SocketAckEmitter.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BFF2E3216CDC364BB8AF /* SocketAckEmitter.swift */; };
		DD52B1FDEB06B853FF932AC7 /* SocketEnginePacketType.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B4EA17D8C3F35C8E2CB4 /* SocketEnginePacketType.swift */; };
		DD52B2AFE7D46039C7AE4D19 /* SocketIOClientOption.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B078DB0A3C3D1BB507CD /* SocketIOClientOption.swift */; };
		DD52B3A6C1E082841C35C85D /* SocketEngineClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BE5FDCE1D684132E897C /* SocketEngineClient.swift */; };
		DD52B44AE56F2E07F3F3F991 /* SocketAckManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B09F7984E730513AB7E5 /* SocketAckManager.swift */; };
		DD52B4DFA12F2599410205D9 /* SocketEngineWebsocket.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BE9AD8B2BD7F841CD1D4 /* SocketEngineWebsocket.swift */; };
		DD52B56DE03CDB4F40BD1A23 /* SocketExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B471D780013E18DF9335 /* SocketExtensions.swift */; };
		DD52B57E7ABC61B57EE2A4B8 /* SocketPacket.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B59C11D3D2BC63612E50 /* SocketPacket.swift */; };
		DD52B883F942CD5A9D29892B /* SocketEnginePollable.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B2D110F55723F82B108E /* SocketEnginePollable.swift */; };
		DD52B9412F660F828B683422 /* SocketParsable.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B31D0E6815F5F10CEFB6 /* SocketParsable.swift */; };
		DD52BB69B6D260035B652CA4 /* SocketAnyEvent.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B5A9DE10C7A8AD35617F /* SocketAnyEvent.swift */; };
		DD52BB82239886CF6ADD642C /* SocketEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B7A9779A2E08075E5AAC /* SocketEngine.swift */; };
		DD52BB9A3E42FF2DD6BE7C2F /* SocketIOClientSpec.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BCAF915A546288664346 /* SocketIOClientSpec.swift */; };
		DD52BC3F1F880820E8FDFD0C /* SocketLogger.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BED81BF312B0E90E92AC /* SocketLogger.swift */; };
		DD52BCCD25EFA76E0F9B313C /* SocketMangerTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BBAC5FAA7730D32CD5BF /* SocketMangerTest.swift */; };
		DD52BD065B74AC5B77BAEFAA /* SocketIOClientConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B57FFEE8560CFFD793B3 /* SocketIOClientConfiguration.swift */; };
		DD52BE4D1E6BB752CD9614A6 /* SocketIOStatus.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B1D9BC4AE46D38D827DE /* SocketIOStatus.swift */; };
		DD52BF924BEF05E1235CFD29 /* SocketIOClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52BA1F41F2E4B3DC20260E /* SocketIOClient.swift */; };
		DD52BFBC9E7CC32D3515AC80 /* SocketEngineSpec.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B645273A873667BC2D43 /* SocketEngineSpec.swift */; };
		DD52BFEB4DBD3BF8D93DAEFF /* SocketEventHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD52B6DCCBBAC6BE9C22568D /* SocketEventHandler.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		572EF2441B51F18A00EEBB58 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 572EF20E1B51F12F00EEBB58 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 572EF2371B51F18A00EEBB58;
			remoteInfo = "SocketIO-Mac";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1C6574AF9687A213814753E4 /* SocketManagerSpec.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketManagerSpec.swift; sourceTree = "<group>"; };
		1C657533E849FC3E4342C602 /* SocketRawView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketRawView.swift; sourceTree = "<group>"; };
		1C65763817782DFAC67BE05C /* SocketManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketManager.swift; sourceTree = "<group>"; };
		1C6577B639C34EE1C8829D9A /* utils.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = utils.swift; sourceTree = "<group>"; };
		1C686BD21F869AF1007D8627 /* SocketIOClientConfigurationTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SocketIOClientConfigurationTest.swift; sourceTree = "<group>"; };
		1C686BD31F869AF1007D8627 /* SocketEngineTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SocketEngineTest.swift; sourceTree = "<group>"; };
		1C686BD41F869AF1007D8627 /* SocketSideEffectTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SocketSideEffectTest.swift; sourceTree = "<group>"; };
		1C686BD51F869AF1007D8627 /* SocketBasicPacketTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SocketBasicPacketTest.swift; sourceTree = "<group>"; };
		1C686BD61F869AF1007D8627 /* SocketAckManagerTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SocketAckManagerTest.swift; sourceTree = "<group>"; };
		1C686BD71F869AF1007D8627 /* SocketParserTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SocketParserTest.swift; sourceTree = "<group>"; };
		1C686BD81F869AF1007D8627 /* SocketNamespacePacketTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SocketNamespacePacketTest.swift; sourceTree = "<group>"; };
		1C686BFE1F869E9D007D8627 /* SocketObjectiveCTest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SocketObjectiveCTest.m; sourceTree = "<group>"; };
		572EF2381B51F18A00EEBB58 /* SocketIO.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SocketIO.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		572EF23B1B51F18A00EEBB58 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		572EF23C1B51F18A00EEBB58 /* SocketIO.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SocketIO.h; sourceTree = "<group>"; };
		572EF2421B51F18A00EEBB58 /* SocketIO-Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "SocketIO-Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		579C7D4B2731B487009F8A2F /* Starscream.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = Starscream.xcframework; path = Carthage/Build/Starscream.xcframework; sourceTree = "<group>"; };
		6CA08A951D615C040061FD2A /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS9.3.sdk/System/Library/Frameworks/Security.framework; sourceTree = DEVELOPER_DIR; };
		6CA08A971D615C0B0061FD2A /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		6CA08A991D615C140061FD2A /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS9.2.sdk/System/Library/Frameworks/Security.framework; sourceTree = DEVELOPER_DIR; };
		749FA1A11F811408002FBB30 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		74D0F58D1F804FED0037C4DC /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS11.0.sdk/usr/lib/libz.tbd; sourceTree = DEVELOPER_DIR; };
		74DA21731F09440F009C19EE /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		74DA217D1F0945E9009C19EE /* libcommonCrypto.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libcommonCrypto.tbd; path = usr/lib/system/libcommonCrypto.tbd; sourceTree = SDKROOT; };
		9432E0061F77F7CA006AF628 /* SSLSecurity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SSLSecurity.swift; sourceTree = "<group>"; };
		DD52B078DB0A3C3D1BB507CD /* SocketIOClientOption.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketIOClientOption.swift; sourceTree = "<group>"; };
		DD52B09F7984E730513AB7E5 /* SocketAckManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketAckManager.swift; sourceTree = "<group>"; };
		DD52B1D9BC4AE46D38D827DE /* SocketIOStatus.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketIOStatus.swift; sourceTree = "<group>"; };
		DD52B2C54A6ADF3371C13DCB /* SocketObjectiveCTest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SocketObjectiveCTest.h; sourceTree = "<group>"; };
		DD52B2D110F55723F82B108E /* SocketEnginePollable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketEnginePollable.swift; sourceTree = "<group>"; };
		DD52B31D0E6815F5F10CEFB6 /* SocketParsable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketParsable.swift; sourceTree = "<group>"; };
		DD52B471D780013E18DF9335 /* SocketExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketExtensions.swift; sourceTree = "<group>"; };
		DD52B4EA17D8C3F35C8E2CB4 /* SocketEnginePacketType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketEnginePacketType.swift; sourceTree = "<group>"; };
		DD52B57FFEE8560CFFD793B3 /* SocketIOClientConfiguration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketIOClientConfiguration.swift; sourceTree = "<group>"; };
		DD52B59C11D3D2BC63612E50 /* SocketPacket.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketPacket.swift; sourceTree = "<group>"; };
		DD52B5A9DE10C7A8AD35617F /* SocketAnyEvent.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketAnyEvent.swift; sourceTree = "<group>"; };
		DD52B645273A873667BC2D43 /* SocketEngineSpec.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketEngineSpec.swift; sourceTree = "<group>"; };
		DD52B6DCCBBAC6BE9C22568D /* SocketEventHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketEventHandler.swift; sourceTree = "<group>"; };
		DD52B7A9779A2E08075E5AAC /* SocketEngine.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketEngine.swift; sourceTree = "<group>"; };
		DD52B8396C7DEE7BFD6A985A /* ManagerObjectiveCTest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ManagerObjectiveCTest.h; sourceTree = "<group>"; };
		DD52BA1F41F2E4B3DC20260E /* SocketIOClient.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketIOClient.swift; sourceTree = "<group>"; };
		DD52BA240D139F72633D4159 /* SocketStringReader.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketStringReader.swift; sourceTree = "<group>"; };
		DD52BB5E907D283ACC31E17F /* ManagerObjectiveCTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ManagerObjectiveCTest.m; sourceTree = "<group>"; };
		DD52BBAC5FAA7730D32CD5BF /* SocketMangerTest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketMangerTest.swift; sourceTree = "<group>"; };
		DD52BCAF915A546288664346 /* SocketIOClientSpec.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketIOClientSpec.swift; sourceTree = "<group>"; };
		DD52BDC9E66AADA2CC5E8246 /* SocketTypes.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketTypes.swift; sourceTree = "<group>"; };
		DD52BE5FDCE1D684132E897C /* SocketEngineClient.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketEngineClient.swift; sourceTree = "<group>"; };
		DD52BE9AD8B2BD7F841CD1D4 /* SocketEngineWebsocket.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketEngineWebsocket.swift; sourceTree = "<group>"; };
		DD52BED81BF312B0E90E92AC /* SocketLogger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketLogger.swift; sourceTree = "<group>"; };
		DD52BFF2E3216CDC364BB8AF /* SocketAckEmitter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SocketAckEmitter.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		572EF2341B51F18A00EEBB58 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74DA21741F09440F009C19EE /* libz.tbd in Frameworks */,
				579C7D4C2731B487009F8A2F /* Starscream.xcframework in Frameworks */,
				6CA08A981D615C0B0061FD2A /* Security.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		572EF23F1B51F18A00EEBB58 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74DA217C1F09457B009C19EE /* libz.tbd in Frameworks */,
				572EF2431B51F18A00EEBB58 /* SocketIO.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1C657951DEA2E0293D0FD1B6 /* Manager */ = {
			isa = PBXGroup;
			children = (
				1C65763817782DFAC67BE05C /* SocketManager.swift */,
				1C6574AF9687A213814753E4 /* SocketManagerSpec.swift */,
			);
			name = Manager;
			path = Source/SocketIO/Manager;
			sourceTree = "<group>";
		};
		1C686BD11F869AF1007D8627 /* TestSocketIO */ = {
			isa = PBXGroup;
			children = (
				1C686BD21F869AF1007D8627 /* SocketIOClientConfigurationTest.swift */,
				1C686BD31F869AF1007D8627 /* SocketEngineTest.swift */,
				1C686BD41F869AF1007D8627 /* SocketSideEffectTest.swift */,
				1C686BD51F869AF1007D8627 /* SocketBasicPacketTest.swift */,
				1C686BD61F869AF1007D8627 /* SocketAckManagerTest.swift */,
				1C686BD71F869AF1007D8627 /* SocketParserTest.swift */,
				1C686BD81F869AF1007D8627 /* SocketNamespacePacketTest.swift */,
				DD52BBAC5FAA7730D32CD5BF /* SocketMangerTest.swift */,
				1C6577B639C34EE1C8829D9A /* utils.swift */,
			);
			name = TestSocketIO;
			path = Tests/TestSocketIO;
			sourceTree = "<group>";
		};
		1C686BFD1F869E9D007D8627 /* TestSocketIOObjc */ = {
			isa = PBXGroup;
			children = (
				1C686BFE1F869E9D007D8627 /* SocketObjectiveCTest.m */,
				DD52BB5E907D283ACC31E17F /* ManagerObjectiveCTest.m */,
				DD52B8396C7DEE7BFD6A985A /* ManagerObjectiveCTest.h */,
				DD52B2C54A6ADF3371C13DCB /* SocketObjectiveCTest.h */,
			);
			name = TestSocketIOObjc;
			path = Tests/TestSocketIOObjc;
			sourceTree = "<group>";
		};
		572EF20D1B51F12F00EEBB58 = {
			isa = PBXGroup;
			children = (
				1C686BD11F869AF1007D8627 /* TestSocketIO */,
				1C686BFD1F869E9D007D8627 /* TestSocketIOObjc */,
				6CA08A9B1D615C190061FD2A /* Frameworks */,
				572EF21A1B51F16C00EEBB58 /* Products */,
				572EF2391B51F18A00EEBB58 /* SocketIO */,
				5764DF7B1B51F24A004FF46E /* Source */,
			);
			sourceTree = "<group>";
		};
		572EF21A1B51F16C00EEBB58 /* Products */ = {
			isa = PBXGroup;
			children = (
				572EF2381B51F18A00EEBB58 /* SocketIO.framework */,
				572EF2421B51F18A00EEBB58 /* SocketIO-Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		572EF2391B51F18A00EEBB58 /* SocketIO */ = {
			isa = PBXGroup;
			children = (
				572EF23C1B51F18A00EEBB58 /* SocketIO.h */,
				572EF23A1B51F18A00EEBB58 /* Supporting Files */,
			);
			path = SocketIO;
			sourceTree = "<group>";
		};
		572EF23A1B51F18A00EEBB58 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				572EF23B1B51F18A00EEBB58 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		5764DF7B1B51F24A004FF46E /* Source */ = {
			isa = PBXGroup;
			children = (
				DD52BB6512B5400E2A3A39F2 /* Engine */,
				DD52BF863FC00831DC047241 /* Ack */,
				DD52B6A0966AF71393777311 /* Client */,
				DD52B1D10D761CEF3944A6BC /* Util */,
				DD52B647ED881F3FF6EEC617 /* Parse */,
				1C657951DEA2E0293D0FD1B6 /* Manager */,
			);
			name = Source;
			sourceTree = "<group>";
		};
		6CA08A9B1D615C190061FD2A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				749FA1A11F811408002FBB30 /* Foundation.framework */,
				579C7D4B2731B487009F8A2F /* Starscream.xcframework */,
				74D0F58D1F804FED0037C4DC /* libz.tbd */,
				74DA217D1F0945E9009C19EE /* libcommonCrypto.tbd */,
				74DA21731F09440F009C19EE /* libz.tbd */,
				6CA08A9E1D615C340061FD2A /* tvOS */,
				6CA08A9D1D615C2C0061FD2A /* Mac */,
				6CA08A9C1D615C270061FD2A /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6CA08A9C1D615C270061FD2A /* iOS */ = {
			isa = PBXGroup;
			children = (
				6CA08A951D615C040061FD2A /* Security.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		6CA08A9D1D615C2C0061FD2A /* Mac */ = {
			isa = PBXGroup;
			children = (
				6CA08A971D615C0B0061FD2A /* Security.framework */,
			);
			name = Mac;
			sourceTree = "<group>";
		};
		6CA08A9E1D615C340061FD2A /* tvOS */ = {
			isa = PBXGroup;
			children = (
				6CA08A991D615C140061FD2A /* Security.framework */,
			);
			name = tvOS;
			sourceTree = "<group>";
		};
		DD52B1D10D761CEF3944A6BC /* Util */ = {
			isa = PBXGroup;
			children = (
				DD52BDC9E66AADA2CC5E8246 /* SocketTypes.swift */,
				DD52BED81BF312B0E90E92AC /* SocketLogger.swift */,
				DD52B471D780013E18DF9335 /* SocketExtensions.swift */,
				DD52BA240D139F72633D4159 /* SocketStringReader.swift */,
				9432E0061F77F7CA006AF628 /* SSLSecurity.swift */,
			);
			name = Util;
			path = Source/SocketIO/Util;
			sourceTree = "<group>";
		};
		DD52B647ED881F3FF6EEC617 /* Parse */ = {
			isa = PBXGroup;
			children = (
				DD52B59C11D3D2BC63612E50 /* SocketPacket.swift */,
				DD52B31D0E6815F5F10CEFB6 /* SocketParsable.swift */,
			);
			name = Parse;
			path = Source/SocketIO/Parse;
			sourceTree = "<group>";
		};
		DD52B6A0966AF71393777311 /* Client */ = {
			isa = PBXGroup;
			children = (
				DD52B5A9DE10C7A8AD35617F /* SocketAnyEvent.swift */,
				DD52BA1F41F2E4B3DC20260E /* SocketIOClient.swift */,
				DD52B6DCCBBAC6BE9C22568D /* SocketEventHandler.swift */,
				DD52BCAF915A546288664346 /* SocketIOClientSpec.swift */,
				DD52B078DB0A3C3D1BB507CD /* SocketIOClientOption.swift */,
				DD52B1D9BC4AE46D38D827DE /* SocketIOStatus.swift */,
				DD52B57FFEE8560CFFD793B3 /* SocketIOClientConfiguration.swift */,
				1C657533E849FC3E4342C602 /* SocketRawView.swift */,
			);
			name = Client;
			path = Source/SocketIO/Client;
			sourceTree = "<group>";
		};
		DD52BB6512B5400E2A3A39F2 /* Engine */ = {
			isa = PBXGroup;
			children = (
				DD52B7A9779A2E08075E5AAC /* SocketEngine.swift */,
				DD52B645273A873667BC2D43 /* SocketEngineSpec.swift */,
				DD52BE5FDCE1D684132E897C /* SocketEngineClient.swift */,
				DD52B2D110F55723F82B108E /* SocketEnginePollable.swift */,
				DD52BE9AD8B2BD7F841CD1D4 /* SocketEngineWebsocket.swift */,
				DD52B4EA17D8C3F35C8E2CB4 /* SocketEnginePacketType.swift */,
			);
			name = Engine;
			path = Source/SocketIO/Engine;
			sourceTree = "<group>";
		};
		DD52BF863FC00831DC047241 /* Ack */ = {
			isa = PBXGroup;
			children = (
				DD52BFF2E3216CDC364BB8AF /* SocketAckEmitter.swift */,
				DD52B09F7984E730513AB7E5 /* SocketAckManager.swift */,
			);
			name = Ack;
			path = Source/SocketIO/Ack;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		572EF2351B51F18A00EEBB58 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74BF53581F894326004972D8 /* SocketIO.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		572EF2371B51F18A00EEBB58 /* SocketIO */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 572EF24B1B51F18A00EEBB58 /* Build configuration list for PBXNativeTarget "SocketIO" */;
			buildPhases = (
				572EF2351B51F18A00EEBB58 /* Headers */,
				572EF2331B51F18A00EEBB58 /* Sources */,
				572EF2341B51F18A00EEBB58 /* Frameworks */,
				572EF2361B51F18A00EEBB58 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SocketIO;
			productName = "SocketIO-Mac";
			productReference = 572EF2381B51F18A00EEBB58 /* SocketIO.framework */;
			productType = "com.apple.product-type.framework";
		};
		572EF2411B51F18A00EEBB58 /* SocketIO-Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 572EF24E1B51F18A00EEBB58 /* Build configuration list for PBXNativeTarget "SocketIO-Tests" */;
			buildPhases = (
				572EF23E1B51F18A00EEBB58 /* Sources */,
				572EF23F1B51F18A00EEBB58 /* Frameworks */,
				572EF2401B51F18A00EEBB58 /* Resources */,
				74BF53571F8930D3004972D8 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				572EF2451B51F18A00EEBB58 /* PBXTargetDependency */,
			);
			name = "SocketIO-Tests";
			productName = "SocketIO-MacTests";
			productReference = 572EF2421B51F18A00EEBB58 /* SocketIO-Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		572EF20E1B51F12F00EEBB58 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftMigration = 0730;
				LastSwiftUpdateCheck = 0730;
				LastUpgradeCheck = 1020;
				TargetAttributes = {
					572EF2371B51F18A00EEBB58 = {
						CreatedOnToolsVersion = 6.4;
						LastSwiftMigration = 0900;
					};
					572EF2411B51F18A00EEBB58 = {
						CreatedOnToolsVersion = 6.4;
						LastSwiftMigration = 0900;
					};
				};
			};
			buildConfigurationList = 572EF2111B51F12F00EEBB58 /* Build configuration list for PBXProject "Socket.IO-Client-Swift" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 572EF20D1B51F12F00EEBB58;
			productRefGroup = 572EF21A1B51F16C00EEBB58 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				572EF2371B51F18A00EEBB58 /* SocketIO */,
				572EF2411B51F18A00EEBB58 /* SocketIO-Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		572EF2361B51F18A00EEBB58 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		572EF2401B51F18A00EEBB58 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		74BF53571F8930D3004972D8 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "for path in $FRAMEWORK_SEARCH_PATHS\ndo\n    if [ -d \"${path}/Starscream.framework\" ] && [[ $path == *\"Carthage\"* ]]; then\n        export SCRIPT_INPUT_FILE_COUNT=1\n        export SCRIPT_INPUT_FILE_0=\"${path}/Starscream.framework\"\n        /usr/local/bin/carthage copy-frameworks\n        break\n    fi\ndone";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		572EF2331B51F18A00EEBB58 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DD52BB82239886CF6ADD642C /* SocketEngine.swift in Sources */,
				DD52BFBC9E7CC32D3515AC80 /* SocketEngineSpec.swift in Sources */,
				DD52B3A6C1E082841C35C85D /* SocketEngineClient.swift in Sources */,
				DD52B883F942CD5A9D29892B /* SocketEnginePollable.swift in Sources */,
				DD52B4DFA12F2599410205D9 /* SocketEngineWebsocket.swift in Sources */,
				DD52B1FDEB06B853FF932AC7 /* SocketEnginePacketType.swift in Sources */,
				DD52B1F8BA0455EBE7C1B93E /* SocketAckEmitter.swift in Sources */,
				DD52B44AE56F2E07F3F3F991 /* SocketAckManager.swift in Sources */,
				DD52BB69B6D260035B652CA4 /* SocketAnyEvent.swift in Sources */,
				DD52BF924BEF05E1235CFD29 /* SocketIOClient.swift in Sources */,
				DD52BFEB4DBD3BF8D93DAEFF /* SocketEventHandler.swift in Sources */,
				DD52BB9A3E42FF2DD6BE7C2F /* SocketIOClientSpec.swift in Sources */,
				DD52B2AFE7D46039C7AE4D19 /* SocketIOClientOption.swift in Sources */,
				DD52BE4D1E6BB752CD9614A6 /* SocketIOStatus.swift in Sources */,
				DD52BD065B74AC5B77BAEFAA /* SocketIOClientConfiguration.swift in Sources */,
				DD52B048C71D724ABBD18C71 /* SocketTypes.swift in Sources */,
				DD52BC3F1F880820E8FDFD0C /* SocketLogger.swift in Sources */,
				DD52B56DE03CDB4F40BD1A23 /* SocketExtensions.swift in Sources */,
				DD52B11AF936352BAE30B2C8 /* SocketStringReader.swift in Sources */,
				DD52B57E7ABC61B57EE2A4B8 /* SocketPacket.swift in Sources */,
				DD52B9412F660F828B683422 /* SocketParsable.swift in Sources */,
				1C6572803D7E252A77A86E5F /* SocketManager.swift in Sources */,
				1C657FBB3F670261780FD72E /* SocketManagerSpec.swift in Sources */,
				1C6573B22DC9423CDFC32F05 /* SocketRawView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		572EF23E1B51F18A00EEBB58 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1C686BE21F869AFD007D8627 /* SocketIOClientConfigurationTest.swift in Sources */,
				1C686BE31F869AFD007D8627 /* SocketEngineTest.swift in Sources */,
				1C686BE41F869AFD007D8627 /* SocketSideEffectTest.swift in Sources */,
				1C686BE51F869AFD007D8627 /* SocketBasicPacketTest.swift in Sources */,
				1C686BE61F869AFD007D8627 /* SocketAckManagerTest.swift in Sources */,
				1C686BE71F869AFD007D8627 /* SocketParserTest.swift in Sources */,
				1C686BE81F869AFD007D8627 /* SocketNamespacePacketTest.swift in Sources */,
				DD52BCCD25EFA76E0F9B313C /* SocketMangerTest.swift in Sources */,
				1C657CDE5D510E8E2E573E39 /* utils.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		572EF2451B51F18A00EEBB58 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 572EF2371B51F18A00EEBB58 /* SocketIO */;
			targetProxy = 572EF2441B51F18A00EEBB58 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		572EF2121B51F12F00EEBB58 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Mac Developer";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/",
				);
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = SocketIO;
				SUPPORTED_PLATFORMS = "macosx appletvsimulator appletvos iphonesimulator iphoneos watchos watchsimulator xrsimulator xros";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VALID_ARCHS = "i386 x86_64 armv7 armv7s arm64 armv7k";
				WATCHOS_DEPLOYMENT_TARGET = 7.0;
			};
			name = Debug;
		};
		572EF2131B51F12F00EEBB58 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Mac Developer";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/",
				);
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MACOSX_DEPLOYMENT_TARGET = 10.14;
				PRODUCT_NAME = SocketIO;
				SUPPORTED_PLATFORMS = "macosx appletvsimulator appletvos iphonesimulator iphoneos watchos watchsimulator xrsimulator xros";
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VALID_ARCHS = "i386 x86_64 armv7 armv7s arm64 armv7k";
				WATCHOS_DEPLOYMENT_TARGET = 7.0;
			};
			name = Release;
		};
		572EF24C1B51F18A00EEBB58 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				FRAMEWORK_VERSION = A;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = SocketIO/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "io.socket.$(PRODUCT_NAME:rfc1034identifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "$(inherited)";
				SUPPORTS_MACCATALYST = YES;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_INCLUDE_PATHS = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3,4,7";
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VALID_ARCHS = "$(inherited)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		572EF24D1B51F18A00EEBB58 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				FRAMEWORK_VERSION = A;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = SocketIO/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "io.socket.$(PRODUCT_NAME:rfc1034identifier)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "$(inherited)";
				SUPPORTS_MACCATALYST = YES;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_INCLUDE_PATHS = "";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3,4,7";
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VALID_ARCHS = "$(inherited)";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		572EF24F1B51F18A00EEBB58 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/Mac",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=appletvos*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/tvOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=appletvsimulator*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/tvOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/iOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/iOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=macosx*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/Mac",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = SocketIO/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "io.socket.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SUPPORTED_PLATFORMS = "$(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VALID_ARCHS = "$(inherited)";
			};
			name = Debug;
		};
		572EF2501B51F18A00EEBB58 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/Mac",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=appletvos*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/tvOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=appletvsimulator*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/tvOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/iOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/iOS",
				);
				"FRAMEWORK_SEARCH_PATHS[sdk=macosx*]" = (
					"$(inherited)",
					"$(PROJECT_DIR)/Carthage/Build/Mac",
				);
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = SocketIO/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "io.socket.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SUPPORTED_PLATFORMS = "$(inherited)";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VALID_ARCHS = "$(inherited)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		572EF2111B51F12F00EEBB58 /* Build configuration list for PBXProject "Socket.IO-Client-Swift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				572EF2121B51F12F00EEBB58 /* Debug */,
				572EF2131B51F12F00EEBB58 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		572EF24B1B51F18A00EEBB58 /* Build configuration list for PBXNativeTarget "SocketIO" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				572EF24C1B51F18A00EEBB58 /* Debug */,
				572EF24D1B51F18A00EEBB58 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		572EF24E1B51F18A00EEBB58 /* Build configuration list for PBXNativeTarget "SocketIO-Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				572EF24F1B51F18A00EEBB58 /* Debug */,
				572EF2501B51F18A00EEBB58 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 572EF20E1B51F12F00EEBB58 /* Project object */;
}
