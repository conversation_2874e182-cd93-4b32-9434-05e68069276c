# Upgrading from v15 to v16

This guide will help you navigate the changes that were introduced in v16.

## Objective-c is no longer supported. You must now use Swift.

## Client supports multiple socket.io versions

The client now supports socket.io 3 servers. This is mostly a transparent change, however if your server
is socket.io 2, you must send `.version(.two)` as an option to the manager.

```swift
SocketManager(socketURL: URL(string:"http://localhost:8087/")!, config: [.version(.two)])
```

## How to upgrade

- first, upgrade the Socket.IO server to v4 with the compatibility mode enabled (`allowEIO3: true`)
- then, upgrade the clients to v16
- finally, once all clients have upgraded, disable the compatibility mode

You can check the version of the connection on the server side with:

```js
io.on("connection", (socket) => {
  // either 3 for the 3rd revision of the protocol (Socket.IO v2) or 4 for the 4th revision (Socket.IO v3/v4)
  const version = socket.conn.protocol;
});
```

See also:

- [Compatibility table](https://nuclearace.github.io/Socket.IO-Client-Swift/Compatibility.html)
- Migrating from 2.x to 3.0: https://socket.io/docs/v4/migrating-from-2-x-to-3-0/
- Migrating from 3.x to 4.0: https://socket.io/docs/v4/migrating-from-3-x-to-4-0/
