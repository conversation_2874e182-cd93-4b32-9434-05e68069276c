//
//  ValidationTests.swift
//
//  Copyright (c) 2014-2018 Alamofire Software Foundation (http://alamofire.org/)
//
//  Permission is hereby granted, free of charge, to any person obtaining a copy
//  of this software and associated documentation files (the "Software"), to deal
//  in the Software without restriction, including without limitation the rights
//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the Software is
//  furnished to do so, subject to the following conditions:
//
//  The above copyright notice and this permission notice shall be included in
//  all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
//  THE SOFTWARE.
//

@testable import Alamofire
import Foundation
import XCTest

final class StatusCodeValidationTestCase: BaseTestCase {
    @MainActor
    func testThatValidationForRequestWithAcceptableStatusCodeResponseSucceeds() {
        // Given
        let endpoint = Endpoint.status(200)

        let expectation1 = expectation(description: "request should return 200 status code")
        let expectation2 = expectation(description: "download should return 200 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(statusCode: 200..<300)
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(statusCode: 200..<300)
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationForRequestWithUnacceptableStatusCodeResponseFails() {
        // Given
        let endpoint = Endpoint.status(404)

        let expectation1 = expectation(description: "request should return 404 status code")
        let expectation2 = expectation(description: "download should return 404 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(statusCode: [200])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(statusCode: [200])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableStatusCode, true)
            XCTAssertEqual(error?.responseCode, 404)
        }
    }

    @MainActor
    func testThatValidationForRequestWithNoAcceptableStatusCodesFails() {
        // Given
        let endpoint = Endpoint.status(201)

        let expectation1 = expectation(description: "request should return 201 status code")
        let expectation2 = expectation(description: "download should return 201 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(statusCode: [])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(statusCode: [])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableStatusCode, true)
            XCTAssertEqual(error?.responseCode, 201)
        }
    }
}

// MARK: -

final class ContentTypeValidationTestCase: BaseTestCase {
    @MainActor
    func testThatValidationForRequestWithAcceptableContentTypeResponseSucceeds() {
        // Given
        let endpoint = Endpoint.ip

        let expectation1 = expectation(description: "request should succeed and return ip")
        let expectation2 = expectation(description: "download should succeed and return ip")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(contentType: ["application/json"])
            .validate(contentType: ["application/json; charset=utf-8"])
            .validate(contentType: ["application/json; q=0.8; charset=utf-8"])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(contentType: ["application/json"])
            .validate(contentType: ["application/json; charset=utf-8"])
            .validate(contentType: ["application/json; q=0.8; charset=utf-8"])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationForRequestWithAcceptableWildcardContentTypeResponseSucceeds() {
        // Given
        let endpoint = Endpoint.ip

        let expectation1 = expectation(description: "request should succeed and return ip")
        let expectation2 = expectation(description: "download should succeed and return ip")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(contentType: ["*/*"])
            .validate(contentType: ["application/*"])
            .validate(contentType: ["*/json"])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(contentType: ["*/*"])
            .validate(contentType: ["application/*"])
            .validate(contentType: ["*/json"])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationForRequestWithUnacceptableContentTypeResponseFails() {
        // Given
        let endpoint = Endpoint.xml

        let expectation1 = expectation(description: "request should succeed and return xml")
        let expectation2 = expectation(description: "download should succeed and return xml")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(contentType: ["application/octet-stream"])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(contentType: ["application/octet-stream"])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableContentType, true)
            XCTAssertEqual(error?.responseContentType, "application/xml")
            XCTAssertEqual(error?.acceptableContentTypes?.first, "application/octet-stream")
        }
    }

    @MainActor
    func testThatContentTypeValidationFailureSortsPossibleContentTypes() {
        // Given
        let endpoint = Endpoint.xml

        let requestDidCompleteExpectation = expectation(description: "request should succeed and return xml")
        let downloadDidCompleteExpectation = expectation(description: "download should succeed and return xml")

        var requestError: AFError?
        var downloadError: AFError?

        let acceptableContentTypes = [ // Sorted in a random order, not alphabetically
            "application/octet-stream",
            "image/gif",
            "image/x-xbitmap",
            "image/tiff",
            "image/jpg",
            "image/x-bmp",
            "image/jpeg",
            "image/x-icon",
            "image/jp2",
            "image/png",
            "image/ico",
            "image/bmp",
            "image/x-ms-bmp",
            "image/x-win-bitmap"
        ]

        // When
        AF.request(endpoint)
            .validate(contentType: acceptableContentTypes)
            .response { resp in
                requestError = resp.error
                requestDidCompleteExpectation.fulfill()
            }

        AF.download(endpoint)
            .validate(contentType: acceptableContentTypes)
            .response { resp in
                downloadError = resp.error
                downloadDidCompleteExpectation.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        let expectedAcceptableContentTypes = [ // Sorted in a specific order, alphabetically
            "application/octet-stream",
            "image/bmp",
            "image/gif",
            "image/ico",
            "image/jp2",
            "image/jpeg",
            "image/jpg",
            "image/png",
            "image/tiff",
            "image/x-bmp",
            "image/x-icon",
            "image/x-ms-bmp",
            "image/x-win-bitmap",
            "image/x-xbitmap"
        ]

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableContentType, true)
            XCTAssertEqual(error?.responseContentType, "application/xml")
            XCTAssertEqual(error?.acceptableContentTypes, expectedAcceptableContentTypes)
        }
    }

    @MainActor
    func testThatValidationForRequestWithNoAcceptableContentTypeResponseFails() {
        // Given
        let endpoint = Endpoint.xml

        let expectation1 = expectation(description: "request should succeed and return xml")
        let expectation2 = expectation(description: "download should succeed and return xml")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(contentType: [])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(contentType: [])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableContentType, true)
            XCTAssertEqual(error?.responseContentType, "application/xml")
            XCTAssertEqual(error?.acceptableContentTypes?.isEmpty, true)
        }
    }

    @MainActor
    func testThatValidationForRequestWithNoAcceptableContentTypeResponseSucceedsWhenNoDataIsReturned() {
        // Given
        let endpoint = Endpoint.status(204)

        let expectation1 = expectation(description: "request should succeed and return no data")
        let expectation2 = expectation(description: "download should succeed and return no data")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(contentType: [])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(contentType: [])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }
}

// MARK: -

final class MultipleValidationTestCase: BaseTestCase {
    @MainActor
    func testThatValidationForRequestWithAcceptableStatusCodeAndContentTypeResponseSucceeds() {
        // Given
        let endpoint = Endpoint.ip

        let expectation1 = expectation(description: "request should succeed and return ip")
        let expectation2 = expectation(description: "request should succeed and return ip")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(statusCode: 200..<300)
            .validate(contentType: ["application/json"])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(statusCode: 200..<300)
            .validate(contentType: ["application/json"])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationForRequestWithUnacceptableStatusCodeAndContentTypeResponseFailsWithStatusCodeError() {
        // Given
        let endpoint = Endpoint.xml

        let expectation1 = expectation(description: "request should succeed and return xml")
        let expectation2 = expectation(description: "download should succeed and return xml")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(statusCode: 400..<600)
            .validate(contentType: ["application/octet-stream"])
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(statusCode: 400..<600)
            .validate(contentType: ["application/octet-stream"])
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableStatusCode, true)
            XCTAssertEqual(error?.responseCode, 200)
        }
    }

    @MainActor
    func testThatValidationForRequestWithUnacceptableStatusCodeAndContentTypeResponseFailsWithContentTypeError() {
        // Given
        let endpoint = Endpoint.xml

        let expectation1 = expectation(description: "request should succeed and return xml")
        let expectation2 = expectation(description: "download should succeed and return xml")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(contentType: ["application/octet-stream"])
            .validate(statusCode: 400..<600)
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(contentType: ["application/octet-stream"])
            .validate(statusCode: 400..<600)
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableContentType, true)
            XCTAssertEqual(error?.responseContentType, "application/xml")
            XCTAssertEqual(error?.acceptableContentTypes?.first, "application/octet-stream")
        }
    }
}

// MARK: -

final class AutomaticValidationTestCase: BaseTestCase {
    @MainActor
    func testThatValidationForRequestWithAcceptableStatusCodeAndContentTypeResponseSucceeds() {
        // Given
        let urlRequest = Endpoint.ip.modifying(\.headers, to: [.accept("application/json")])

        let expectation1 = expectation(description: "request should succeed and return ip")
        let expectation2 = expectation(description: "download should succeed and return ip")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(urlRequest).validate().response { resp in
            requestError = resp.error
            expectation1.fulfill()
        }

        AF.download(urlRequest).validate().response { resp in
            downloadError = resp.error
            expectation2.fulfill()
        }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationForRequestWithUnacceptableStatusCodeResponseFails() {
        // Given
        let request = Endpoint.status(404)

        let expectation1 = expectation(description: "request should return 404 status code")
        let expectation2 = expectation(description: "download should return 404 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(request)
            .validate()
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(request)
            .validate()
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableStatusCode, true)
            XCTAssertEqual(error?.responseCode, 404)
        }
    }

    @MainActor
    func testThatValidationForRequestWithAcceptableWildcardContentTypeResponseSucceeds() {
        // Given
        let urlRequest = Endpoint.ip.modifying(\.headers, to: [.accept("application/*")])

        let expectation1 = expectation(description: "request should succeed and return ip")
        let expectation2 = expectation(description: "download should succeed and return ip")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(urlRequest).validate().response { resp in
            requestError = resp.error
            expectation1.fulfill()
        }

        AF.download(urlRequest).validate().response { resp in
            downloadError = resp.error
            expectation2.fulfill()
        }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationForRequestWithAcceptableComplexContentTypeResponseSucceeds() {
        // Given
        var urlRequest = Endpoint.xml.urlRequest

        let headerValue = "text/xml, application/xml, application/xhtml+xml, text/html;q=0.9, text/plain;q=0.8,*/*;q=0.5"
        urlRequest.headers["Accept"] = headerValue

        let expectation1 = expectation(description: "request should succeed and return xml")
        let expectation2 = expectation(description: "request should succeed and return xml")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(urlRequest).validate().response { resp in
            requestError = resp.error
            expectation1.fulfill()
        }

        AF.download(urlRequest).validate().response { resp in
            downloadError = resp.error
            expectation2.fulfill()
        }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationForRequestWithUnacceptableContentTypeResponseFails() {
        // Given
        let urlRequest = Endpoint.xml.modifying(\.headers, to: [.accept("application/json")])

        let expectation1 = expectation(description: "request should succeed and return xml")
        let expectation2 = expectation(description: "download should succeed and return xml")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(urlRequest).validate().response { resp in
            requestError = resp.error
            expectation1.fulfill()
        }

        AF.download(urlRequest).validate().response { resp in
            downloadError = resp.error
            expectation2.fulfill()
        }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNotNil(requestError)
        XCTAssertNotNil(downloadError)

        for error in [requestError, downloadError] {
            XCTAssertEqual(error?.isUnacceptableContentType, true)
            XCTAssertEqual(error?.responseContentType, "application/xml")
            XCTAssertEqual(error?.acceptableContentTypes?.first, "application/json")
        }
    }
}

// MARK: -

private enum ValidationError: Error {
    case missingData, missingFile, fileReadFailed
}

extension DataRequest {
    func validateDataExists() -> Self {
        validate { _, _, data in
            guard data != nil else { return .failure(ValidationError.missingData) }
            return .success(())
        }
    }

    func validate(with error: any Error) -> Self {
        validate { _, _, _ in .failure(error) }
    }
}

extension DownloadRequest {
    func validateDataExists() -> Self {
        validate { [unowned self] _, _, _ in
            guard let validFileURL = fileURL else { return .failure(ValidationError.missingFile) }

            do {
                _ = try Data(contentsOf: validFileURL)
                return .success(())
            } catch {
                return .failure(ValidationError.fileReadFailed)
            }
        }
    }

    func validate(with error: any Error) -> Self {
        validate { _, _, _ in .failure(error) }
    }
}

// MARK: -

final class CustomValidationTestCase: BaseTestCase {
    @MainActor
    func testThatCustomValidationClosureHasAccessToServerResponseData() {
        // Given
        let endpoint = Endpoint()

        let expectation1 = expectation(description: "request should return 200 status code")
        let expectation2 = expectation(description: "download should return 200 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate { _, _, data in
                guard data != nil else { return .failure(ValidationError.missingData) }
                return .success(())
            }
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate { _, _, fileURL in
                guard let fileURL else { return .failure(ValidationError.missingFile) }

                do {
                    _ = try Data(contentsOf: fileURL)
                    return .success(())
                } catch {
                    return .failure(ValidationError.fileReadFailed)
                }
            }
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatCustomValidationCanThrowCustomError() {
        // Given
        let endpoint = Endpoint()

        let expectation1 = expectation(description: "request should return 200 status code")
        let expectation2 = expectation(description: "download should return 200 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate { _, _, _ in .failure(ValidationError.missingData) }
            .validate { _, _, _ in .failure(ValidationError.missingFile) } // should be ignored
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate { _, _, _ in .failure(ValidationError.missingFile) }
            .validate { _, _, _ in .failure(ValidationError.fileReadFailed) } // should be ignored
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertEqual(requestError?.asAFError?.underlyingError as? ValidationError, .missingData)
        XCTAssertEqual(downloadError?.asAFError?.underlyingError as? ValidationError, .missingFile)
    }

    @MainActor
    func testThatValidationExtensionHasAccessToServerResponseData() {
        // Given
        let endpoint = Endpoint()

        let expectation1 = expectation(description: "request should return 200 status code")
        let expectation2 = expectation(description: "download should return 200 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validateDataExists()
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validateDataExists()
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertNil(requestError)
        XCTAssertNil(downloadError)
    }

    @MainActor
    func testThatValidationExtensionCanThrowCustomError() {
        // Given
        let endpoint = Endpoint()

        let expectation1 = expectation(description: "request should return 200 status code")
        let expectation2 = expectation(description: "download should return 200 status code")

        var requestError: AFError?
        var downloadError: AFError?

        // When
        AF.request(endpoint)
            .validate(with: ValidationError.missingData)
            .validate(with: ValidationError.missingFile) // should be ignored
            .response { resp in
                requestError = resp.error
                expectation1.fulfill()
            }

        AF.download(endpoint)
            .validate(with: ValidationError.missingFile)
            .validate(with: ValidationError.fileReadFailed) // should be ignored
            .response { resp in
                downloadError = resp.error
                expectation2.fulfill()
            }

        waitForExpectations(timeout: timeout)

        // Then
        XCTAssertEqual(requestError?.asAFError?.underlyingError as? ValidationError, .missingData)
        XCTAssertEqual(downloadError?.asAFError?.underlyingError as? ValidationError, .missingFile)
    }
}
