{"Typealiases.html#/s:9Alamofire10Parametersa": {"name": "Parameters", "abstract": "<p>A dictionary of parameters to apply to a <code>URLRequest</code>.</p>"}, "Typealiases.html#/s:9Alamofire14AFDataResponsea": {"name": "AFDataResponse", "abstract": "<p>Default type of <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponse.html\">DataResponse</a></code> returned by Alamofire, with an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code> <code>Failure</code> type.</p>"}, "Typealiases.html#/s:9Alamofire18AFDownloadResponsea": {"name": "AFDownloadResponse", "abstract": "<p>Default type of <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponse.html\">DownloadResponse</a></code> returned by Alamofire, with an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code> <code>Failure</code> type.</p>"}, "Typealiases.html#/s:9Alamofire8AFResulta": {"name": "AFResult", "abstract": "<p>Default type of <code>Result</code> returned by Alamofire, with an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code> <code>Failure</code> type.</p>"}, "Typealiases.html#/s:9Alamofire12AdaptHandlera": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> closure definition.</p>"}, "Typealiases.html#/s:9Alamofire12RetryHandlera": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code> closure definition.</p>"}, "Typealiases.html#/s:9Alamofire17DisabledEvaluatora": {"name": "DisabledEvaluator", "abstract": "<p>Disables all evaluation which in turn will always consider any server trust as valid.</p>"}, "Structs/Empty.html#/s:9Alamofire5EmptyV5valueACvpZ": {"name": "value", "abstract": "<p>Static <code>Empty</code> instance used for all <code>Empty</code> responses.</p>", "parent_name": "Empty"}, "Structs/Empty.html#/s:9Alamofire13EmptyResponseP10emptyValuexyFZ": {"name": "emptyValue()", "parent_name": "Empty"}, "Structs/URLResponseSerializer.html#/s:9Alamofire21URLResponseSerializerVACycfc": {"name": "init()", "abstract": "<p>Creates an instance.</p>", "parent_name": "URLResponseSerializer"}, "Structs/URLResponseSerializer.html#/s:9Alamofire34DownloadResponseSerializerProtocolP09serializeB07request8response7fileURL5error16SerializedObjectQz10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0J0VSgs5Error_pSgtKF": {"name": "serializeDownload(request:response:fileURL:error:)", "parent_name": "URLResponseSerializer"}, "Structs/GoogleXSSIPreprocessor.html#/s:9Alamofire22GoogleXSSIPreprocessorVACycfc": {"name": "init()", "abstract": "<p>Creates an instance.</p>", "parent_name": "GoogleXSSIPreprocessor"}, "Structs/GoogleXSSIPreprocessor.html#/s:9Alamofire16DataPreprocessorP10preprocessy10Foundation0B0VAGKF": {"name": "preprocess(_:)", "parent_name": "GoogleXSSIPreprocessor"}, "Structs/PassthroughPreprocessor.html#/s:9Alamofire23PassthroughPreprocessorVACycfc": {"name": "init()", "abstract": "<p>Creates an instance.</p>", "parent_name": "PassthroughPreprocessor"}, "Structs/PassthroughPreprocessor.html#/s:9Alamofire16DataPreprocessorP10preprocessy10Foundation0B0VAGKF": {"name": "preprocess(_:)", "parent_name": "PassthroughPreprocessor"}, "Structs/RequestAdapterState.html#/s:9Alamofire19RequestAdapterStateV9requestID10Foundation4UUIDVvp": {"name": "requestID", "abstract": "<p>The <code>UUID</code> of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> associated with the <code>URLRequest</code> to adapt.</p>", "parent_name": "RequestAdapterState"}, "Structs/RequestAdapterState.html#/s:9Alamofire19RequestAdapterStateV7sessionAA7SessionCvp": {"name": "session", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Session.html\">Session</a></code> associated with the <code>URLRequest</code> to adapt.</p>", "parent_name": "RequestAdapterState"}, "Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html#/s:9Alamofire24DeflateRequestCompressorV23DuplicateHeaderBehaviorO5erroryA2EmF": {"name": "error", "abstract": "<p>Throws a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV\">DuplicateHeaderError</a></code>. The default.</p>", "parent_name": "DuplicateHeaderBehavior"}, "Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html#/s:9Alamofire24DeflateRequestCompressorV23DuplicateHeaderBehaviorO7replaceyA2EmF": {"name": "replace", "abstract": "<p>Replaces the existing header value with <code>deflate</code>.</p>", "parent_name": "DuplicateHeaderBehavior"}, "Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html#/s:9Alamofire24DeflateRequestCompressorV23DuplicateHeaderBehaviorO4skipyA2EmF": {"name": "skip", "abstract": "<p>Silently skips compression when the header exists.</p>", "parent_name": "DuplicateHeaderBehavior"}, "Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html": {"name": "DuplicateHeaderBehavior", "abstract": "<p>Type that determines the action taken when the <code>URLRequest</code> already has a <code>Content-Encoding</code> header.</p>", "parent_name": "DeflateRequestCompressor"}, "Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV": {"name": "DuplicateHeaderError", "abstract": "<p><code>Error</code> produced when the outgoing <code>URLRequest</code> already has a <code>Content-Encoding</code> header, when the instance has", "parent_name": "DeflateRequestCompressor"}, "Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV23duplicateHeaderBehaviorAC09DuplicatefG0Ovp": {"name": "duplicateHeaderBehavior", "abstract": "<p>Behavior to use when the outgoing <code>URLRequest</code> already has a <code>Content-Encoding</code> header.</p>", "parent_name": "DeflateRequestCompressor"}, "Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV22shouldCompressBodyDataySb10Foundation0H0VYbcvp": {"name": "shouldCompressBodyData", "abstract": "<p>Closure which determines whether the outgoing body data should be compressed.</p>", "parent_name": "DeflateRequestCompressor"}, "Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV23duplicateHeaderBehavior22shouldCompressBodyDataA2C09DuplicatefG0O_Sb10Foundation0K0VYbctcfc": {"name": "init(duplicateHeaderBehavior:shouldCompressBodyData:)", "abstract": "<p>Creates an instance with the provided parameters.</p>", "parent_name": "DeflateRequestCompressor"}, "Structs/DeflateRequestCompressor.html#/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:for:completion:)", "parent_name": "DeflateRequestCompressor"}, "Structs/Redirector/Behavior.html#/s:9Alamofire10RedirectorV8BehaviorO6followyA2EmF": {"name": "follow", "abstract": "<p>Follow the redirect as defined in the response.</p>", "parent_name": "Behavior"}, "Structs/Redirector/Behavior.html#/s:9Alamofire10RedirectorV8BehaviorO11doNotFollowyA2EmF": {"name": "doNotFollow", "abstract": "<p>Do not follow the redirect defined in the response.</p>", "parent_name": "Behavior"}, "Structs/Redirector/Behavior.html#/s:9Alamofire10RedirectorV8BehaviorO6modifyyAE10Foundation10URLRequestVSgSo16NSURLSessionTaskC_AISo17NSHTTPURLResponseCtYbccAEmF": {"name": "modify(_:)", "abstract": "<p>Modify the redirect request defined in the response.</p>", "parent_name": "Behavior"}, "Structs/Redirector/Behavior.html": {"name": "Behavior", "abstract": "<p>Defines the behavior of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector.html\">Redirector</a></code> type.</p>", "parent_name": "Redirector"}, "Structs/Redirector.html#/s:9Alamofire10RedirectorV6followACvpZ": {"name": "follow", "abstract": "<p>Returns a <code>Redirector</code> with a <code>.follow</code> <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector/Behavior.html\">Behavior</a></code>.</p>", "parent_name": "Redirector"}, "Structs/Redirector.html#/s:9Alamofire10RedirectorV11doNotFollowACvpZ": {"name": "doNotFollow", "abstract": "<p>Returns a <code>Redirector</code> with a <code>.doNotFollow</code> <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector/Behavior.html\">Behavior</a></code>.</p>", "parent_name": "Redirector"}, "Structs/Redirector.html#/s:9Alamofire10RedirectorV8behaviorAC8BehaviorOvp": {"name": "behavior", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector/Behavior.html\">Behavior</a></code> of the <code>Redirector</code>.</p>", "parent_name": "Redirector"}, "Structs/Redirector.html#/s:9Alamofire10RedirectorV8behaviorA2C8BehaviorO_tcfc": {"name": "init(behavior:)", "abstract": "<p>Creates a <code>Redirector</code> instance from the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector/Behavior.html\">Behavior</a></code>.</p>", "parent_name": "Redirector"}, "Structs/Redirector.html#/s:9Alamofire15RedirectHandlerP4task_18willBeRedirectedTo3for10completionySo16NSURLSessionTaskC_10Foundation10URLRequestVSo17NSHTTPURLResponseCyALSgctF": {"name": "task(_:willBeRedirectedTo:for:completion:)", "parent_name": "Redirector"}, "Structs/StreamOf/Iterator.html#/s:ScI4next7ElementQzSgyYaKF": {"name": "next()", "parent_name": "Iterator"}, "Structs/StreamOf.html#/s:Sci13AsyncIteratorQa": {"name": "AsyncIterator", "parent_name": "StreamOf"}, "Structs/StreamOf.html#/s:9Alamofire8StreamOfV15BufferingPolicya": {"name": "BufferingPolicy", "abstract": "<p>Undocumented</p>", "parent_name": "StreamOf"}, "Structs/StreamOf.html#/s:Sci17makeAsyncIterator0bC0QzyF": {"name": "makeAsyncIterator()", "parent_name": "StreamOf"}, "Structs/StreamOf/Iterator.html": {"name": "Iterator", "abstract": "<p>Undocumented</p>", "parent_name": "StreamOf"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV0C0a": {"name": "Stream", "abstract": "<p>Undocumented</p>", "parent_name": "DataStreamTask"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV09streamingB023automaticallyCancelling15bufferingPolicyAA0C2OfVyAA0bC7RequestC0C0Vy_10Foundation0B0Vs5NeverOGGSb_ScS12ContinuationV09BufferingI0OyAR__GtF": {"name": "streamingData(automaticallyCancelling:bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV0C0a\">Stream</a></code> of <code>Data</code> values from the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "DataStreamTask"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV16streamingStrings23automaticallyCancelling15bufferingPolicyAA0C2OfVyAA0bC7RequestC0C0Vy_SSs5NeverOGGSb_ScS12ContinuationV09BufferingJ0OyAO__GtF": {"name": "streamingStrings(automaticallyCancelling:bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV0C0a\">Stream</a></code> of <code>UTF-8</code> <code>String</code>s from the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "DataStreamTask"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV19streamingDecodables_23automaticallyCancelling15bufferingPolicyAA0C2OfVyAA0bC7RequestC0C0Vy_xAA7AFErrorOGGxm_SbScS12ContinuationV09BufferingJ0OyAO__GtSeRzs8SendableRzlF": {"name": "streamingDecodables(_:automaticallyCancelling:bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV0C0a\">Stream</a></code> of <code>Decodable</code> values from the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "DataStreamTask"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV18streamingResponses15serializedUsing23automaticallyCancelling15bufferingPolicyAA0C2OfVyAA0bC7RequestC0C0Vy_16SerializedObjectQzAA7AFErrorOGGx_SbScS12ContinuationV09BufferingL0OyAR__GtAA0bC10SerializerRzlF": {"name": "streamingResponses(serializedUsing:automaticallyCancelling:bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV0C0a\">Stream</a></code> of values using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code> from the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "DataStreamTask"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV6cancelyyF": {"name": "cancel()", "abstract": "<p>Cancel the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "DataStreamTask"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV6resumeyyF": {"name": "resume()", "abstract": "<p>Resume the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "DataStreamTask"}, "Structs/DataStreamTask.html#/s:9Alamofire14DataStreamTaskV7suspendyyF": {"name": "suspend()", "abstract": "<p>Suspend the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "DataStreamTask"}, "Structs/DownloadTask.html#/s:9Alamofire12DownloadTaskV8responseAA0B8ResponseVyxAA7AFErrorOGvp": {"name": "response", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponse.html\">DownloadResponse</a></code> produced by the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> and its response handler.</p>", "parent_name": "DownloadTask"}, "Structs/DownloadTask.html#/s:9Alamofire12DownloadTaskV6results6ResultOyxAA7AFErrorOGvp": {"name": "result", "abstract": "<p><code>Result</code> of any response serialization performed for the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html#/s:9Alamofire12DownloadTaskV8responseAA0B8ResponseVyxAA7AFErrorOGvp\">response</a></code>.</p>", "parent_name": "DownloadTask"}, "Structs/DownloadTask.html#/s:9Alamofire12DownloadTaskV5valuexvp": {"name": "value", "abstract": "<p><code>Value</code> returned by the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html#/s:9Alamofire12DownloadTaskV8responseAA0B8ResponseVyxAA7AFErrorOGvp\">response</a></code>.</p>", "parent_name": "DownloadTask"}, "Structs/DownloadTask.html#/s:9Alamofire12DownloadTaskV6cancelyyF": {"name": "cancel()", "abstract": "<p>Cancel the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> and <code>Task</code>.</p>", "parent_name": "DownloadTask"}, "Structs/DownloadTask.html#/s:9Alamofire12DownloadTaskV6resumeyyF": {"name": "resume()", "abstract": "<p>Resume the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code>.</p>", "parent_name": "DownloadTask"}, "Structs/DownloadTask.html#/s:9Alamofire12DownloadTaskV7suspendyyF": {"name": "suspend()", "abstract": "<p>Suspend the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code>.</p>", "parent_name": "DownloadTask"}, "Structs/DataTask.html#/s:9Alamofire8DataTaskV8responseAA0B8ResponseVyxAA7AFErrorOGvp": {"name": "response", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponse.html\">DataResponse</a></code> produced by the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> and its response handler.</p>", "parent_name": "DataTask"}, "Structs/DataTask.html#/s:9Alamofire8DataTaskV6results6ResultOyxAA7AFErrorOGvp": {"name": "result", "abstract": "<p><code>Result</code> of any response serialization performed for the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataTask.html#/s:9Alamofire8DataTaskV8responseAA0B8ResponseVyxAA7AFErrorOGvp\">response</a></code>.</p>", "parent_name": "DataTask"}, "Structs/DataTask.html#/s:9Alamofire8DataTaskV5valuexvp": {"name": "value", "abstract": "<p><code>Value</code> returned by the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataTask.html#/s:9Alamofire8DataTaskV8responseAA0B8ResponseVyxAA7AFErrorOGvp\">response</a></code>.</p>", "parent_name": "DataTask"}, "Structs/DataTask.html#/s:9Alamofire8DataTaskV6cancelyyF": {"name": "cancel()", "abstract": "<p>Cancel the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> and <code>Task</code>.</p>", "parent_name": "DataTask"}, "Structs/DataTask.html#/s:9Alamofire8DataTaskV6resumeyyF": {"name": "resume()", "abstract": "<p>Resume the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code>.</p>", "parent_name": "DataTask"}, "Structs/DataTask.html#/s:9Alamofire8DataTaskV7suspendyyF": {"name": "suspend()", "abstract": "<p>Suspend the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code>.</p>", "parent_name": "DataTask"}, "Structs/DownloadResponsePublisher.html#/s:7Combine9PublisherP6OutputQa": {"name": "Output", "parent_name": "DownloadResponsePublisher"}, "Structs/DownloadResponsePublisher.html#/s:7Combine9PublisherP7FailureQa": {"name": "Failure", "parent_name": "DownloadResponsePublisher"}, "Structs/DownloadResponsePublisher.html#/s:9Alamofire25DownloadResponsePublisherV_5queue10serializerACyxGAA0B7RequestC_So012OS_dispatch_E0Cqd__tc16SerializedObjectQyd__RszAA0C10SerializerRd__lufc": {"name": "init(_:queue:serializer:)", "abstract": "<p>Creates an instance which will serialize responses using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code>.</p>", "parent_name": "DownloadResponsePublisher"}, "Structs/DownloadResponsePublisher.html#/s:9Alamofire25DownloadResponsePublisherV_5queue10serializerACyxGAA0B7RequestC_So012OS_dispatch_E0Cqd__tc16SerializedObjectQyd__RszAA0bC18SerializerProtocolRd__lufc": {"name": "init(_:queue:serializer:)", "abstract": "<p>Creates an instance which will serialize responses using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DownloadResponseSerializerProtocol.html\">DownloadResponseSerializerProtocol</a></code> value.</p>", "parent_name": "DownloadResponsePublisher"}, "Structs/DownloadResponsePublisher.html#/s:9Alamofire25DownloadResponsePublisherV6result7Combine03AnyD0Vys6ResultOyxAA7AFErrorOGs5NeverOGyF": {"name": "result()", "abstract": "<p>Publishes only the <code>Result</code> of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponse.html\">DownloadResponse</a></code> value.</p>", "parent_name": "DownloadResponsePublisher"}, "Structs/DownloadResponsePublisher.html#/s:9Alamofire25DownloadResponsePublisherV5value7Combine03AnyD0VyxAA7AFErrorOGyF": {"name": "value()", "abstract": "<p>Publishes the <code>Result</code> of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponse.html\">DownloadResponse</a></code> as a single <code>Value</code> or fail with the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code> instance.</p>", "parent_name": "DownloadResponsePublisher"}, "Structs/DownloadResponsePublisher.html#/s:7Combine9PublisherP7receive10subscriberyqd___tAA10SubscriberRd__7FailureQyd__AGRtz5InputQyd__6OutputRtzlF": {"name": "receive(subscriber:)", "parent_name": "DownloadResponsePublisher"}, "Structs/DownloadResponsePublisher.html#/s:9Alamofire25DownloadResponsePublisherVAA10Foundation3URLVSgRszrlE_5queueACyAGGAA0B7RequestC_So012OS_dispatch_G0Ctcfc": {"name": "init(_:queue:)", "abstract": "<p>Creates an instance which publishes a <code>DownloadResponse&lt;URL?, AFError&gt;</code> value without serialization.</p>", "parent_name": "DownloadResponsePublisher"}, "Structs/DataStreamPublisher.html#/s:7Combine9PublisherP6OutputQa": {"name": "Output", "parent_name": "DataStreamPublisher"}, "Structs/DataStreamPublisher.html#/s:7Combine9PublisherP7FailureQa": {"name": "Failure", "parent_name": "DataStreamPublisher"}, "Structs/DataStreamPublisher.html#/s:9Alamofire19DataStreamPublisherV_5queue10serializerACyxGAA0bC7RequestC_So012OS_dispatch_E0Cqd__tc16SerializedObjectQyd__RszAA0bC10SerializerRd__lufc": {"name": "init(_:queue:serializer:)", "abstract": "<p>Creates an instance which will serialize responses using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code>.</p>", "parent_name": "DataStreamPublisher"}, "Structs/DataStreamPublisher.html#/s:9Alamofire19DataStreamPublisherV6result7Combine03AnyD0Vys6ResultOyxAA7AFErrorOGs5NeverOGyF": {"name": "result()", "abstract": "<p>Publishes only the <code>Result</code> of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest/Stream.html\">DataStreamRequest.Stream</a></code>&lsquo;s <code>Event</code>s.</p>", "parent_name": "DataStreamPublisher"}, "Structs/DataStreamPublisher.html#/s:9Alamofire19DataStreamPublisherV5value7Combine03AnyD0VyxAA7AFErrorOGyF": {"name": "value()", "abstract": "<p>Publishes the streamed values of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest/Stream.html\">DataStreamRequest.Stream</a></code> as a sequence of <code>Value</code> or fail with the", "parent_name": "DataStreamPublisher"}, "Structs/DataStreamPublisher.html#/s:7Combine9PublisherP7receive10subscriberyqd___tAA10SubscriberRd__7FailureQyd__AGRtz5InputQyd__6OutputRtzlF": {"name": "receive(subscriber:)", "parent_name": "DataStreamPublisher"}, "Structs/DataResponsePublisher.html#/s:7Combine9PublisherP6OutputQa": {"name": "Output", "parent_name": "DataResponsePublisher"}, "Structs/DataResponsePublisher.html#/s:7Combine9PublisherP7FailureQa": {"name": "Failure", "parent_name": "DataResponsePublisher"}, "Structs/DataResponsePublisher.html#/s:9Alamofire21DataResponsePublisherV_5queue10serializerACyxGAA0B7RequestC_So012OS_dispatch_E0Cqd__tc16SerializedObjectQyd__RszAA0C10SerializerRd__lufc": {"name": "init(_:queue:serializer:)", "abstract": "<p>Creates an instance which will serialize responses using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code>.</p>", "parent_name": "DataResponsePublisher"}, "Structs/DataResponsePublisher.html#/s:9Alamofire21DataResponsePublisherV_5queue10serializerACyxGAA0B7RequestC_So012OS_dispatch_E0Cqd__tc16SerializedObjectQyd__RszAA0bC18SerializerProtocolRd__lufc": {"name": "init(_:queue:serializer:)", "abstract": "<p>Creates an instance which will serialize responses using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataResponseSerializerProtocol.html\">DataResponseSerializerProtocol</a></code>.</p>", "parent_name": "DataResponsePublisher"}, "Structs/DataResponsePublisher.html#/s:9Alamofire21DataResponsePublisherV6result7Combine03AnyD0Vys6ResultOyxAA7AFErrorOGs5NeverOGyF": {"name": "result()", "abstract": "<p>Publishes only the <code>Result</code> of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponse.html\">DataResponse</a></code> value.</p>", "parent_name": "DataResponsePublisher"}, "Structs/DataResponsePublisher.html#/s:9Alamofire21DataResponsePublisherV5value7Combine03AnyD0VyxAA7AFErrorOGyF": {"name": "value()", "abstract": "<p>Publishes the <code>Result</code> of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponse.html\">DataResponse</a></code> as a single <code>Value</code> or fail with the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code> instance.</p>", "parent_name": "DataResponsePublisher"}, "Structs/DataResponsePublisher.html#/s:7Combine9PublisherP7receive10subscriberyqd___tAA10SubscriberRd__7FailureQyd__AGRtz5InputQyd__6OutputRtzlF": {"name": "receive(subscriber:)", "parent_name": "DataResponsePublisher"}, "Structs/DataResponsePublisher.html#/s:9Alamofire21DataResponsePublisherVAA10Foundation0B0VSgRszrlE_5queueACyAGGAA0B7RequestC_So012OS_dispatch_F0Ctcfc": {"name": "init(_:queue:)", "abstract": "<p>Creates an instance which publishes a <code>DataResponse&lt;Data?, AFError&gt;</code> value without serialization.</p>", "parent_name": "DataResponsePublisher"}, "Structs/ResponseCacher/Behavior.html#/s:9Alamofire14ResponseCacherV8BehaviorO5cacheyA2EmF": {"name": "cache", "abstract": "<p>Stores the cached response in the cache.</p>", "parent_name": "Behavior"}, "Structs/ResponseCacher/Behavior.html#/s:9Alamofire14ResponseCacherV8BehaviorO10doNotCacheyA2EmF": {"name": "doNotCache", "abstract": "<p>Prevents the cached response from being stored in the cache.</p>", "parent_name": "Behavior"}, "Structs/ResponseCacher/Behavior.html#/s:9Alamofire14ResponseCacherV8BehaviorO6modifyyAESo19NSCachedURLResponseCSgSo20NSURLSessionDataTaskC_AHtYbccAEmF": {"name": "modify(_:)", "abstract": "<p>Modifies the cached response before storing it in the cache.</p>", "parent_name": "Behavior"}, "Structs/ResponseCacher/Behavior.html": {"name": "Behavior", "abstract": "<p>Defines the behavior of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher.html\">ResponseCacher</a></code> type.</p>", "parent_name": "ResponseCacher"}, "Structs/ResponseCacher.html#/s:9Alamofire14ResponseCacherV5cacheACvpZ": {"name": "cache", "abstract": "<p>Returns a <code>ResponseCacher</code> with a <code>.cache</code> <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher/Behavior.html\">Behavior</a></code>.</p>", "parent_name": "ResponseCacher"}, "Structs/ResponseCacher.html#/s:9Alamofire14ResponseCacherV10doNotCacheACvpZ": {"name": "doNotCache", "abstract": "<p>Returns a <code>ResponseCacher</code> with a <code>.doNotCache</code> <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher/Behavior.html\">Behavior</a></code>.</p>", "parent_name": "ResponseCacher"}, "Structs/ResponseCacher.html#/s:9Alamofire14ResponseCacherV8behaviorAC8BehaviorOvp": {"name": "behavior", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher/Behavior.html\">Behavior</a></code> of the <code>ResponseCacher</code>.</p>", "parent_name": "ResponseCacher"}, "Structs/ResponseCacher.html#/s:9Alamofire14ResponseCacherV8behaviorA2C8BehaviorO_tcfc": {"name": "init(behavior:)", "abstract": "<p>Creates a <code>ResponseCacher</code> instance from the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher/Behavior.html\">Behavior</a></code>.</p>", "parent_name": "ResponseCacher"}, "Structs/ResponseCacher.html#/s:9Alamofire21CachedResponseHandlerP8dataTask_09willCacheC010completionySo016NSURLSessionDataF0C_So19NSCachedURLResponseCyAJSgctF": {"name": "dataTask(_:willCacheResponse:completion:)", "parent_name": "ResponseCacher"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionV4typexvp": {"name": "type", "abstract": "<p>Stores the type or meta-type of any extended type.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVyACyxGxcfc": {"name": "init(_:)", "abstract": "<p>Create an instance from the provided value.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo25NSURLSessionConfigurationCRbzlE7defaultAEvpZ": {"name": "default", "abstract": "<p>Alamofire&rsquo;s default configuration. Same as <code>URLSessionConfiguration.default</code> but adds Alamofire default", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo25NSURLSessionConfigurationCRbzlE9ephemeralAEvpZ": {"name": "ephemeral", "abstract": "<p><code>.ephemeral</code> configuration with Alamofire&rsquo;s default <code>Accept-Language</code>, <code>Accept-Encoding</code>, and <code>User-Agent</code>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE12certificatesSaySo17SecCertificateRefaGvp": {"name": "certificates", "abstract": "<p>Returns all valid <code>cer</code>, <code>crt</code>, and <code>der</code> certificates in the bundle.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE10publicKeysSaySo9SecKeyRefaGvp": {"name": "publicKeys", "abstract": "<p>Returns all public keys for the valid certificates in the bundle.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE5paths19forResourcesOfTypesSaySSGAH_tF": {"name": "paths(forResourcesOfTypes:)", "abstract": "<p>Returns all pathnames for the resources identified by the provided file extensions.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8evaluate13afterApplyingySo0c6PolicyE0a_tKF": {"name": "evaluate(afterApplying:)", "abstract": "<p>Evaluates <code>self</code> after applying the <code>SecPolicy</code> value provided.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8validate6policy13errorProducerySo0c6PolicyE0a_s5Error_ps5Int32V_So0cD10ResultTypeVtXEtKF": {"name": "validate(policy:errorProducer:)", "abstract": "<p>Attempts to validate <code>self</code> using the <code>SecPolicy</code> provided and transforming any error produced using the closure passed.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE5apply6policyAESo0c6PolicyE0a_tKF": {"name": "apply(policy:)", "abstract": "<p>Applies a <code>SecPolicy</code> to <code>self</code>, throwing if it fails.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8evaluateyyKF": {"name": "evaluate()", "abstract": "<p>Evaluate <code>self</code>, throwing an <code>Error</code> if evaluation fails.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8validate13errorProducerys5Error_ps5Int32V_So0cD10ResultTypeVtXE_tKF": {"name": "validate(errorProducer:)", "abstract": "<p>Validate <code>self</code>, passing any failure values through <code>errorProducer</code>.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE21setAnchorCertificatesyySaySo0c11CertificateE0aGKF": {"name": "setAnchorCertificates(_:)", "abstract": "<p>Sets a custom certificate chain on <code>self</code>, allowing full validation of a self-signed certificate and its chain.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE10publicKeysSaySo0c3KeyE0aGvp": {"name": "publicKeys", "abstract": "<p>The public keys contained in <code>self</code>.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE12certificatesSaySo0c11CertificateE0aGvp": {"name": "certificates", "abstract": "<p>The <code>SecCertificate</code>s contained in <code>self</code>.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE15certificateDataSay10Foundation0G0VGvp": {"name": "certificateData", "abstract": "<p>The <code>Data</code> values for all certificates contained in <code>self</code>.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE24performDefaultValidation7forHostySS_tKF": {"name": "performDefaultValidation(forHost:)", "abstract": "<p>Validates <code>self</code> after applying <code>SecPolicy.af.default</code>. This evaluation does not validate the hostname.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE17performValidation7forHostySS_tKF": {"name": "performValidation(forHost:)", "abstract": "<p>Validates <code>self</code> after applying <code>SecPolicy.af.hostname(host)</code>, which performs the default validation as well as", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE7defaultAEvpZ": {"name": "default", "abstract": "<p>Creates a <code>SecPolicy</code> instance which will validate server certificates but not require a host name match.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE8hostnameyAESSFZ": {"name": "hostname(_:)", "abstract": "<p>Creates a <code>SecPolicy</code> instance which will validate server certificates and much match the provided hostname.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE10revocation7optionsAeA24RevocationTrustEvaluatorC7OptionsV_tKFZ": {"name": "revocation(options:)", "abstract": "<p>Creates a <code>SecPolicy</code> which checks the revocation of certificates.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASaySo17SecCertificateRefaGRszlE4dataSay10Foundation4DataVGvp": {"name": "data", "abstract": "<p>All <code>Data</code> values for the contained <code>SecCertificate</code>s.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASaySo17SecCertificateRefaGRszlE10publicKeysSaySo0c3KeyE0aGvp": {"name": "publicKeys", "abstract": "<p>All public <code>Sec<PERSON>ey</code> values for the contained <code>SecCertificate</code>s.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo17SecCertificateRefaRszlE9publicKeySo0cgE0aSgvp": {"name": "public<PERSON>ey", "abstract": "<p>The public key for <code>self</code>, if it can be extracted.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAAs5Int32VRszlE9isSuccessSbvp": {"name": "isSuccess", "abstract": "<p>Returns whether <code>self</code> is <code>errSecSuccess</code>.</p>", "parent_name": "AlamofireExtension"}, "Structs/AlamofireExtension.html#/s:9Alamofire0A9ExtensionVAASo18SecTrustResultTypeVRszlE9isSuccessSbvp": {"name": "isSuccess", "abstract": "<p>Returns whether <code>self</code> is <code>.unspecified</code> or <code>.proceed</code>.</p>", "parent_name": "AlamofireExtension"}, "Structs/DecodableWebSocketMessageDecoder/Error.html#/s:9Alamofire32DecodableWebSocketMessageDecoderV5ErrorO8decodingyAEyx_GsAD_pcAGmSeRzs8SendableRzlF": {"name": "decoding(_:)", "abstract": "<p>Undocumented</p>", "parent_name": "Error"}, "Structs/DecodableWebSocketMessageDecoder/Error.html#/s:9Alamofire32DecodableWebSocketMessageDecoderV5ErrorO07unknownE0yAEyx_GSS_tcAGmSeRzs8SendableRzlF": {"name": "unknownMessage(description:)", "abstract": "<p>Undocumented</p>", "parent_name": "Error"}, "Structs/DecodableWebSocketMessageDecoder/Error.html": {"name": "Error", "abstract": "<p>Undocumented</p>", "parent_name": "DecodableWebSocketMessageDecoder"}, "Structs/DecodableWebSocketMessageDecoder.html#/s:9Alamofire32DecodableWebSocketMessageDecoderV7decoderAA04DataF0_pvp": {"name": "decoder", "abstract": "<p>Undocumented</p>", "parent_name": "DecodableWebSocketMessageDecoder"}, "Structs/DecodableWebSocketMessageDecoder.html#/s:9Alamofire32DecodableWebSocketMessageDecoderV7decoderACyxGAA04DataF0_p_tcfc": {"name": "init(decoder:)", "abstract": "<p>Undocumented</p>", "parent_name": "DecodableWebSocketMessageDecoder"}, "Structs/DecodableWebSocketMessageDecoder.html#/s:9Alamofire32DecodableWebSocketMessageDecoderV6decodeyxSo012NSURLSessioncD4TaskC10FoundationE0E0OKF": {"name": "decode(_:)", "abstract": "<p>Undocumented</p>", "parent_name": "DecodableWebSocketMessageDecoder"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV7request10Foundation10URLRequestVSgvp": {"name": "request", "abstract": "<p>The URL request sent to the server.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV8responseSo17NSHTTPURLResponseCSgvp": {"name": "response", "abstract": "<p>The server&rsquo;s response to the URL request.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV7fileURL10Foundation0E0VSgvp": {"name": "fileURL", "abstract": "<p>The final destination URL of the data returned from the server after it is moved.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV10resumeData10Foundation0E0VSgvp": {"name": "resumeData", "abstract": "<p>The resume data generated if the request was cancelled.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV7metricsSo23NSURLSessionTaskMetricsCSgvp": {"name": "metrics", "abstract": "<p>The final metrics of the response.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV21serializationDurationSdvp": {"name": "serializationDuration", "abstract": "<p>The time taken to serialize the response.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV6results6ResultOyxq_Gvp": {"name": "result", "abstract": "<p>The result of response serialization.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV5valuexSgvp": {"name": "value", "abstract": "<p>Returns the associated value of the result if it is a success, <code>nil</code> otherwise.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV5errorq_Sgvp": {"name": "error", "abstract": "<p>Returns the associated error value if the result if it is a failure, <code>nil</code> otherwise.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV7request8response7fileURL10resumeData7metrics21serializationDuration6resultACyxq_G10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAL0G0VSgAL0I0VSgSo23NSURLSessionTaskMetricsCSgSds6ResultOyxq_Gtcfc": {"name": "init(request:response:fileURL:resumeData:metrics:serializationDuration:result:)", "abstract": "<p>Creates a <code>DownloadResponse</code> instance with the specified parameters derived from response serialization.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV11descriptionSSvp": {"name": "description", "abstract": "<p>The textual representation used when written to an output stream, which includes whether the result was a", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV16debugDescriptionSSvp": {"name": "debugDescription", "abstract": "<p>The debug textual representation used when written to an output stream, which includes the URL request, the URL", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV3mapyACyqd__q_Gqd__xXEs8SendableRd__lF": {"name": "map(_:)", "abstract": "<p>Evaluates the given closure when the result of this <code>DownloadResponse</code> is a success, passing the unwrapped", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV6tryMapyACyqd__s5Error_pGqd__xKXEs8SendableRd__lF": {"name": "tryMap(_:)", "abstract": "<p>Evaluates the given closure when the result of this <code>DownloadResponse</code> is a success, passing the unwrapped", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV8mapErroryACyxqd__Gqd__q_XEs0E0Rd__lF": {"name": "mapError(_:)", "abstract": "<p>Evaluates the specified closure when the <code>DownloadResponse</code> is a failure, passing the unwrapped error as a parameter.</p>", "parent_name": "DownloadResponse"}, "Structs/DownloadResponse.html#/s:9Alamofire16DownloadResponseV11tryMapErroryACyxs0F0_pGqd__q_KXEsAERd__lF": {"name": "tryMapError(_:)", "abstract": "<p>Evaluates the specified closure when the <code>DownloadResponse</code> is a failure, passing the unwrapped error as a parameter.</p>", "parent_name": "DownloadResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV7request10Foundation10URLRequestVSgvp": {"name": "request", "abstract": "<p>The URL request sent to the server.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV8responseSo17NSHTTPURLResponseCSgvp": {"name": "response", "abstract": "<p>The server&rsquo;s response to the URL request.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV4data10Foundation0B0VSgvp": {"name": "data", "abstract": "<p>The data returned by the server.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV7metricsSo23NSURLSessionTaskMetricsCSgvp": {"name": "metrics", "abstract": "<p>The final metrics of the response.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV21serializationDurationSdvp": {"name": "serializationDuration", "abstract": "<p>The time taken to serialize the response.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV6results6ResultOyxq_Gvp": {"name": "result", "abstract": "<p>The result of response serialization.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV5valuexSgvp": {"name": "value", "abstract": "<p>Returns the associated value of the result if it is a success, <code>nil</code> otherwise.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV5errorq_Sgvp": {"name": "error", "abstract": "<p>Returns the associated error value if the result if it is a failure, <code>nil</code> otherwise.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV7request8response4data7metrics21serializationDuration6resultACyxq_G10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0B0VSgSo23NSURLSessionTaskMetricsCSgSds6ResultOyxq_Gtcfc": {"name": "init(request:response:data:metrics:serializationDuration:result:)", "abstract": "<p>Creates a <code>DataResponse</code> instance with the specified parameters derived from the response serialization.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV11descriptionSSvp": {"name": "description", "abstract": "<p>The textual representation used when written to an output stream, which includes whether the result was a", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV16debugDescriptionSSvp": {"name": "debugDescription", "abstract": "<p>The debug textual representation used when written to an output stream, which includes (if available) a summary", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV3mapyACyqd__q_Gqd__xXEs8SendableRd__lF": {"name": "map(_:)", "abstract": "<p>Evaluates the specified closure when the result of this <code>DataResponse</code> is a success, passing the unwrapped", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV6tryMapyACyqd__s5Error_pGqd__xKXEs8SendableRd__lF": {"name": "tryMap(_:)", "abstract": "<p>Evaluates the given closure when the result of this <code>DataResponse</code> is a success, passing the unwrapped result", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV8mapErroryACyxqd__Gqd__q_XEs0E0Rd__lF": {"name": "mapError(_:)", "abstract": "<p>Evaluates the specified closure when the <code>DataResponse</code> is a failure, passing the unwrapped error as a parameter.</p>", "parent_name": "DataResponse"}, "Structs/DataResponse.html#/s:9Alamofire12DataResponseV11tryMapErroryACyxs0F0_pGqd__q_KXEsAERd__lF": {"name": "tryMapError(_:)", "abstract": "<p>Evaluates the specified closure when the <code>DataResponse</code> is a failure, passing the unwrapped error as a parameter.</p>", "parent_name": "DataResponse"}, "Structs/JSONEncoding/Error.html#/s:9Alamofire12JSONEncodingV5ErrorO17invalidJSONObjectyA2EmF": {"name": "invalidJSONObject", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/JSONEncoding.html\">JSONEncoding</a></code> attempted to encode an invalid JSON object. Usually this means the value included types not", "parent_name": "Error"}, "Structs/JSONEncoding/Error.html#/s:9Alamofire12JSONEncodingV5ErrorO20localizedDescriptionSSvp": {"name": "localizedDescription", "abstract": "<p>Undocumented</p>", "parent_name": "Error"}, "Structs/JSONEncoding/Error.html": {"name": "Error", "abstract": "<p>Error produced by <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/JSONEncoding.html\">JSONEncoding</a></code>.</p>", "parent_name": "JSONEncoding"}, "Structs/JSONEncoding.html#/s:9Alamofire12JSONEncodingV7defaultACvpZ": {"name": "default", "abstract": "<p>Returns a <code>JSONEncoding</code> instance with default writing options.</p>", "parent_name": "JSONEncoding"}, "Structs/JSONEncoding.html#/s:9Alamofire12JSONEncodingV13prettyPrintedACvpZ": {"name": "prettyPrinted", "abstract": "<p>Returns a <code>JSONEncoding</code> instance with <code>.prettyPrinted</code> writing options.</p>", "parent_name": "JSONEncoding"}, "Structs/JSONEncoding.html#/s:9Alamofire12JSONEncodingV7optionsSo20NSJSONWritingOptionsVvp": {"name": "options", "abstract": "<p>The options for writing the parameters as JSON data.</p>", "parent_name": "JSONEncoding"}, "Structs/JSONEncoding.html#/s:9Alamofire12JSONEncodingV7optionsACSo20NSJSONWritingOptionsV_tcfc": {"name": "init(options:)", "abstract": "<p>Creates an instance using the specified <code>WritingOptions</code>.</p>", "parent_name": "JSONEncoding"}, "Structs/JSONEncoding.html#/s:9Alamofire17ParameterEncodingP6encode_4with10Foundation10URLRequestVAA0G11Convertible_p_SDySSs8Sendable_pGSgtKF": {"name": "encode(_:with:)", "parent_name": "JSONEncoding"}, "Structs/JSONEncoding.html#/s:9Alamofire12JSONEncodingV6encode_14withJSONObject10Foundation10URLRequestVAA0G11Convertible_p_ypSgtKF": {"name": "encode(_:withJSONObject:)", "abstract": "<p>Encodes any JSON compatible object into a <code>URLRequest</code>.</p>", "parent_name": "JSONEncoding"}, "Structs/URLEncoding/BoolEncoding.html#/s:9Alamofire11URLEncodingV12BoolEncodingO7numericyA2EmF": {"name": "numeric", "abstract": "<p>Encode <code>true</code> as <code>1</code> and <code>false</code> as <code>0</code>. This is the default behavior.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Structs/URLEncoding/BoolEncoding.html#/s:9Alamofire11URLEncodingV12BoolEncodingO7literalyA2EmF": {"name": "literal", "abstract": "<p>Encode <code>true</code> and <code>false</code> as string literals.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Structs/URLEncoding/ArrayEncoding.html#/s:9Alamofire11URLEncodingV13ArrayEncodingO8bracketsyA2EmF": {"name": "brackets", "abstract": "<p>An empty set of square brackets is appended to the key for every value. This is the default behavior.</p>", "parent_name": "ArrayEncoding"}, "Structs/URLEncoding/ArrayEncoding.html#/s:9Alamofire11URLEncodingV13ArrayEncodingO10noBracketsyA2EmF": {"name": "noBrackets", "abstract": "<p>No brackets are appended. The key is encoded as is.</p>", "parent_name": "ArrayEncoding"}, "Structs/URLEncoding/ArrayEncoding.html#/s:9Alamofire11URLEncodingV13ArrayEncodingO15indexInBracketsyA2EmF": {"name": "indexInBrackets", "abstract": "<p>Brackets containing the item index are appended. This matches the jQuery and Node.js behavior.</p>", "parent_name": "ArrayEncoding"}, "Structs/URLEncoding/ArrayEncoding.html#/s:9Alamofire11URLEncodingV13ArrayEncodingO6customyAES2S_SitYbccAEmF": {"name": "custom(_:)", "abstract": "<p>Provide a custom array key encoding with the given closure.</p>", "parent_name": "ArrayEncoding"}, "Structs/URLEncoding/Destination.html#/s:9Alamofire11URLEncodingV11DestinationO15methodDependentyA2EmF": {"name": "methodDependent", "abstract": "<p>Applies encoded query string result to existing query string for <code>GET</code>, <code>HEAD</code> and <code>DELETE</code> requests and", "parent_name": "Destination"}, "Structs/URLEncoding/Destination.html#/s:9Alamofire11URLEncodingV11DestinationO11queryStringyA2EmF": {"name": "queryString", "abstract": "<p>Sets or appends encoded query string result to existing query string.</p>", "parent_name": "Destination"}, "Structs/URLEncoding/Destination.html#/s:9Alamofire11URLEncodingV11DestinationO8httpBodyyA2EmF": {"name": "httpBody", "abstract": "<p>Sets encoded query string result as the HTTP body of the URL request.</p>", "parent_name": "Destination"}, "Structs/URLEncoding/Destination.html": {"name": "Destination", "abstract": "<p>Defines whether the url-encoded query string is applied to the existing query string or HTTP body of the", "parent_name": "URLEncoding"}, "Structs/URLEncoding/ArrayEncoding.html": {"name": "ArrayEncoding", "abstract": "<p>Configures how <code>Array</code> parameters are encoded.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding/BoolEncoding.html": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": "<p>Configures how <code>Bool</code> parameters are encoded.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV7defaultACvpZ": {"name": "default", "abstract": "<p>Returns a default <code>URLEncoding</code> instance with a <code>.methodDependent</code> destination.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV11queryStringACvpZ": {"name": "queryString", "abstract": "<p>Returns a <code>URLEncoding</code> instance with a <code>.queryString</code> destination.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV8httpBodyACvpZ": {"name": "httpBody", "abstract": "<p>Returns a <code>URLEncoding</code> instance with an <code>.httpBody</code> destination.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV11destinationAC11DestinationOvp": {"name": "destination", "abstract": "<p>The destination defining where the encoded query string is to be applied to the URL request.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV13arrayEncodingAC05ArrayD0Ovp": {"name": "arrayEncoding", "abstract": "<p>The encoding to use for <code>Array</code> parameters.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV12boolEncodingAC04BoolD0Ovp": {"name": "boolEncoding", "abstract": "<p>The encoding to use for <code>Bool</code> parameters.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV11destination13arrayEncoding04boolE0A2C11DestinationO_AC05ArrayE0OAC04BoolE0Otcfc": {"name": "init(destination:arrayEncoding:boolEncoding:)", "abstract": "<p>Creates an instance using the specified parameters.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire17ParameterEncodingP6encode_4with10Foundation10URLRequestVAA0G11Convertible_p_SDySSs8Sendable_pGSgtKF": {"name": "encode(_:with:)", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV15queryComponents7fromKey5valueSaySS_SStGSS_yptF": {"name": "queryComponents(fromKey:value:)", "abstract": "<p>Creates a percent-escaped, URL encoded query string components from the given key-value pair recursively.</p>", "parent_name": "URLEncoding"}, "Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV6escapeyS2SF": {"name": "escape(_:)", "abstract": "<p>Creates a percent-escaped string following RFC 3986 for a query string key or value.</p>", "parent_name": "URLEncoding"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV7connectACvpZ": {"name": "connect", "abstract": "<p><code>CONNECT</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV6deleteACvpZ": {"name": "delete", "abstract": "<p><code>DELETE</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV3getACvpZ": {"name": "get", "abstract": "<p><code>GET</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV4headACvpZ": {"name": "head", "abstract": "<p><code>HEAD</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV7optionsACvpZ": {"name": "options", "abstract": "<p><code>OPTIONS</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV5patchACvpZ": {"name": "patch", "abstract": "<p><code>PATCH</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV4postACvpZ": {"name": "post", "abstract": "<p><code>POST</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV3putACvpZ": {"name": "put", "abstract": "<p><code>PUT</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV5queryACvpZ": {"name": "query", "abstract": "<p><code>QUERY</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:9Alamofire10HTTPMethodV5traceACvpZ": {"name": "trace", "abstract": "<p><code>TRACE</code> method.</p>", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:SY8rawValue03RawB0Qzvp": {"name": "rawValue", "parent_name": "HTTPMethod"}, "Structs/HTTPMethod.html#/s:SY8rawValuexSg03RawB0Qz_tcfc": {"name": "init(rawValue:)", "parent_name": "HTTPMethod"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV4nameSSvp": {"name": "name", "abstract": "<p>Name of the header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV5valueSSvp": {"name": "value", "abstract": "<p>Value of the header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV4name5valueACSS_SStcfc": {"name": "init(name:value:)", "abstract": "<p>Creates an instance from the given <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV4nameSSvp\">name</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV5valueSSvp\">value</a></code>.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:s23CustomStringConvertibleP11descriptionSSvp": {"name": "description", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV6acceptyACSSFZ": {"name": "accept(_:)", "abstract": "<p>Returns an <code>Accept</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV13acceptCharsetyACSSFZ": {"name": "acceptCharset(_:)", "abstract": "<p>Returns an <code>Accept-Charset</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV14acceptLanguageyACSSFZ": {"name": "acceptLanguage(_:)", "abstract": "<p>Returns an <code>Accept-Language</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV14acceptEncodingyACSSFZ": {"name": "acceptEncoding(_:)", "abstract": "<p>Returns an <code>Accept-Encoding</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV13authorization8username8passwordACSS_SStFZ": {"name": "authorization(username:password:)", "abstract": "<p>Returns a <code>Basic</code> <code>Authorization</code> header using the <code>username</code> and <code>password</code> provided.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV13authorization11bearerTokenACSS_tFZ": {"name": "authorization(bearerToken:)", "abstract": "<p>Returns a <code>Bearer</code> <code>Authorization</code> header using the <code>bearerToken</code> provided.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV13authorizationyACSSFZ": {"name": "authorization(_:)", "abstract": "<p>Returns an <code>Authorization</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV18contentDispositionyACSSFZ": {"name": "contentDisposition(_:)", "abstract": "<p>Returns a <code>Content-Disposition</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV15contentEncodingyACSSFZ": {"name": "contentEncoding(_:)", "abstract": "<p>Returns a <code>Content-Encoding</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV11contentTypeyACSSFZ": {"name": "contentType(_:)", "abstract": "<p>Returns a <code>Content-Type</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV9userAgentyACSSFZ": {"name": "userAgent(_:)", "abstract": "<p>Returns a <code>User-Agent</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV17websocketProtocolyACSSFZ": {"name": "websocketProtocol(_:)", "abstract": "<p>Returns a <code>Sec-WebSocket-Protocol</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV21defaultAcceptEncodingACvpZ": {"name": "defaultAcceptEncoding", "abstract": "<p>Returns Alamofire&rsquo;s default <code>Accept-Encoding</code> header, appropriate for the encodings supported by particular OS", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV21defaultAcceptLanguageACvpZ": {"name": "defaultAcceptLanguage", "abstract": "<p>Returns Alamofire&rsquo;s default <code>Accept-Language</code> header, generated by querying <code>Locale</code> for the user&rsquo;s", "parent_name": "HTTPHeader"}, "Structs/HTTPHeader.html#/s:9Alamofire10HTTPHeaderV16defaultUserAgentACvpZ": {"name": "defaultUserAgent", "abstract": "<p>Returns Alamofire&rsquo;s default <code>User-Agent</code> header.</p>", "parent_name": "HTTPHeader"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersVACycfc": {"name": "init()", "abstract": "<p>Creates an empty instance.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersVyACSayAA10HTTPHeaderVGcfc": {"name": "init(_:)", "abstract": "<p>Creates an instance from an array of <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html\">HTTPHeader</a></code>s. Duplicate case-insensitive names are collapsed into the last", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersVyACSDyS2SGcfc": {"name": "init(_:)", "abstract": "<p>Creates an instance from a <code>[String: String]</code>. Duplicate case-insensitive names are collapsed into the last name", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV3add4name5valueySS_SStF": {"name": "add(name:value:)", "abstract": "<p>Case-insensitively updates or appends an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html\">HTTPHeader</a></code> into the instance using the provided <code>name</code> and <code>value</code>.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV3addyyAA10HTTPHeaderVF": {"name": "add(_:)", "abstract": "<p>Case-insensitively updates or appends the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html\">HTTPHeader</a></code> into the instance.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV6update4name5valueySS_SStF": {"name": "update(name:value:)", "abstract": "<p>Case-insensitively updates or appends an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html\">HTTPHeader</a></code> into the instance using the provided <code>name</code> and <code>value</code>.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV6updateyyAA10HTTPHeaderVF": {"name": "update(_:)", "abstract": "<p>Case-insensitively updates or appends the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html\">HTTPHeader</a></code> into the instance.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV6remove4nameySS_tF": {"name": "remove(name:)", "abstract": "<p>Case-insensitively removes an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeader.html\">HTTPHeader</a></code>, if it exists, from the instance.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV4sortyyF": {"name": "sort()", "abstract": "<p>Sort the current instance by header name, case insensitively.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV6sortedACyF": {"name": "sorted()", "abstract": "<p>Returns an instance sorted by header name.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV5value3forSSSgSS_tF": {"name": "value(for:)", "abstract": "<p>Case-insensitively find a header&rsquo;s value by name.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersVySSSgSScip": {"name": "subscript(_:)", "abstract": "<p>Case-insensitively access the header with the given name.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV10dictionarySDyS2SGvp": {"name": "dictionary", "abstract": "<p>The dictionary representation of all headers.</p>", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:s30ExpressibleByDictionaryLiteralP010dictionaryD0x3KeyQz_5ValueQztd_tcfc": {"name": "init(dictionaryLiteral:)", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:s25ExpressibleByArrayLiteralP05arrayD0x0cD7ElementQzd_tcfc": {"name": "init(arrayLiteral:)", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:ST12makeIterator0B0QzyF": {"name": "makeIterator()", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:Sl10startIndex0B0Qzvp": {"name": "startIndex", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:Sl8endIndex0B0Qzvp": {"name": "endIndex", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:Sly7ElementQz5IndexQzcip": {"name": "subscript(_:)", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:Sl5index5after5IndexQzAD_tF": {"name": "index(after:)", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:s23CustomStringConvertibleP11descriptionSSvp": {"name": "description", "parent_name": "HTTPHeaders"}, "Structs/HTTPHeaders.html#/s:9Alamofire11HTTPHeadersV7defaultACvpZ": {"name": "default", "abstract": "<p>The default set of <code>HTTPHeaders</code> used by Alamofire. Includes <code>Accept-Encoding</code>, <code>Accept-Language</code>, and", "parent_name": "HTTPHeaders"}, "Structs/StringStreamSerializer.html#/s:9Alamofire22StringStreamSerializerVACycfc": {"name": "init()", "abstract": "<p>Creates an instance.</p>", "parent_name": "StringStreamSerializer"}, "Structs/StringStreamSerializer.html#/s:9Alamofire20DataStreamSerializerP9serializey16SerializedObjectQz10Foundation0B0VKF": {"name": "serialize(_:)", "parent_name": "StringStreamSerializer"}, "Structs/PassthroughStreamSerializer.html#/s:9Alamofire27PassthroughStreamSerializerVACycfc": {"name": "init()", "abstract": "<p>Creates an instance.</p>", "parent_name": "PassthroughStreamSerializer"}, "Structs/PassthroughStreamSerializer.html#/s:9Alamofire20DataStreamSerializerP9serializey16SerializedObjectQz10Foundation0B0VKF": {"name": "serialize(_:)", "parent_name": "PassthroughStreamSerializer"}, "Structs/DecodableStreamSerializer.html#/s:9Alamofire25DecodableStreamSerializerV7decoderAA11DataDecoder_pvp": {"name": "decoder", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code> used to decode incoming <code>Data</code>.</p>", "parent_name": "DecodableStreamSerializer"}, "Structs/DecodableStreamSerializer.html#/s:9Alamofire25DecodableStreamSerializerV16dataPreprocessorAA04DataF0_pvp": {"name": "dataPreprocessor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code> incoming <code>Data</code> is passed through before being passed to the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code>.</p>", "parent_name": "DecodableStreamSerializer"}, "Structs/DecodableStreamSerializer.html#/s:9Alamofire25DecodableStreamSerializerV7decoder16dataPreprocessorACyxGAA11DataDecoder_p_AA0hG0_ptcfc": {"name": "init(decoder:dataPreprocessor:)", "abstract": "<p>Creates an instance with the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code>.</p>", "parent_name": "DecodableStreamSerializer"}, "Structs/DecodableStreamSerializer.html#/s:9Alamofire20DataStreamSerializerP9serializey16SerializedObjectQz10Foundation0B0VKF": {"name": "serialize(_:)", "parent_name": "DecodableStreamSerializer"}, "Structs/DecodableStreamSerializer.html": {"name": "DecodableStreamSerializer", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code> which uses the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code> to serialize the incoming <code>Data</code>.</p>"}, "Structs/PassthroughStreamSerializer.html": {"name": "PassthroughStreamSerializer", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code> which performs no serialization on incoming <code>Data</code>.</p>"}, "Structs/StringStreamSerializer.html": {"name": "StringStreamSerializer", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code> which serializes incoming stream <code>Data</code> into <code>UTF8</code>-decoded <code>String</code> values.</p>"}, "Structs/HTTPHeaders.html": {"name": "HTTPHeaders", "abstract": "<p>An order-preserving and case-insensitive representation of HTTP headers.</p>"}, "Structs/HTTPHeader.html": {"name": "HTTPHeader", "abstract": "<p>A representation of a single HTTP header&rsquo;s name / value pair.</p>"}, "Structs/HTTPMethod.html": {"name": "HTTPMethod", "abstract": "<p>Type representing HTTP methods. Raw <code>String</code> value is stored and compared case-sensitively, so"}, "Structs/URLEncoding.html": {"name": "URLEncoding", "abstract": "<p>Creates a url-encoded query string to be set as or appended to any existing URL query string or set as the HTTP"}, "Structs/JSONEncoding.html": {"name": "JSONEncoding", "abstract": "<p>Uses <code>JSONSerialization</code> to create a JSON representation of the parameters object, which is set as the body of the"}, "Structs/DataResponse.html": {"name": "DataResponse", "abstract": "<p>Type used to store all values associated with a serialized response of a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> or <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code>.</p>"}, "Structs/DownloadResponse.html": {"name": "DownloadResponse", "abstract": "<p>Used to store all data associated with a serialized response of a download request.</p>"}, "Structs/DecodableWebSocketMessageDecoder.html": {"name": "DecodableWebSocketMessageDecoder", "abstract": "<p>Undocumented</p>"}, "Structs/AlamofireExtension.html": {"name": "AlamofireExtension", "abstract": "<p>Type that acts as a generic extension point for all <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/AlamofireExtended.html\">AlamofireExtended</a></code> types.</p>"}, "Structs/ResponseCacher.html": {"name": "ResponseCacher", "abstract": "<p><code>ResponseCacher</code> is a convenience <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/CachedResponseHandler.html\">CachedResponseHandler</a></code> making it easy to cache, not cache, or modify a cached"}, "Structs/DataResponsePublisher.html": {"name": "DataResponsePublisher", "abstract": "<p>A Combine <code>Publisher</code> that publishes the <code>DataResponse&lt;Value, AFError&gt;</code> of the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code>.</p>"}, "Structs/DataStreamPublisher.html": {"name": "DataStreamPublisher", "abstract": "<p>Undocumented</p>"}, "Structs/DownloadResponsePublisher.html": {"name": "DownloadResponsePublisher", "abstract": "<p>A Combine <code>Publisher</code> that publishes the <code>DownloadResponse&lt;Value, AFError&gt;</code> of the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code>.</p>"}, "Structs/DataTask.html": {"name": "DataTask", "abstract": "<p>Value used to <code>await</code> a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponse.html\">DataResponse</a></code> and associated values.</p>"}, "Structs/DownloadTask.html": {"name": "DownloadTask", "abstract": "<p>Value used to <code>await</code> a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponse.html\">DownloadResponse</a></code> and associated values.</p>"}, "Structs/DataStreamTask.html": {"name": "DataStreamTask", "abstract": "<p>Undocumented</p>"}, "Structs/StreamOf.html": {"name": "StreamOf", "abstract": "<p>An asynchronous sequence generated from an underlying <code>AsyncStream</code>. Only produced by Alamofire.</p>"}, "Structs/Redirector.html": {"name": "Redirector", "abstract": "<p><code>Redirector</code> is a convenience <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RedirectHandler.html\">RedirectHandler</a></code> making it easy to follow, not follow, or modify a redirect.</p>"}, "Structs/DeflateRequestCompressor.html": {"name": "DeflateRequestCompressor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> which compresses outgoing <code>URLRequest</code> bodies using the <code>deflate</code> <code>Content-Encoding</code> and adds the"}, "Structs/RequestAdapterState.html": {"name": "RequestAdapterState", "abstract": "<p>Stores all state associated with a <code>URLRequest</code> being adapted.</p>"}, "Structs/PassthroughPreprocessor.html": {"name": "PassthroughPreprocessor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code> that returns passed <code>Data</code> without any transform.</p>"}, "Structs/GoogleXSSIPreprocessor.html": {"name": "GoogleXSSIPreprocessor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code> that trims Google&rsquo;s typical <code>)]}&#39;,\\n</code> XSSI JSON header.</p>"}, "Structs/URLResponseSerializer.html": {"name": "URLResponseSerializer", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DownloadResponseSerializerProtocol.html\">DownloadResponseSerializerProtocol</a></code> that performs only <code>Error</code> checking and ensures that a downloaded <code>fileURL</code>"}, "Structs/Empty.html": {"name": "Empty", "abstract": "<p>Type representing an empty value. Use <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Empty.html#/s:9Alamofire5EmptyV5valueACvpZ\">Empty.value</a></code> to get the static instance.</p>"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingP8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "abstract": "<p>Evaluates the given <code>SecTrust</code> value for the given <code>host</code>.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingPA2A010RevocationC9EvaluatorCRszrlE18revocationCheckingAEvpZ": {"name": "revocationChecking", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/RevocationTrustEvaluator.html\">RevocationTrustEvaluator</a></code> instance.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingPA2A010RevocationC9EvaluatorCRszrlE18revocationChecking24performDefaultValidation12validateHost7optionsAESb_SbAE7OptionsVtFZ": {"name": "revocationChecking(performDefaultValidation:validateHost:options:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/RevocationTrustEvaluator.html\">RevocationTrustEvaluator</a></code> using the provided parameters.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingPA2A018PinnedCertificatesC9EvaluatorCRszrlE06pinnedF0AEvpZ": {"name": "pinnedCertificates", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/PinnedCertificatesTrustEvaluator.html\">PinnedCertificatesTrustEvaluator</a></code> instance.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingPA2A018PinnedCertificatesC9EvaluatorCRszrlE06pinnedF012certificates016acceptSelfSignedF024performDefaultValidation12validateHostAESaySo17SecCertificateRefaG_S3btFZ": {"name": "pinnedCertificates(certificates:acceptSelfSignedCertificates:performDefaultValidation:validateHost:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/PinnedCertificatesTrustEvaluator.html\">PinnedCertificatesTrustEvaluator</a></code> using the provided parameters.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingPA2A010PublicKeysC9EvaluatorCRszrlE06publicF0AEvpZ": {"name": "publicKeys", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/PublicKeysTrustEvaluator.html\">PublicKeysTrustEvaluator</a></code> instance.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingPA2A010PublicKeysC9EvaluatorCRszrlE06publicF04keys24performDefaultValidation12validateHostAESaySo9SecKeyRefaG_S2btFZ": {"name": "publicKeys(keys:performDefaultValidation:validateHost:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/PublicKeysTrustEvaluator.html\">PublicKeysTrustEvaluator</a></code> from the provided parameters.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/ServerTrustEvaluating.html#/s:9Alamofire21ServerTrustEvaluatingPA2A09CompositeC9EvaluatorCRszrlE9composite10evaluatorsAESayAaB_pG_tFZ": {"name": "composite(evaluators:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/CompositeTrustEvaluator.html\">CompositeTrustEvaluator</a></code> from the provided evaluators.</p>", "parent_name": "ServerTrustEvaluating"}, "Protocols/DataDecoder.html#/s:9Alamofire11DataDecoderP6decode_4fromqd__qd__m_10Foundation0B0VtKSeRd__lF": {"name": "decode(_:from:)", "abstract": "<p>Decode <code>Data</code> into the provided type.</p>", "parent_name": "DataDecoder"}, "Protocols/EmptyResponse.html#/s:9Alamofire13EmptyResponseP10emptyValuexyFZ": {"name": "emptyValue()", "abstract": "<p>Empty value for the conforming type.</p>", "parent_name": "EmptyResponse"}, "Protocols/DataPreprocessor.html#/s:9Alamofire16DataPreprocessorP10preprocessy10Foundation0B0VAGKF": {"name": "preprocess(_:)", "abstract": "<p>Process           <code>Data</code> before it&rsquo;s handled by a serializer.</p>", "parent_name": "DataPreprocessor"}, "Protocols/DataPreprocessor.html#/s:9Alamofire16DataPreprocessorPA2A011PassthroughC0VRszrlE11passthroughAEvpZ": {"name": "passthrough", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/PassthroughPreprocessor.html\">PassthroughPreprocessor</a></code> instance.</p>", "parent_name": "DataPreprocessor"}, "Protocols/DataPreprocessor.html#/s:9Alamofire16DataPreprocessorPA2A22GoogleXSSIPreprocessorVRszrlE10googleXSSIAEvpZ": {"name": "googleXSSI", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/GoogleXSSIPreprocessor.html\">GoogleXSSIPreprocessor</a></code> instance.</p>", "parent_name": "DataPreprocessor"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerP16dataPreprocessorAA04DataE0_pvp": {"name": "dataPreprocessor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code> used to prepare incoming <code>Data</code> for serialization.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerP19emptyRequestMethodsShyAA10HTTPMethodVGvp": {"name": "emptyRequestMethods", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPMethod.html\">HTTPMethod</a></code>s for which empty response bodies are considered appropriate.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerP05emptyB5CodesShySiGvp": {"name": "emptyResponseCodes", "abstract": "<p>HTTP response codes for which empty response bodies are considered appropriate.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPAAE23defaultDataPreprocessorAA0eF0_pvpZ": {"name": "defaultDataPreprocessor", "abstract": "<p>Default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code>. <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/PassthroughPreprocessor.html\">PassthroughPreprocessor</a></code> by default.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPAAE26defaultEmptyRequestMethodsShyAA10HTTPMethodVGvpZ": {"name": "defaultEmptyRequestMethods", "abstract": "<p>Default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPMethod.html\">HTTPMethod</a></code>s for which empty response bodies are always considered appropriate. <code>[.head]</code> by default.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPAAE012defaultEmptyB5CodesShySiGvpZ": {"name": "defaultEmptyResponseCodes", "abstract": "<p>HTTP response codes for which empty response bodies are always considered appropriate. <code>[204, 205]</code> by default.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPAAE018requestAllowsEmptyB4DataySbSg10Foundation10URLRequestVSgF": {"name": "requestAllowsEmptyResponseData(_:)", "abstract": "<p>Determines whether the <code>request</code> allows empty response bodies, if <code>request</code> exists.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPAAE019responseAllowsEmptyB4DataySbSgSo17NSHTTPURLResponseCSgF": {"name": "responseAllowsEmptyResponseData(_:)", "abstract": "<p>Determines whether the <code>response</code> allows empty response bodies, if <code>response</code> exists.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPAAE05emptyB7Allowed10forRequest8responseSb10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgtF": {"name": "emptyResponseAllowed(forRequest:response:)", "abstract": "<p>Determines whether <code>request</code> and <code>response</code> allow empty response bodies.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPAAE9decodable2of16dataPreprocessor7decoder05emptyB5Codes0I14RequestMethodsAA09DecodablebC0Cyqd__Gqd__m_AA04DataG0_pAA0N7Decoder_pShySiGShyAA10HTTPMethodVGtALRszSeRd__s8SendableRd__lFZ": {"name": "decodable(of:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DecodableResponseSerializer.html\">DecodableResponseSerializer</a></code> using the values provided.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPA2A04DatabC0CRszrlE4dataAEvpZ": {"name": "data", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataResponseSerializer.html\">DataResponseSerializer</a></code> instance.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPA2A04DatabC0CRszrlE4data0E12Preprocessor05emptyB5Codes0G14RequestMethodsAeA0dF0_p_ShySiGShyAA10HTTPMethodVGtFZ": {"name": "data(dataPreprocessor:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataResponseSerializer.html\">DataResponseSerializer</a></code> using the provided parameters.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPA2A06StringbC0CRszrlE6stringAEvpZ": {"name": "string", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/StringResponseSerializer.html\">StringResponseSerializer</a></code> instance.</p>", "parent_name": "ResponseSerializer"}, "Protocols/ResponseSerializer.html#/s:9Alamofire18ResponseSerializerPA2A06StringbC0CRszrlE6string16dataPreprocessor8encoding05emptyB5Codes0I14RequestMethodsAeA04DataG0_p_SS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtFZ": {"name": "string(dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/StringResponseSerializer.html\">StringResponseSerializer</a></code> with the provided values.</p>", "parent_name": "ResponseSerializer"}, "Protocols/DownloadResponseSerializerProtocol.html#/s:9Alamofire34DownloadResponseSerializerProtocolP16SerializedObjectQa": {"name": "SerializedObject", "abstract": "<p>The type of serialized object to be created.</p>", "parent_name": "DownloadResponseSerializerProtocol"}, "Protocols/DownloadResponseSerializerProtocol.html#/s:9Alamofire34DownloadResponseSerializerProtocolP09serializeB07request8response7fileURL5error16SerializedObjectQz10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0J0VSgs5Error_pSgtKF": {"name": "serializeDownload(request:response:fileURL:error:)", "abstract": "<p>Serialize the downloaded response <code>Data</code> from disk into the provided type.</p>", "parent_name": "DownloadResponseSerializerProtocol"}, "Protocols/DownloadResponseSerializerProtocol.html#/s:9Alamofire34DownloadResponseSerializerProtocolPA2A011URLResponseD0VRszrlE3urlAEvpZ": {"name": "url", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/URLResponseSerializer.html\">URLResponseSerializer</a></code> instance.</p>", "parent_name": "DownloadResponseSerializerProtocol"}, "Protocols/DataResponseSerializerProtocol.html#/s:9Alamofire30DataResponseSerializerProtocolP16SerializedObjectQa": {"name": "SerializedObject", "abstract": "<p>The type of serialized object to be created.</p>", "parent_name": "DataResponseSerializerProtocol"}, "Protocols/DataResponseSerializerProtocol.html#/s:9Alamofire30DataResponseSerializerProtocolP9serialize7request8response4data5error16SerializedObjectQz10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0B0VSgs5Error_pSgtKF": {"name": "serialize(request:response:data:error:)", "abstract": "<p>Serialize the response <code>Data</code> into the provided type.</p>", "parent_name": "DataResponseSerializerProtocol"}, "Protocols/RequestRetrier.html#/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF": {"name": "retry(_:for:dueTo:completion:)", "abstract": "<p>Determines whether the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> should be retried by calling the <code>completion</code> closure.</p>", "parent_name": "RequestRetrier"}, "Protocols/RequestRetrier.html#/s:9Alamofire14RequestRetrierPA2A0C0CRszrlE7retrier5usingAEyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOctc_tFZ": {"name": "retrier(using:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Retrier.html\">Retrier</a></code> using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbTypealiases.html#/s:9Alamofire12RetryHandlera\">RetryHandler</a></code> closure.</p>", "parent_name": "RequestRetrier"}, "Protocols/RequestAdapter.html#/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:for:completion:)", "abstract": "<p>Inspects and adapts the specified <code>URLRequest</code> in some manner and calls the completion handler with the Result.</p>", "parent_name": "RequestAdapter"}, "Protocols/RequestAdapter.html#/s:9Alamofire14RequestAdapterP5adapt_5using10completiony10Foundation10URLRequestV_AA0bC5StateVys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:using:completion:)", "abstract": "<p>Inspects and adapts the specified <code>URLRequest</code> in some manner and calls the completion handler with the Result.</p>", "parent_name": "RequestAdapter"}, "Protocols/RequestAdapter.html#/s:9Alamofire14RequestAdapterPA2A0C0CRszrlE7adapter5usingAEy10Foundation10URLRequestV_AA7SessionCys6ResultOyAJs5Error_pGctc_tFZ": {"name": "adapter(using:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Adapter.html\">Adapter</a></code> using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbTypealiases.html#/s:9Alamofire12AdaptHandlera\">AdaptHandler</a></code> closure.</p>", "parent_name": "RequestAdapter"}, "Protocols/RequestInterceptor.html#/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:for:completion:)", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF": {"name": "retry(_:for:dueTo:completion:)", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A07DeflateB10CompressorVRszrlE07deflateE0AEvpZ": {"name": "deflateCompressor", "abstract": "<p>Create a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DeflateRequestCompressor.html\">DeflateRequestCompressor</a></code> with default <code>duplicateHeaderBehavior</code> and <code>shouldCompressBodyData</code> values.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A07DeflateB10CompressorVRszrlE07deflateE023duplicateHeaderBehavior22shouldCompressBodyDataA2E09DuplicatehI0O_Sb10Foundation0M0VYbctFZ": {"name": "deflateCompressor(duplicateHeaderBehavior:shouldCompressBodyData:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DeflateRequestCompressor.html\">DeflateRequestCompressor</a></code> with the provided <code>DuplicateHeaderBehavior</code> and <code>shouldCompressBodyData</code>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor7adapter7retrierAEy10Foundation10URLRequestV_AA7SessionCys6ResultOyAKs5Error_pGctc_yAA0B0C_AMsAP_pyAA05RetryJ0OctctFZ": {"name": "interceptor(adapter:retrier:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Interceptor.html\">Interceptor</a></code> using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbTypealiases.html#/s:9Alamofire12AdaptHandlera\">AdaptHandler</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbTypealiases.html#/s:9Alamofire12RetryHandlera\">RetryHandler</a></code> closures.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor7adapter7retrierAeA0B7Adapter_p_AA0B7Retrier_ptFZ": {"name": "interceptor(adapter:retrier:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Interceptor.html\">Interceptor</a></code> using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code> instances.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor8adapters8retriers12interceptorsAESayAA0B7Adapter_pG_SayAA0B7Retrier_pGSayAaB_pGtFZ": {"name": "interceptor(adapters:retriers:interceptors:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Interceptor.html\">Interceptor</a></code> using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code>s, <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code>s, and <code>RequestInterceptor</code>s.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A11RetryPolicyCRszrlE05retryE0AEvpZ": {"name": "retryPolicy", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/RetryPolicy.html\">RetryPolicy</a></code> instance.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A11RetryPolicyCRszrlE05retryE00F5Limit22exponentialBackoffBase0hI5Scale20retryableHTTPMethods0L15HTTPStatusCodes0l8URLErrorO0AESu_SuSdShyAA10HTTPMethodVGShySiGShy10Foundation0P0V4CodeVGtFZ": {"name": "retryPolicy(retryLimit:exponentialBackoffBase:exponentialBackoffScale:retryableHTTPMethods:retryableHTTPStatusCodes:retryableURLErrorCodes:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/RetryPolicy.html\">RetryPolicy</a></code> from the specified parameters.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A25ConnectionLostRetryPolicyCRszrlE010connectionefG0AEvpZ": {"name": "connectionLostRetryPolicy", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ConnectionLostRetryPolicy.html\">ConnectionLostRetryPolicy</a></code> instance.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RequestInterceptor.html#/s:9Alamofire18RequestInterceptorPA2A25ConnectionLostRetryPolicyCRszrlE010connectionefG010retryLimit22exponentialBackoffBase0kL5Scale20retryableHTTPMethodsAESu_SuSdShyAA10HTTPMethodVGtFZ": {"name": "connectionLostRetryPolicy(retryLimit:exponentialBackoffBase:exponentialBackoffScale:retryableHTTPMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ConnectionLostRetryPolicy.html\">ConnectionLostRetryPolicy</a></code> instance from the specified parameters.</p>", "parent_name": "RequestInterceptor"}, "Protocols/RedirectHandler.html#/s:9Alamofire15RedirectHandlerP4task_18willBeRedirectedTo3for10completionySo16NSURLSessionTaskC_10Foundation10URLRequestVSo17NSHTTPURLResponseCyALSgctF": {"name": "task(_:willBeRedirectedTo:for:completion:)", "abstract": "<p>Determines how the HTTP redirect response should be redirected to the new request.</p>", "parent_name": "RedirectHandler"}, "Protocols/RedirectHandler.html#/s:9Alamofire15RedirectHandlerPA2A10RedirectorVRszrlE6followAEvpZ": {"name": "follow", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector.html\">Redirector</a></code> which follows redirects. Equivalent to <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector.html#/s:9Alamofire10RedirectorV6followACvpZ\">Redirector.follow</a></code>.</p>", "parent_name": "RedirectHandler"}, "Protocols/RedirectHandler.html#/s:9Alamofire15RedirectHandlerPA2A10RedirectorVRszrlE11doNotFollowAEvpZ": {"name": "doNotFollow", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector.html\">Redirector</a></code> which does not follow redirects. Equivalent to <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector.html#/s:9Alamofire10RedirectorV11doNotFollowACvpZ\">Redirector.doNotFollow</a></code>.</p>", "parent_name": "RedirectHandler"}, "Protocols/RedirectHandler.html#/s:9Alamofire15RedirectHandlerPA2A10RedirectorVRszrlE6modify5usingAE10Foundation10URLRequestVSgSo16NSURLSessionTaskC_AJSo17NSHTTPURLResponseCtYbc_tFZ": {"name": "modify(using:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Redirector.html\">Redirector</a></code> which modifies the redirected <code>URLRequest</code> using the provided closure.</p>", "parent_name": "RedirectHandler"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP5queueSo012OS_dispatch_D0Cvp": {"name": "queue", "abstract": "<p>The <code>DispatchQueue</code> onto which Alamofire&rsquo;s root <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/CompositeEventMonitor.html\">CompositeEventMonitor</a></code> will dispatch events. <code>.main</code> by default.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_25didBecomeInvalidWithErrorySo12NSURLSessionC_s0J0_pSgtF": {"name": "urlSession(_:didBecomeInvalidWithError:)", "abstract": "<p>Event called during <code>URLSessionDelegate</code>&lsquo;s <code>urlSession(_:didBecomeInvalidWithError:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task10didReceiveySo12NSURLSessionC_So0I4TaskCSo28NSURLAuthenticationChallengeCtF": {"name": "urlSession(_:task:didReceive:)", "abstract": "<p>Event called during <code>URLSessionTaskDelegate</code>&lsquo;s <code>urlSession(_:task:didReceive:completionHandler:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task15didSendBodyData14totalBytesSent0kl10ExpectedToH0ySo12NSURLSessionC_So0P4TaskCs5Int64VA2NtF": {"name": "urlSession(_:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:)", "abstract": "<p>Event called during <code>URLSessionTaskDelegate</code>&lsquo;s <code>urlSession(_:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_22taskNeedsNewBodyStreamySo12NSURLSessionC_So0K4TaskCtF": {"name": "urlSession(_:taskNeedsNewBodyStream:)", "abstract": "<p>Event called during <code>URLSessionTaskDelegate</code>&lsquo;s <code>urlSession(_:task:needNewBodyStream:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task26willPerformHTTPRedirection10newRequestySo12NSURLSessionC_So0L4TaskCSo17NSHTTPURLResponseC10Foundation10URLRequestVtF": {"name": "urlSession(_:task:willPerformHTTPRedirection:newRequest:)", "abstract": "<p>Event called during <code>URLSessionTaskDelegate</code>&lsquo;s <code>urlSession(_:task:willPerformHTTPRedirection:newRequest:completionHandler:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task19didFinishCollectingySo12NSURLSessionC_So0J4TaskCSo0jK7MetricsCtF": {"name": "urlSession(_:task:didFinishCollecting:)", "abstract": "<p>Event called during <code>URLSessionTaskDelegate</code>&lsquo;s <code>urlSession(_:task:didFinishCollecting:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task20didCompleteWithErrorySo12NSURLSessionC_So0K4TaskCs0J0_pSgtF": {"name": "urlSession(_:task:didCompleteWithError:)", "abstract": "<p>Event called during <code>URLSessionTaskDelegate</code>&lsquo;s <code>urlSession(_:task:didCompleteWithError:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_28taskIsWaitingForConnectivityySo12NSURLSessionC_So0K4TaskCtF": {"name": "urlSession(_:taskIsWaitingForConnectivity:)", "abstract": "<p>Event called during <code>URLSessionTaskDelegate</code>&lsquo;s <code>urlSession(_:taskIsWaitingForConnectivity:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0CSo13NSURLResponseCtF": {"name": "urlSession(_:dataTask:didReceive:)", "abstract": "<p>Event called during <code>URLSessionDataDelegate</code>&lsquo;s <code>urlSession(_:dataTask:didReceive:completionHandler:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0C10Foundation0K0VtF": {"name": "urlSession(_:dataTask:didReceive:)", "abstract": "<p>Event called during <code>URLSessionDataDelegate</code>&lsquo;s <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0CSo13NSURLResponseCtF\">urlSession(_:dataTask:didReceive:)</a></code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask17willCacheResponseySo12NSURLSessionC_So0k4DataG0CSo19NSCachedURLResponseCtF": {"name": "urlSession(_:dataTask:willCacheResponse:)", "abstract": "<p>Event called during <code>URLSessionDataDelegate</code>&lsquo;s <code>urlSession(_:dataTask:willCacheResponse:completionHandler:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask17didResumeAtOffset18expectedTotalBytesySo12NSURLSessionC_So0o8DownloadG0Cs5Int64VAMtF": {"name": "urlSession(_:downloadTask:didResumeAtOffset:expectedTotalBytes:)", "abstract": "<p>Event called during <code>URLSessionDownloadDelegate</code>&lsquo;s <code>urlSession(_:downloadTask:didResumeAtOffset:expectedTotalBytes:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask12didWriteData17totalBytesWritten0kl10ExpectedToI0ySo12NSURLSessionC_So0p8DownloadG0Cs5Int64VA2NtF": {"name": "urlSession(_:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:)", "abstract": "<p>Event called during <code>URLSessionDownloadDelegate</code>&lsquo;s <code>urlSession(_:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask22didFinishDownloadingToySo12NSURLSessionC_So0l8DownloadG0C10Foundation3URLVtF": {"name": "urlSession(_:downloadTask:didFinishDownloadingTo:)", "abstract": "<p>Event called during <code>URLSessionDownloadDelegate</code>&lsquo;s <code>urlSession(_:downloadTask:didFinishDownloadingTo:)</code> method.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_26didCreateInitialURLRequestyAA7RequestC_10Foundation0H0VtF": {"name": "request(_:didCreateInitialURLRequest:)", "abstract": "<p>Event called when a <code>URLRequest</code> is first created for a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>. If a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> is active, the", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateURLRequestWithErroryAA7RequestC_AA7AFErrorOtF": {"name": "request(_:didFailToCreateURLRequestWithError:)", "abstract": "<p>Event called when the attempt to create a <code>URLRequest</code> from a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s original <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value fails.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_22didAdaptInitialRequest2toyAA0H0C_10Foundation10URLRequestVAKtF": {"name": "request(_:didAdaptInitialRequest:to:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> adapts the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s initial <code>URLRequest</code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_24didFailToAdaptURLRequest9withErroryAA7RequestC_10Foundation0I0VAA7AFErrorOtF": {"name": "request(_:didFailToAdaptURLRequest:withError:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> fails to adapt the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s initial <code>URLRequest</code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateURLRequestyAA7RequestC_10Foundation0G0VtF": {"name": "request(_:didCreateURLRequest:)", "abstract": "<p>Event called when a final <code>URLRequest</code> is created for a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCreateTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didCreateTask:)", "abstract": "<p>Event called when a <code>URLSessionTask</code> subclass instance is created for a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didGatherMetricsyAA7RequestC_So016NSURLSessionTaskG0CtF": {"name": "request(_:didGatherMetrics:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> receives a <code>URLSessionTaskMetrics</code> value.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_11didFailTask14earlyWithErroryAA7RequestC_So012NSURLSessionG0CAA7AFErrorOtF": {"name": "request(_:didFailTask:earlyWithError:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> fails due to an error created by Alamofire. e.g. When certificate pinning fails.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_15didCompleteTask4withyAA7RequestC_So012NSURLSessionG0CAA7AFErrorOSgtF": {"name": "request(_:didCompleteTask:with:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s task completes, possibly with an error. A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> may receive this event", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP17requestIsRetryingyyAA7RequestCF": {"name": "requestIsRetrying(_:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> is about to be retried.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidFinishyyAA7RequestCF": {"name": "requestDidFinish(_:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> finishes and response serializers are being called.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidResumeyyAA7RequestCF": {"name": "requestDidResume(_:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> receives a <code>resume</code> call.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didResumeTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didResumeTask:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s associated <code>URLSessionTask</code> is resumed.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP17requestDidSuspendyyAA7RequestCF": {"name": "requestDidSuspend(_:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> receives a <code>suspend</code> call.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_14didSuspendTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didSuspendTask:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s associated <code>URLSessionTask</code> is suspended.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidCancelyyAA7RequestCF": {"name": "requestDidCancel(_:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> receives a <code>cancel</code> call.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCancelTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didCancelTask:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s associated <code>URLSessionTask</code> is cancelled.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response4data10withResultyAA04DataG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAK0L0VSgs0K0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:data:withResult:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> calls a <code>Validation</code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA11DataRequestC_AA0hG0Vy10Foundation0H0VSgAA7AFErrorOGtF": {"name": "request(_:didParseResponse:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponse.html\">DataResponse&lt;Data?&gt;</a></code> value without calling a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA11DataRequestC_AA0hG0Vyqd__AA7AFErrorOGts8SendableRd__lF": {"name": "request(_:didParseResponse:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> calls a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> and creates a generic <code>DataResponse&lt;Value, AFError&gt;</code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response10withResultyAA010DataStreamG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCs0J0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:withResult:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code> calls a <code>Validation</code> closure.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_14didParseStreamyAA04DataG7RequestC_s6ResultOyqd__AA7AFErrorOGts8SendableRd__lF": {"name": "request(_:didParseStream:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code> produces a value from streamed <code>Data</code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateUploadableyAA13UploadRequestC_AG0G0OtF": {"name": "request(_:didCreateUploadable:)", "abstract": "<p>Event called when an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> creates its <code>Uploadable</code> value, indicating the type of upload it represents.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateUploadableWithErroryAA13UploadRequestC_AA7AFErrorOtF": {"name": "request(_:didFailToCreateUploadableWithError:)", "abstract": "<p>Event called when an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> failed to create its <code>Uploadable</code> value due to an error.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_21didProvideInputStreamyAA13UploadRequestC_So07NSInputH0CtF": {"name": "request(_:didProvideInputStream:)", "abstract": "<p>Event called when an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> provides the <code>InputStream</code> from its <code>Uploadable</code> value. This only occurs if", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_25didFinishDownloadingUsing4withyAA15DownloadRequestC_So16NSURLSessionTaskCs6ResultOy10Foundation3URLVAA7AFErrorOGtF": {"name": "request(_:didFinishDownloadingUsing:with:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code>&lsquo;s <code>URLSessionDownloadTask</code> finishes and the temporary file has been moved.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_23didCreateDestinationURLyAA15DownloadRequestC_10Foundation0H0VtF": {"name": "request(_:didCreateDestinationURL:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code>&lsquo;s <code>Destination</code> closure is called and creates the destination URL the", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response7fileURL10withResultyAA08DownloadG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAK0J0VSgs0L0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:fileURL:withResult:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> calls a <code>Validation</code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA15DownloadRequestC_AA0hG0Vy10Foundation3URLVSgAA7AFErrorOGtF": {"name": "request(_:didParseResponse:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> creates a <code>DownloadResponse&lt;URL?, AFError&gt;</code> without calling a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code>.</p>", "parent_name": "EventMonitor"}, "Protocols/EventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA15DownloadRequestC_AA0hG0Vyqd__AA7AFErrorOGts8SendableRd__lF": {"name": "request(_:didParseResponse:)", "abstract": "<p>Event called when a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> calls a <code>DownloadResponseSerializer</code> and creates a generic <code>DownloadResponse&lt;Value, AFError&gt;</code></p>", "parent_name": "EventMonitor"}, "Protocols/CachedResponseHandler.html#/s:9Alamofire21CachedResponseHandlerP8dataTask_09willCacheC010completionySo016NSURLSessionDataF0C_So19NSCachedURLResponseCyAJSgctF": {"name": "dataTask(_:willCacheResponse:completion:)", "abstract": "<p>Determines whether the HTTP response should be stored in the cache.</p>", "parent_name": "CachedR<PERSON>po<PERSON><PERSON><PERSON><PERSON>"}, "Protocols/CachedResponseHandler.html#/s:9Alamofire21CachedResponseHandlerPA2A0C6CacherVRszrlE5cacheAEvpZ": {"name": "cache", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher.html\">ResponseCacher</a></code> which caches the response, if allowed. Equivalent to <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher.html#/s:9Alamofire14ResponseCacherV5cacheACvpZ\">ResponseCacher.cache</a></code>.</p>", "parent_name": "CachedR<PERSON>po<PERSON><PERSON><PERSON><PERSON>"}, "Protocols/CachedResponseHandler.html#/s:9Alamofire21CachedResponseHandlerPA2A0C6CacherVRszrlE10doNotCacheAEvpZ": {"name": "doNotCache", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher.html\">ResponseCacher</a></code> which does not cache the response. Equivalent to <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher.html#/s:9Alamofire14ResponseCacherV10doNotCacheACvpZ\">ResponseCacher.doNotCache</a></code>.</p>", "parent_name": "CachedR<PERSON>po<PERSON><PERSON><PERSON><PERSON>"}, "Protocols/CachedResponseHandler.html#/s:9Alamofire21CachedResponseHandlerPA2A0C6CacherVRszrlE6modify5usingAESo19NSCachedURLResponseCSgSo20NSURLSessionDataTaskC_AItYbc_tFZ": {"name": "modify(using:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/ResponseCacher.html\">ResponseCacher</a></code> which modifies the proposed <code>CachedURLResponse</code> using the provided closure.</p>", "parent_name": "CachedR<PERSON>po<PERSON><PERSON><PERSON><PERSON>"}, "Protocols/Authenticator.html#/s:9Alamofire13AuthenticatorP10CredentialQa": {"name": "Credential", "abstract": "<p>The type of credential associated with the <code>Authenticator</code> instance.</p>", "parent_name": "Authenticator"}, "Protocols/Authenticator.html#/s:9Alamofire13AuthenticatorP5apply_2toy10CredentialQz_10Foundation10URLRequestVztF": {"name": "apply(_:to:)", "abstract": "<p>Applies the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/Authenticator.html#/s:9Alamofire13AuthenticatorP10CredentialQa\">Credential</a></code> to the <code>URLRequest</code>.</p>", "parent_name": "Authenticator"}, "Protocols/Authenticator.html#/s:9Alamofire13AuthenticatorP7refresh_3for10completiony10CredentialQz_AA7SessionCys6ResultOyAHs5Error_pGYbctF": {"name": "refresh(_:for:completion:)", "abstract": "<p>Refreshes the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/Authenticator.html#/s:9Alamofire13AuthenticatorP10CredentialQa\">Credential</a></code> and executes the <code>completion</code> closure with the <code>Result</code> once complete.</p>", "parent_name": "Authenticator"}, "Protocols/Authenticator.html#/s:9Alamofire13AuthenticatorP10didRequest_4with28failDueToAuthenticationErrorSb10Foundation10URLRequestV_So17NSHTTPURLResponseCs0J0_ptF": {"name": "didRequest(_:with:failDueToAuthenticationError:)", "abstract": "<p>Determines whether the <code>URLRequest</code> failed due to an authentication error based on the <code>HTTPURLResponse</code>.</p>", "parent_name": "Authenticator"}, "Protocols/Authenticator.html#/s:9Alamofire13AuthenticatorP9isRequest_17authenticatedWithSb10Foundation10URLRequestV_10CredentialQztF": {"name": "isRequest(_:authenticatedWith:)", "abstract": "<p>Determines whether the <code>URLRequest</code> is authenticated with the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/Authenticator.html#/s:9Alamofire13AuthenticatorP10CredentialQa\">Credential</a></code>.</p>", "parent_name": "Authenticator"}, "Protocols/AuthenticationCredential.html#/s:9Alamofire24AuthenticationCredentialP15requiresRefreshSbvp": {"name": "requiresRefresh", "abstract": "<p>Whether the credential requires a refresh. This property should always return <code>true</code> when the credential is", "parent_name": "AuthenticationCredential"}, "Protocols/AlamofireExtended.html#/s:9Alamofire0A8ExtendedP0B4TypeQa": {"name": "ExtendedType", "abstract": "<p>Type being extended.</p>", "parent_name": "AlamofireExtended"}, "Protocols/AlamofireExtended.html#/s:9Alamofire0A8ExtendedP2afAA0A9ExtensionVy0B4TypeQzGmvpZ": {"name": "af", "abstract": "<p>Static Alamofire extension point.</p>", "parent_name": "AlamofireExtended"}, "Protocols/AlamofireExtended.html#/s:9Alamofire0A8ExtendedP2afAA0A9ExtensionVy0B4TypeQzGvp": {"name": "af", "abstract": "<p>Instance Alamofire extension point.</p>", "parent_name": "AlamofireExtended"}, "Protocols/WebSocketMessageSerializer.html#/s:9Alamofire26WebSocketMessageSerializerP6OutputQa": {"name": "Output", "abstract": "<p>Undocumented</p>", "parent_name": "WebSocketMessageSerializer"}, "Protocols/WebSocketMessageSerializer.html#/s:9Alamofire26WebSocketMessageSerializerP7FailureQa": {"name": "Failure", "abstract": "<p>Undocumented</p>", "parent_name": "WebSocketMessageSerializer"}, "Protocols/WebSocketMessageSerializer.html#/s:9Alamofire26WebSocketMessageSerializerP6decodey6OutputQzSo012NSURLSessionbC4TaskC10FoundationE0D0OKF": {"name": "decode(_:)", "abstract": "<p>Undocumented</p>", "parent_name": "WebSocketMessageSerializer"}, "Protocols/WebSocketMessageSerializer.html#/s:9Alamofire26WebSocketMessageSerializerPAAE4json8decoding5usingAA09DecodablebcD7DecoderVyqd__Gqd__m_10Foundation11JSONDecoderCtAIRszSeRd__s8SendableRd__lFZ": {"name": "json(decoding:using:)", "abstract": "<p>Undocumented</p>", "parent_name": "WebSocketMessageSerializer"}, "Protocols/UploadableConvertible.html#/s:9Alamofire21UploadableConvertibleP06createB0AA13UploadRequestC0B0OyKF": {"name": "createUploadable()", "abstract": "<p>Produces an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest/Uploadable.html\">UploadRequest.Uploadable</a></code> value from the instance.</p>", "parent_name": "UploadableConvertible"}, "Protocols/URLRequestConvertible.html#/s:9Alamofire21URLRequestConvertibleP02asB010Foundation0B0VyKF": {"name": "asURLRequest()", "abstract": "<p>Returns a <code>URLRequest</code> or throws if an <code>Error</code> was encountered.</p>", "parent_name": "URLRequestConvertible"}, "Protocols/URLRequestConvertible.html#/s:9Alamofire21URLRequestConvertiblePAAE10urlRequest10Foundation0B0VSgvp": {"name": "urlRequest", "abstract": "<p>The <code>URLRequest</code> returned by discarding any <code>Error</code> encountered.</p>", "parent_name": "URLRequestConvertible"}, "Protocols/URLConvertible.html#/s:9Alamofire14URLConvertibleP5asURL10Foundation0D0VyKF": {"name": "asURL()", "abstract": "<p>Returns a <code>URL</code> from the conforming instance or throws.</p>", "parent_name": "URLConvertible"}, "Protocols/RequestDelegate.html#/s:9Alamofire15RequestDelegateP20sessionConfigurationSo012NSURLSessionE0Cvp": {"name": "sessionConfiguration", "abstract": "<p><code>URLSessionConfiguration</code> used to create the underlying <code>URLSessionTask</code>s.</p>", "parent_name": "RequestDelegate"}, "Protocols/RequestDelegate.html#/s:9Alamofire15RequestDelegateP16startImmediatelySbvp": {"name": "startImmediately", "abstract": "<p>Determines whether the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> should automatically call <code>resume()</code> when adding the first response handler.</p>", "parent_name": "RequestDelegate"}, "Protocols/RequestDelegate.html#/s:9Alamofire15RequestDelegateP7cleanup5afteryAA0B0C_tF": {"name": "cleanup(after:)", "abstract": "<p>Notifies the delegate the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> has reached a point where it needs cleanup.</p>", "parent_name": "RequestDelegate"}, "Protocols/RequestDelegate.html#/s:9Alamofire15RequestDelegateP11retryResult3for5dueTo10completionyAA0B0C_AA7AFErrorOyAA05RetryE0OYbctF": {"name": "retryResult(for:dueTo:completion:)", "abstract": "<p>Asynchronously ask the delegate whether a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> will be retried.</p>", "parent_name": "RequestDelegate"}, "Protocols/RequestDelegate.html#/s:9Alamofire15RequestDelegateP05retryB0_9withDelayyAA0B0C_SdSgtF": {"name": "retryRequest(_:withDelay:)", "abstract": "<p>Asynchronously retry the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>.</p>", "parent_name": "RequestDelegate"}, "Protocols/ParameterEncoding.html#/s:9Alamofire17ParameterEncodingP6encode_4with10Foundation10URLRequestVAA0G11Convertible_p_SDySSs8Sendable_pGSgtKF": {"name": "encode(_:with:)", "abstract": "<p>Creates a <code>URLRequest</code> by encoding parameters and applying them on the passed request.</p>", "parent_name": "ParameterEncoding"}, "Protocols/ParameterEncoder.html#/s:9Alamofire16ParameterEncoderP6encode_4into10Foundation10URLRequestVqd__Sg_AHtKSERd__s8SendableRd__lF": {"name": "encode(_:into:)", "abstract": "<p>Encode the provided <code>Encodable</code> parameters into <code>request</code>.</p>", "parent_name": "ParameterEncoder"}, "Protocols/ParameterEncoder.html#/s:9Alamofire16ParameterEncoderPA2A013JSONParameterC0CRszrlE4jsonAEvpZ": {"name": "json", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/JSONParameterEncoder.html\">JSONParameterEncoder</a></code> instance.</p>", "parent_name": "ParameterEncoder"}, "Protocols/ParameterEncoder.html#/s:9Alamofire16ParameterEncoderPA2A013JSONParameterC0CRszrlE4json7encoderAE10Foundation11JSONEncoderC_tFZ": {"name": "json(encoder:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/JSONParameterEncoder.html\">JSONParameterEncoder</a></code> using the provided <code>JSONEncoder</code>.</p>", "parent_name": "ParameterEncoder"}, "Protocols/ParameterEncoder.html#/s:9Alamofire16ParameterEncoderPA2A014URLEncodedFormbC0CRszrlE010urlEncodedE0AEvpZ": {"name": "urlEncodedForm", "abstract": "<p>Provides a default <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormParameterEncoder.html\">URLEncodedFormParameterEncoder</a></code> instance.</p>", "parent_name": "ParameterEncoder"}, "Protocols/ParameterEncoder.html#/s:9Alamofire16ParameterEncoderPA2A014URLEncodedFormbC0CRszrlE010urlEncodedE07encoder11destinationAeA0deC0C_AE11DestinationOtFZ": {"name": "urlEncodedForm(encoder:destination:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormParameterEncoder.html\">URLEncodedFormParameterEncoder</a></code> with the provided encoder and destination.</p>", "parent_name": "ParameterEncoder"}, "Protocols/DataStreamSerializer.html#/s:9Alamofire20DataStreamSerializerP16SerializedObjectQa": {"name": "SerializedObject", "abstract": "<p>Type produced from the serialized <code>Data</code>.</p>", "parent_name": "DataStreamSerializer"}, "Protocols/DataStreamSerializer.html#/s:9Alamofire20DataStreamSerializerP9serializey16SerializedObjectQz10Foundation0B0VKF": {"name": "serialize(_:)", "abstract": "<p>Serializes incoming <code>Data</code> into a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html#/s:9Alamofire20DataStreamSerializerP16SerializedObjectQa\">SerializedObject</a></code> value.</p>", "parent_name": "DataStreamSerializer"}, "Protocols/DataStreamSerializer.html#/s:9Alamofire20DataStreamSerializerPAAE9decodable2of7decoder16dataPreprocessorAA09DecodablecD0Vyqd__Gqd__m_AA0B7Decoder_pAA0bI0_ptAJRszSeRd__s8SendableRd__lFZ": {"name": "decodable(of:decoder:dataPreprocessor:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DecodableStreamSerializer.html\">DecodableStreamSerializer</a></code> instance with the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataPreprocessor.html\">DataPreprocessor</a></code>.</p>", "parent_name": "DataStreamSerializer"}, "Protocols/DataStreamSerializer.html#/s:9Alamofire20DataStreamSerializerPA2A011PassthroughcD0VRszrlE11passthroughAEvpZ": {"name": "passthrough", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/PassthroughStreamSerializer.html\">PassthroughStreamSerializer</a></code> instance.</p>", "parent_name": "DataStreamSerializer"}, "Protocols/DataStreamSerializer.html#/s:9Alamofire20DataStreamSerializerPA2A06StringcD0VRszrlE6stringAEvpZ": {"name": "string", "abstract": "<p>Provides a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StringStreamSerializer.html\">StringStreamSerializer</a></code> instance.</p>", "parent_name": "DataStreamSerializer"}, "Protocols/DataStreamSerializer.html": {"name": "DataStreamSerializer", "abstract": "<p>A type which can serialize incoming <code>Data</code>.</p>"}, "Protocols/ParameterEncoder.html": {"name": "ParameterEncoder", "abstract": "<p>A type that can encode any <code>Encodable</code> type into a <code>URLRequest</code>.</p>"}, "Protocols/ParameterEncoding.html": {"name": "ParameterEncoding", "abstract": "<p>A type used to define how a set of parameters are applied to a <code>URLRequest</code>.</p>"}, "Protocols/RequestDelegate.html": {"name": "RequestDelegate", "abstract": "<p>Protocol abstraction for <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>&lsquo;s communication back to the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/SessionDelegate.html\">SessionDelegate</a></code>.</p>"}, "Protocols/URLConvertible.html": {"name": "URLConvertible", "abstract": "<p>Types adopting the <code>URLConvertible</code> protocol can be used to construct <code>URL</code>s, which can then be used to construct"}, "Protocols/URLRequestConvertible.html": {"name": "URLRequestConvertible", "abstract": "<p>Types adopting the <code>URLRequestConvertible</code> protocol can be used to safely construct <code>URLRequest</code>s.</p>"}, "Protocols/UploadableConvertible.html": {"name": "UploadableConvertible", "abstract": "<p>A type that can produce an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest/Uploadable.html\">UploadRequest.Uploadable</a></code> value.</p>"}, "Protocols.html#/s:9Alamofire17UploadConvertibleP": {"name": "UploadConvertible", "abstract": "<p>A type that can be converted to an upload, whether from an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest/Uploadable.html\">UploadRequest.Uploadable</a></code> or <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code>.</p>"}, "Protocols/WebSocketMessageSerializer.html": {"name": "WebSocketMessageSerializer", "abstract": "<p>Undocumented</p>"}, "Protocols/AlamofireExtended.html": {"name": "AlamofireExtended", "abstract": "<p>Protocol describing the <code>af</code> extension points for Alamofire extended types.</p>"}, "Protocols/AuthenticationCredential.html": {"name": "AuthenticationCredential", "abstract": "<p>Types adopting the <code>AuthenticationCredential</code> protocol can be used to authenticate <code>URLRequest</code>s.</p>"}, "Protocols/Authenticator.html": {"name": "Authenticator", "abstract": "<p>Types adopting the <code>Authenticator</code> protocol can be used to authenticate <code>URLRequest</code>s with an"}, "Protocols/CachedResponseHandler.html": {"name": "CachedR<PERSON>po<PERSON><PERSON><PERSON><PERSON>", "abstract": "<p>A type that handles whether the data task should store the HTTP response in the cache.</p>"}, "Protocols/EventMonitor.html": {"name": "EventMonitor", "abstract": "<p>Protocol outlining the lifetime events inside Alamofire. It includes both events received from the various"}, "Protocols/RedirectHandler.html": {"name": "RedirectHandler", "abstract": "<p>A type that handles how an HTTP redirect response from a remote server should be redirected to the new request.</p>"}, "Protocols/RequestInterceptor.html": {"name": "RequestInterceptor", "abstract": "<p>Type that provides both <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code> functionality.</p>"}, "Protocols/RequestAdapter.html": {"name": "RequestAdapter", "abstract": "<p>A type that can inspect and optionally adapt a <code>URLRequest</code> in some manner if necessary.</p>"}, "Protocols/RequestRetrier.html": {"name": "RequestRetrier", "abstract": "<p>A type that determines whether a request should be retried after being executed by the specified session manager"}, "Protocols/DataResponseSerializerProtocol.html": {"name": "DataResponseSerializerProtocol", "abstract": "<p>The type to which all data response serializers must conform in order to serialize a response.</p>"}, "Protocols/DownloadResponseSerializerProtocol.html": {"name": "DownloadResponseSerializerProtocol", "abstract": "<p>The type to which all download response serializers must conform in order to serialize a response.</p>"}, "Protocols/ResponseSerializer.html": {"name": "ResponseSerializer", "abstract": "<p>A serializer that can handle both data and download responses.</p>"}, "Protocols/DataPreprocessor.html": {"name": "DataPreprocessor", "abstract": "<p>Type used to preprocess <code>Data</code> before it handled by a serializer.</p>"}, "Protocols/EmptyResponse.html": {"name": "EmptyResponse", "abstract": "<p>Protocol representing an empty response. Use <code>T.emptyValue()</code> to get an instance.</p>"}, "Protocols/DataDecoder.html": {"name": "DataDecoder", "abstract": "<p>Any type which can decode <code>Data</code> into a <code>Decodable</code> type.</p>"}, "Protocols/ServerTrustEvaluating.html": {"name": "ServerTrustEvaluating", "abstract": "<p>A protocol describing the API used to evaluate server trusts.</p>"}, "Extensions/CharacterSet.html#/s:10Foundation12CharacterSetV9AlamofireE17afURLQueryAllowedACvpZ": {"name": "afURL<PERSON>ueryAllowed", "abstract": "<p>Creates a CharacterSet from RFC 3986 allowed characters.</p>", "parent_name": "CharacterSet"}, "Extensions/%5BServerTrustEvaluating%5D.html#/s:Sa9AlamofireAA21ServerTrustEvaluating_pRszlE8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "abstract": "<p>Evaluates the given <code>SecTrust</code> value for the given <code>host</code>.</p>", "parent_name": "[ServerTrustEvaluating]"}, "Extensions/URLComponents.html#/s:10Foundation13URLComponentsV9AlamofireE5asURLAA0E0VyKF": {"name": "asURL()", "abstract": "<p>Returns a <code>URL</code> if the <code>self</code>&lsquo;s <code>url</code> is not nil, otherwise throws.</p>", "parent_name": "URLComponents"}, "Extensions/URL.html#/s:10Foundation3URLV9AlamofireE02asB0ACyKF": {"name": "asURL()", "abstract": "<p>Returns <code>self</code>.</p>", "parent_name": "URL"}, "Extensions/String.html#/s:SS9AlamofireE5asURL10Foundation0C0VyKF": {"name": "asURL()", "abstract": "<p>Returns a <code>URL</code> if <code>self</code> can be used to initialize a <code>URL</code> instance, otherwise throws.</p>", "parent_name": "String"}, "Extensions/Notification.html#/s:10Foundation12NotificationV9AlamofireE7requestAD7RequestCSgvp": {"name": "request", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> contained by the instance&rsquo;s <code>userInfo</code>, <code>nil</code> otherwise.</p>", "parent_name": "Notification"}, "Extensions/URLSessionConfiguration.html#/s:So25NSURLSessionConfigurationC9AlamofireE7headersAC11HTTPHeadersVvp": {"name": "headers", "abstract": "<p>Returns <code>httpAdditionalHeaders</code> as <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeaders.html\">HTTPHeaders</a></code>.</p>", "parent_name": "URLSessionConfiguration"}, "Extensions/HTTPURLResponse.html#/s:So17NSHTTPURLResponseC9AlamofireE7headersAC11HTTPHeadersVvp": {"name": "headers", "abstract": "<p>Returns <code>allHeaderFields</code> as <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeaders.html\">HTTPHeaders</a></code>.</p>", "parent_name": "HTTPURLResponse"}, "Extensions/URLRequest.html#/s:10Foundation10URLRequestV9AlamofireE7headersAD11HTTPHeadersVvp": {"name": "headers", "abstract": "<p>Returns <code>allHTTPHeaderFields</code> as <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPHeaders.html\">HTTPHeaders</a></code>.</p>", "parent_name": "URLRequest"}, "Extensions/URLRequest.html#/s:10Foundation10URLRequestV9AlamofireE02asB0ACyKF": {"name": "asURLRequest()", "abstract": "<p>Returns <code>self</code>.</p>", "parent_name": "URLRequest"}, "Extensions/URLRequest.html#/s:10Foundation10URLRequestV9AlamofireE3url6method7headersAcD14URLConvertible_p_AD10HTTPMethodVAD11HTTPHeadersVSgtKcfc": {"name": "init(url:method:headers:)", "abstract": "<p>Creates an instance with the specified <code>url</code>, <code><a href=\"36f8f5912051ae747ef441d6511ca4cbExtensions/URLRequest.html#/s:10Foundation10URLRequestV9AlamofireE6methodAD10HTTPMethodVSgvp\">method</a></code>, and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbExtensions/URLRequest.html#/s:10Foundation10URLRequestV9AlamofireE7headersAD11HTTPHeadersVvp\">headers</a></code>.</p>", "parent_name": "URLRequest"}, "Extensions/URLRequest.html#/s:10Foundation10URLRequestV9AlamofireE6methodAD10HTTPMethodVSgvp": {"name": "method", "abstract": "<p>Returns the <code>httpMethod</code> as Alamofire&rsquo;s <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPMethod.html\">HTTPMethod</a></code> type.</p>", "parent_name": "URLRequest"}, "Extensions/URLRequest.html#/s:10Foundation10URLRequestV9AlamofireE8validateyyKF": {"name": "validate()", "abstract": "<p>Undocumented</p>", "parent_name": "URLRequest"}, "Extensions/Error.html#/s:s5ErrorP9AlamofireE9asAFErrorAC0D0OSgvp": {"name": "asAFError", "abstract": "<p>Returns the instance cast as an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code>.</p>", "parent_name": "Error"}, "Extensions/Error.html#/s:s5ErrorP9AlamofireE9asAFError10orFailWith4file4lineAC0D0OSSyXK_s12StaticStringVSutF": {"name": "asAFError(orFailWith:file:line:)", "abstract": "<p>Returns the instance cast as an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code>. If casting fails, a <code>fatalError</code> with the specified <code>message</code> is thrown.</p>", "parent_name": "Error"}, "Extensions/Error.html": {"name": "Error"}, "Extensions/URLRequest.html": {"name": "URLRequest"}, "Extensions/HTTPURLResponse.html": {"name": "HTTPURLResponse"}, "Extensions/URLSessionConfiguration.html": {"name": "URLSessionConfiguration"}, "Extensions/Notification.html": {"name": "Notification"}, "Extensions.html#/Protected": {"name": "Protected"}, "Extensions/String.html": {"name": "String"}, "Extensions/URL.html": {"name": "URL"}, "Extensions/URLComponents.html": {"name": "URLComponents"}, "Extensions.html#/s:10Foundation11JSONDecoderC": {"name": "JSONDecoder", "abstract": "<p><code>JSONDecoder</code> automatically conforms to <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code>.</p>"}, "Extensions.html#/s:10Foundation19PropertyListDecoderC": {"name": "PropertyListDecoder", "abstract": "<p><code>PropertyListDecoder</code> automatically conforms to <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code>.</p>"}, "Extensions/%5BServerTrustEvaluating%5D.html": {"name": "[ServerTrustEvaluating]"}, "Extensions.html#/c:objc(cs)NSBundle": {"name": "Bundle"}, "Extensions.html#/c:@T@SecTrustRef": {"name": "SecTrust"}, "Extensions.html#/c:@T@SecPolicyRef": {"name": "SecPolicy"}, "Extensions.html#/s:Sa": {"name": "Array"}, "Extensions.html#/c:@T@SecCertificateRef": {"name": "SecCertificate"}, "Extensions.html#/c:@T@OSStatus": {"name": "OSStatus"}, "Extensions.html#/c:@E@SecTrustResultType": {"name": "SecTrustResultType"}, "Extensions/CharacterSet.html": {"name": "CharacterSet"}, "Enums/RetryResult.html#/s:9Alamofire11RetryResultO5retryyA2CmF": {"name": "retry", "abstract": "<p>Retry should be attempted immediately.</p>", "parent_name": "RetryResult"}, "Enums/RetryResult.html#/s:9Alamofire11RetryResultO14retryWithDelayyACSdcACmF": {"name": "retryWithDelay(_:)", "abstract": "<p>Retry should be attempted after the associated <code>TimeInterval</code>.</p>", "parent_name": "RetryResult"}, "Enums/RetryResult.html#/s:9Alamofire11RetryResultO05doNotB0yA2CmF": {"name": "doNotRetry", "abstract": "<p>Do not retry.</p>", "parent_name": "RetryResult"}, "Enums/RetryResult.html#/s:9Alamofire11RetryResultO05doNotB9WithErroryACs0G0_pcACmF": {"name": "doNotRetryWithError(_:)", "abstract": "<p>Do not retry due to the associated <code>Error</code>.</p>", "parent_name": "RetryResult"}, "Enums/AuthenticationError.html#/s:9Alamofire19AuthenticationErrorO17missingCredentialyA2CmF": {"name": "missingCredential", "abstract": "<p>The credential was missing so the request could not be authenticated.</p>", "parent_name": "AuthenticationError"}, "Enums/AuthenticationError.html#/s:9Alamofire19AuthenticationErrorO16excessiveRefreshyA2CmF": {"name": "excessiveRefresh", "abstract": "<p>The credential was refreshed too many times within the <code>RefreshWindow</code>.</p>", "parent_name": "AuthenticationError"}, "Enums/AFError/URLRequestValidationFailureReason.html#/s:9Alamofire7AFErrorO33URLRequestValidationFailureReasonO20bodyDataInGETRequestyAE10Foundation0H0VcAEmF": {"name": "bodyDataInGETRequest(_:)", "abstract": "<p>URLRequest with GET method had body data.</p>", "parent_name": "URLRequestValidationFailureReason"}, "Enums/AFError/ServerTrustFailureReason/Output.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO6OutputV4hostSSvp": {"name": "host", "abstract": "<p>The host for which the evaluation was performed.</p>", "parent_name": "Output"}, "Enums/AFError/ServerTrustFailureReason/Output.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO6OutputV5trustSo03SecD3Refavp": {"name": "trust", "abstract": "<p>The <code>SecTrust</code> value which was evaluated.</p>", "parent_name": "Output"}, "Enums/AFError/ServerTrustFailureReason/Output.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO6OutputV6statuss5Int32Vvp": {"name": "status", "abstract": "<p>The <code>OSStatus</code> of evaluation operation.</p>", "parent_name": "Output"}, "Enums/AFError/ServerTrustFailureReason/Output.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO6OutputV6resultSo03SecD10ResultTypeVvp": {"name": "result", "abstract": "<p>The result of the evaluation operation.</p>", "parent_name": "Output"}, "Enums/AFError/ServerTrustFailureReason/Output.html": {"name": "Output", "abstract": "<p>The output of a server trust evaluation.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO19noRequiredEvaluatoryAESS_tcAEmF": {"name": "noRequiredEvaluator(host:)", "abstract": "<p>No <code>ServerTrustEvaluator</code> was found for the associated host.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO19noCertificatesFoundyA2EmF": {"name": "noCertificatesFound", "abstract": "<p>No certificates were found with which to perform the trust evaluation.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO17noPublicKeysFoundyA2EmF": {"name": "noPublicKeysFound", "abstract": "<p>No public keys were found with which to perform the trust evaluation.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO23policyApplicationFailedyAESo03SecD3Refa_So0j6PolicyK0as5Int32VtcAEmF": {"name": "policyApplicationFailed(trust:policy:status:)", "abstract": "<p>During evaluation, application of the associated <code>SecPolicy</code> failed.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO31settingAnchorCertificatesFailedyAEs5Int32V_SaySo17SecCertificateRefaGtcAEmF": {"name": "settingAnchorCertificatesFailed(status:certificates:)", "abstract": "<p>During evaluation, setting the associated anchor certificates failed.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO30revocationPolicyCreationFailedyA2EmF": {"name": "revocationPolicyCreationFailed", "abstract": "<p>During evaluation, creation of the revocation policy failed.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO21trustEvaluationFailedyAEs5Error_pSg_tcAEmF": {"name": "trustEvaluationFailed(error:)", "abstract": "<p><code>SecTrust</code> evaluation failed with the associated <code>Error</code>, if one was produced.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO23defaultEvaluationFailedyA2E6OutputV_tcAEmF": {"name": "defaultEvaluationFailed(output:)", "abstract": "<p>Default evaluation failed with the associated <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError/ServerTrustFailureReason/Output.html\">Output</a></code>.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO20hostValidationFailedyA2E6OutputV_tcAEmF": {"name": "hostValidationFailed(output:)", "abstract": "<p>Host validation failed with the associated <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError/ServerTrustFailureReason/Output.html\">Output</a></code>.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO21revocationCheckFailedyA2E6OutputV_AA010RevocationD9EvaluatorC7OptionsVtcAEmF": {"name": "revocationCheckFailed(output:options:)", "abstract": "<p>Revocation check failed with the associated <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError/ServerTrustFailureReason/Output.html\">Output</a></code> and options.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO24certificatePinningFailedyAESS_So03SecD3RefaSaySo0j11CertificateK0aGAKtcAEmF": {"name": "certificatePinningFailed(host:trust:pinnedCertificates:serverCertificates:)", "abstract": "<p>Certificate pinning failed.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO22publicKeyPinningFailedyAESS_So03SecD3RefaSaySo0khL0aGAKtcAEmF": {"name": "publicKeyPinningFailed(host:trust:pinnedKeys:serverKeys:)", "abstract": "<p>Public key pinning failed.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ServerTrustFailureReason.html#/s:9Alamofire7AFErrorO24ServerTrustFailureReasonO22customEvaluationFailedyAEs5Error_p_tcAEmF": {"name": "customEvaluationFailed(error:)", "abstract": "<p>Custom server trust evaluation failed due to the associated <code>Error</code>.</p>", "parent_name": "ServerTrustFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO24inputDataNilOrZeroLengthyA2EmF": {"name": "inputDataNilOrZeroLength", "abstract": "<p>The server response contained no data or the data was zero length.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO12inputFileNilyA2EmF": {"name": "inputFileNil", "abstract": "<p>The file containing the server response did not exist.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO19inputFileReadFailedyAE10Foundation3URLV_tcAEmF": {"name": "inputFileReadFailed(at:)", "abstract": "<p>The file containing the server response could not be read from the associated <code>URL</code>.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO06stringD6FailedyAESS10FoundationE8EncodingV_tcAEmF": {"name": "stringSerializationFailed(encoding:)", "abstract": "<p>String serialization failed using the provided <code>String.Encoding</code>.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO04jsonD6FailedyAEs5Error_p_tcAEmF": {"name": "jsonSerializationFailed(error:)", "abstract": "<p>JSON serialization failed with an underlying system error.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO14decodingFailedyAEs5Error_p_tcAEmF": {"name": "decodingFailed(error:)", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code> failed to decode the response due to the associated <code>Error</code>.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO06customD6FailedyAEs5Error_p_tcAEmF": {"name": "customSerializationFailed(error:)", "abstract": "<p>A custom response serializer failed due to the associated <code>Error</code>.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseSerializationFailureReason.html#/s:9Alamofire7AFErrorO34ResponseSerializationFailureReasonO012invalidEmptyC0yAESS_tcAEmF": {"name": "invalidEmptyResponse(type:)", "abstract": "<p>Generic serialization failed for an empty response that wasn&rsquo;t type <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/Empty.html\">Empty</a></code> but instead the associated type.</p>", "parent_name": "ResponseSerializationFailureReason"}, "Enums/AFError/ResponseValidationFailureReason.html#/s:9Alamofire7AFErrorO31ResponseValidationFailureReasonO11dataFileNilyA2EmF": {"name": "dataFileNil", "abstract": "<p>The data file containing the server response did not exist.</p>", "parent_name": "ResponseValidationFailureReason"}, "Enums/AFError/ResponseValidationFailureReason.html#/s:9Alamofire7AFErrorO31ResponseValidationFailureReasonO18dataFileReadFailedyAE10Foundation3URLV_tcAEmF": {"name": "dataFileReadFailed(at:)", "abstract": "<p>The data file containing the server response at the associated <code>URL</code> could not be read.</p>", "parent_name": "ResponseValidationFailureReason"}, "Enums/AFError/ResponseValidationFailureReason.html#/s:9Alamofire7AFErrorO31ResponseValidationFailureReasonO18missingContentTypeyAESaySSG_tcAEmF": {"name": "missingContentType(acceptableContentTypes:)", "abstract": "<p>The response did not contain a <code>Content-Type</code> and the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO22acceptableContentTypesSaySSGSgvp\">acceptableContentTypes</a></code> provided did not contain a", "parent_name": "ResponseValidationFailureReason"}, "Enums/AFError/ResponseValidationFailureReason.html#/s:9Alamofire7AFErrorO31ResponseValidationFailureReasonO23unacceptableContentTypeyAESaySSG_SStcAEmF": {"name": "unacceptableContentType(acceptableContentTypes:responseContentType:)", "abstract": "<p>The response <code>Content-Type</code> did not match any type in the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO22acceptableContentTypesSaySSGSgvp\">acceptableContentTypes</a></code>.</p>", "parent_name": "ResponseValidationFailureReason"}, "Enums/AFError/ResponseValidationFailureReason.html#/s:9Alamofire7AFErrorO31ResponseValidationFailureReasonO22unacceptableStatusCodeyAESi_tcAEmF": {"name": "unacceptableStatusCode(code:)", "abstract": "<p>The response status code was not acceptable.</p>", "parent_name": "ResponseValidationFailureReason"}, "Enums/AFError/ResponseValidationFailureReason.html#/s:9Alamofire7AFErrorO31ResponseValidationFailureReasonO06customD6FailedyAEs5Error_p_tcAEmF": {"name": "customValidationFailed(error:)", "abstract": "<p>Custom response validation failed due to the associated <code>Error</code>.</p>", "parent_name": "ResponseValidationFailureReason"}, "Enums/AFError/ParameterEncoderFailureReason/RequiredComponent.html#/s:9Alamofire7AFErrorO29ParameterEncoderFailureReasonO17RequiredComponentO3urlyA2GmF": {"name": "url", "abstract": "<p>The <code>URL</code> was missing or unable to be extracted from the passed <code>URLRequest</code> or during encoding.</p>", "parent_name": "RequiredComponent"}, "Enums/AFError/ParameterEncoderFailureReason/RequiredComponent.html#/s:9Alamofire7AFErrorO29ParameterEncoderFailureReasonO17RequiredComponentO10httpMethodyAGSS_tcAGmF": {"name": "httpMethod(rawValue:)", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPMethod.html\">HTTPMethod</a></code> could not be extracted from the passed <code>URLRequest</code>.</p>", "parent_name": "RequiredComponent"}, "Enums/AFError/ParameterEncoderFailureReason/RequiredComponent.html": {"name": "RequiredComponent", "abstract": "<p>Possible missing components.</p>", "parent_name": "ParameterEncoderFailureReason"}, "Enums/AFError/ParameterEncoderFailureReason.html#/s:9Alamofire7AFErrorO29ParameterEncoderFailureReasonO24missingRequiredComponentyA2E0hI0OcAEmF": {"name": "missingRequiredComponent(_:)", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError/ParameterEncoderFailureReason/RequiredComponent.html\">RequiredComponent</a></code> was missing during encoding.</p>", "parent_name": "ParameterEncoderFailureReason"}, "Enums/AFError/ParameterEncoderFailureReason.html#/s:9Alamofire7AFErrorO29ParameterEncoderFailureReasonO13encoderFailedyAEs5Error_p_tcAEmF": {"name": "encoderFailed(error:)", "abstract": "<p>The underlying encoder failed with the associated error.</p>", "parent_name": "ParameterEncoderFailureReason"}, "Enums/AFError/ParameterEncodingFailureReason.html#/s:9Alamofire7AFErrorO30ParameterEncodingFailureReasonO10missingURLyA2EmF": {"name": "missingURL", "abstract": "<p>The <code>URLRequest</code> did not have a <code>URL</code> to encode.</p>", "parent_name": "ParameterEncodingFailureReason"}, "Enums/AFError/ParameterEncodingFailureReason.html#/s:9Alamofire7AFErrorO30ParameterEncodingFailureReasonO04jsonD6FailedyAEs5Error_p_tcAEmF": {"name": "jsonEncodingFailed(error:)", "abstract": "<p>JSON serialization failed with an underlying system error during the encoding process.</p>", "parent_name": "ParameterEncodingFailureReason"}, "Enums/AFError/ParameterEncodingFailureReason.html#/s:9Alamofire7AFErrorO30ParameterEncodingFailureReasonO06customD6FailedyAEs5Error_p_tcAEmF": {"name": "customEncodingFailed(error:)", "abstract": "<p>Custom parameter encoding failed due to the associated <code>Error</code>.</p>", "parent_name": "ParameterEncodingFailureReason"}, "Enums/AFError/UnexpectedInputStreamLength.html#/s:9Alamofire7AFErrorO27UnexpectedInputStreamLengthV13bytesExpecteds6UInt64Vvp": {"name": "bytesExpected", "abstract": "<p>The expected byte count to read.</p>", "parent_name": "UnexpectedInputStreamLength"}, "Enums/AFError/UnexpectedInputStreamLength.html#/s:9Alamofire7AFErrorO27UnexpectedInputStreamLengthV9bytesReads6UInt64Vvp": {"name": "bytesRead", "abstract": "<p>The actual byte count read.</p>", "parent_name": "UnexpectedInputStreamLength"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO18bodyPartURLInvalidyAE10Foundation3URLV_tcAEmF": {"name": "bodyPartURLInvalid(url:)", "abstract": "<p>The <code>fileURL</code> provided for reading an encodable body part isn&rsquo;t a file <code>URL</code>.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO23bodyPartFilenameInvalidyAE10Foundation3URLV_tcAEmF": {"name": "bodyPartFilenameInvalid(in:)", "abstract": "<p>The filename of the <code>fileURL</code> provided has either an empty <code>lastPathComponent</code> or <code>pathExtension</code>.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO24bodyPartFileNotReachableyAE10Foundation3URLV_tcAEmF": {"name": "bodyPartFileNotReachable(at:)", "abstract": "<p>The file at the <code>fileURL</code> provided was not reachable.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO33bodyPartFileNotReachableWithErroryAE10Foundation3URLV_s0M0_ptcAEmF": {"name": "bodyPartFileNotReachableWithError(atURL:error:)", "abstract": "<p>Attempting to check the reachability of the <code>fileURL</code> provided threw an error.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO23bodyPartFileIsDirectoryyAE10Foundation3URLV_tcAEmF": {"name": "bodyPartFileIsDirectory(at:)", "abstract": "<p>The file at the <code>fileURL</code> provided is actually a directory.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO28bodyPartFileSizeNotAvailableyAE10Foundation3URLV_tcAEmF": {"name": "bodyPartFileSizeNotAvailable(at:)", "abstract": "<p>The size of the file at the <code>fileURL</code> provided was not returned by the system.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO36bodyPartFileSizeQueryFailedWithErroryAE10Foundation3URLV_s0N0_ptcAEmF": {"name": "bodyPartFileSizeQueryFailedWithError(forURL:error:)", "abstract": "<p>The attempt to find the size of the file at the <code>fileURL</code> provided threw an error.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO33bodyPartInputStreamCreationFailedyAE10Foundation3URLV_tcAEmF": {"name": "bodyPartInputStreamCreationFailed(for:)", "abstract": "<p>An <code>InputStream</code> could not be created for the provided <code>fileURL</code>.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO26outputStreamCreationFailedyAE10Foundation3URLV_tcAEmF": {"name": "outputStreamCreationFailed(for:)", "abstract": "<p>An <code>OutputStream</code> could not be created when attempting to write the encoded data to disk.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO29outputStreamFileAlreadyExistsyAE10Foundation3URLV_tcAEmF": {"name": "outputStreamFileAlreadyExists(at:)", "abstract": "<p>The encoded body data could not be written to disk because a file already exists at the provided <code>fileURL</code>.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO22outputStreamURLInvalidyAE10Foundation3URLV_tcAEmF": {"name": "outputStreamURLInvalid(url:)", "abstract": "<p>The <code>fileURL</code> provided for writing the encoded body data to disk is not a file <code>URL</code>.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO23outputStreamWriteFailedyAEs5Error_p_tcAEmF": {"name": "outputStreamWriteFailed(error:)", "abstract": "<p>The attempt to write the encoded body data to disk failed with an underlying error.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html#/s:9Alamofire7AFErrorO30MultipartEncodingFailureReasonO21inputStreamReadFailedyAEs5Error_p_tcAEmF": {"name": "inputStreamReadFailed(error:)", "abstract": "<p>The attempt to read an encoded body part <code>InputStream</code> failed with underlying system error.</p>", "parent_name": "MultipartEncodingFailureReason"}, "Enums/AFError/MultipartEncodingFailureReason.html": {"name": "MultipartEncodingFailureReason", "abstract": "<p>The underlying reason the <code>.multipartEncodingFailed</code> error occurred.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError/UnexpectedInputStreamLength.html": {"name": "UnexpectedInputStreamLength", "abstract": "<p>Represents unexpected input stream length that occur when encoding the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/MultipartFormData.html\">MultipartFormData</a></code>. Instances will be", "parent_name": "AFE<PERSON>r"}, "Enums/AFError/ParameterEncodingFailureReason.html": {"name": "ParameterEncodingFailureReason", "abstract": "<p>The underlying reason the <code>.parameterEncodingFailed</code> error occurred.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError/ParameterEncoderFailureReason.html": {"name": "ParameterEncoderFailureReason", "abstract": "<p>The underlying reason the <code>.parameterEncoderFailed</code> error occurred.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError/ResponseValidationFailureReason.html": {"name": "ResponseValidationFailureReason", "abstract": "<p>The underlying reason the <code>.responseValidationFailed</code> error occurred.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError/ResponseSerializationFailureReason.html": {"name": "ResponseSerializationFailureReason", "abstract": "<p>The underlying reason the response serialization error occurred.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError/ServerTrustFailureReason.html": {"name": "ServerTrustFailureReason", "abstract": "<p>Underlying reason a server trust evaluation error occurred.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError/URLRequestValidationFailureReason.html": {"name": "URLRequestValidationFailureReason", "abstract": "<p>The underlying reason the <code>.urlRequestValidationFailed</code> error occurred.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO22createUploadableFailedyACs5Error_p_tcACmF": {"name": "createUploadableFailed(error:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/UploadableConvertible.html\">UploadableConvertible</a></code> threw an error in <code>createUploadable()</code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO22createURLRequestFailedyACs5Error_p_tcACmF": {"name": "createURLRequestFailed(error:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> threw an error in <code>asURLRequest()</code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO24downloadedFileMoveFailedyACs5Error_p_10Foundation3URLVAHtcACmF": {"name": "downloadedFileMoveFailed(error:source:destination:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/SessionDelegate.html\">SessionDelegate</a></code> threw an error while attempting to move downloaded file to destination URL.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO19explicitlyCancelledyA2CmF": {"name": "explicitlyCancelled", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> was explicitly cancelled.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO10invalidURLyAcA14URLConvertible_p_tcACmF": {"name": "invalidURL(url:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLConvertible.html\">URLConvertible</a></code> type failed to create a valid <code>URL</code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO23multipartEncodingFailedyA2C09MultipartD13FailureReasonO_tcACmF": {"name": "multipartEncodingFailed(reason:)", "abstract": "<p>Multipart form encoding failed.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO23parameterEncodingFailedyA2C09ParameterD13FailureReasonO_tcACmF": {"name": "parameterEncodingFailed(reason:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ParameterEncoding.html\">ParameterEncoding</a></code> threw an error during the encoding process.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO22parameterEncoderFailedyA2C09ParameterD13FailureReasonO_tcACmF": {"name": "parameterEncoderFailed(reason:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ParameterEncoder.html\">ParameterEncoder</a></code> threw an error while running the encoder.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO23requestAdaptationFailedyACs5Error_p_tcACmF": {"name": "requestAdaptationFailed(error:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> threw an error during adaptation.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO18requestRetryFailedyACs5Error_p_sAE_ptcACmF": {"name": "requestRetryFailed(retryError:originalError:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code> threw an error during the request retry process.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO24responseValidationFailedyA2C08ResponseD13FailureReasonO_tcACmF": {"name": "responseValidationFailed(reason:)", "abstract": "<p>Response validation failed.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO27responseSerializationFailedyA2C08ResponseD13FailureReasonO_tcACmF": {"name": "responseSerializationFailed(reason:)", "abstract": "<p>Response serialization failed.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO27serverTrustEvaluationFailedyA2C06ServerD13FailureReasonO_tcACmF": {"name": "serverTrustEvaluationFailed(reason:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ServerTrustEvaluating.html\">ServerTrustEvaluating</a></code> instance threw an error during trust evaluation.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO20sessionDeinitializedyA2CmF": {"name": "sessionDeinitialized", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Session.html\">Session</a></code> which issued the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> was deinitialized, most likely because its reference went out of scope.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO18sessionInvalidatedyACs5Error_pSg_tcACmF": {"name": "sessionInvalidated(error:)", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Session.html\">Session</a></code> was explicitly invalidated, possibly with the <code>Error</code> produced by the underlying <code>URLSession</code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO17sessionTaskFailedyACs5Error_p_tcACmF": {"name": "sessionTaskFailed(error:)", "abstract": "<p><code>URLSessionTask</code> completed with error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO26urlRequestValidationFailedyA2C010URLRequestE13FailureReasonO_tcACmF": {"name": "urlRequestValidationFailed(reason:)", "abstract": "<p><code>URLRequest</code> failed validation.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO27isSessionDeinitializedErrorSbvp": {"name": "isSessionDeinitializedError", "abstract": "<p>Returns whether the instance is <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO20sessionDeinitializedyA2CmF\">.sessionDeinitialized</a></code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO25isSessionInvalidatedErrorSbvp": {"name": "isSessionInvalidatedError", "abstract": "<p>Returns whether the instance is <code>.sessionInvalidated</code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO26isExplicitlyCancelledErrorSbvp": {"name": "isExplicitlyCancelledError", "abstract": "<p>Returns whether the instance is <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO19explicitlyCancelledyA2CmF\">.explicitlyCancelled</a></code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO17isInvalidURLErrorSbvp": {"name": "isInvalidURLError", "abstract": "<p>Returns whether the instance is <code>.invalidURL</code>.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO24isParameterEncodingErrorSbvp": {"name": "isParameterEncodingError", "abstract": "<p>Returns whether the instance is <code>.parameterEncodingFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO23isParameterEncoderErrorSbvp": {"name": "isParameterEncoderError", "abstract": "<p>Returns whether the instance is <code>.parameterEncoderFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO24isMultipartEncodingErrorSbvp": {"name": "isMultipartEncodingError", "abstract": "<p>Returns whether the instance is <code>.multipartEncodingFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO3url10Foundation3URLVSgvp\">url</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO24isRequestAdaptationErrorSbvp": {"name": "isRequestAdaptationError", "abstract": "<p>Returns whether the instance is <code>.requestAdaptationFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO25isResponseValidationErrorSbvp": {"name": "isResponseValidationError", "abstract": "<p>Returns whether the instance is <code>.responseValidationFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO22acceptableContentTypesSaySSGSgvp\">acceptableContentTypes</a></code>,", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO28isResponseSerializationErrorSbvp": {"name": "isResponseSerializationError", "abstract": "<p>Returns whether the instance is <code>.responseSerializationFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO20failedStringEncodingSS10FoundationE0E0VSgvp\">failedStringEncoding</a></code> and", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO28isServerTrustEvaluationErrorSbvp": {"name": "isServerTrustEvaluationError", "abstract": "<p>Returns whether the instance is <code>.serverTrustEvaluationFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO19isRequestRetryErrorSbvp": {"name": "isRequestRetryError", "abstract": "<p>Returns whether the instance is <code>requestRetryFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO23isCreateUploadableErrorSbvp": {"name": "isCreateUploadableError", "abstract": "<p>Returns whether the instance is <code>createUploadableFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO23isCreateURLRequestErrorSbvp": {"name": "isCreateURLRequestError", "abstract": "<p>Returns whether the instance is <code>createURLRequestFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO25isDownloadedFileMoveErrorSbvp": {"name": "isDownloadedFileMoveError", "abstract": "<p>Returns whether the instance is <code>downloadedFileMoveFailed</code>. When <code>true</code>, the <code>destination</code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> properties will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO18isSessionTaskErrorSbvp": {"name": "isSessionTaskError", "abstract": "<p>Returns whether the instance is <code>createURLRequestFailed</code>. When <code>true</code>, the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp\">underlyingError</a></code> property will", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO14urlConvertibleAA14URLConvertible_pSgvp": {"name": "urlConvertible", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLConvertible.html\">URLConvertible</a></code> associated with the error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO3url10Foundation3URLVSgvp": {"name": "url", "abstract": "<p>The <code>URL</code> associated with the error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO15underlyingErrors0D0_pSgvp": {"name": "underlyingError", "abstract": "<p>The underlying <code>Error</code> responsible for generating the failure associated with <code>.sessionInvalidated</code>,", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO22acceptableContentTypesSaySSGSgvp": {"name": "acceptableContentTypes", "abstract": "<p>The acceptable <code>Content-Type</code>s of a <code>.responseValidationFailed</code> error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO19responseContentTypeSSSgvp": {"name": "responseContentType", "abstract": "<p>The response <code>Content-Type</code> of a <code>.responseValidationFailed</code> error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO12responseCodeSiSgvp": {"name": "responseCode", "abstract": "<p>The response code of a <code>.responseValidationFailed</code> error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO20failedStringEncodingSS10FoundationE0E0VSgvp": {"name": "failedStringEncoding", "abstract": "<p>The <code>String.Encoding</code> associated with a failed <code>.stringResponse()</code> call.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO9sourceURL10Foundation0D0VSgvp": {"name": "sourceURL", "abstract": "<p>The <code>source</code> URL of a <code>.downloadedFileMoveFailed</code> error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO14destinationURL10Foundation0D0VSgvp": {"name": "destinationURL", "abstract": "<p>The <code>destination</code> URL of a <code>.downloadedFileMoveFailed</code> error.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:9Alamofire7AFErrorO18downloadResumeData10Foundation0E0VSgvp": {"name": "downloadResumeData", "abstract": "<p>The download resume data of any underlying network error. Only produced by <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code>s.</p>", "parent_name": "AFE<PERSON>r"}, "Enums/AFError.html#/s:10Foundation14LocalizedErrorP16errorDescriptionSSSgvp": {"name": "errorDescription", "parent_name": "AFE<PERSON>r"}, "Enums/AFInfo.html#/s:9Alamofire6AFInfoO7versionSSvpZ": {"name": "version", "abstract": "<p>Current Alamofire version.</p>", "parent_name": "AFInfo"}, "Enums/AFInfo.html": {"name": "AFInfo", "abstract": "<p>Namespace for informational Alamofire values.</p>"}, "Enums/AFError.html": {"name": "AFE<PERSON>r", "abstract": "<p><code>AFError</code> is the error type returned by Alamofire. It encompasses a few different types of errors, each with"}, "Enums/AuthenticationError.html": {"name": "AuthenticationError", "abstract": "<p>Represents various authentication failures that occur when using the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/AuthenticationInterceptor.html\">AuthenticationInterceptor</a></code>. All errors are"}, "Enums/RetryResult.html": {"name": "RetryResult", "abstract": "<p>Outcome of determination whether retry is necessary.</p>"}, "Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp": {"name": "AF", "abstract": "<p>Reference to <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Session.html#/s:9Alamofire7SessionC7defaultACvpZ\">Session.default</a></code> for quick bootstrapping and examples.</p>"}, "Classes/URLEncodedFormEncoder/Error.html#/s:9Alamofire21URLEncodedFormEncoderC5ErrorO17invalidRootObjectyAESScAEmF": {"name": "invalidRootObject(_:)", "abstract": "<p>An invalid root object was created by the encoder. Only keyed values are valid.</p>", "parent_name": "Error"}, "Classes/URLEncodedFormEncoder/SpaceEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC13SpaceEncodingO14percentEscapedyA2EmF": {"name": "percentEscaped", "abstract": "<p>Encodes spaces using percent escaping (<code>%20</code>).</p>", "parent_name": "SpaceEncoding"}, "Classes/URLEncodedFormEncoder/SpaceEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC13SpaceEncodingO12plusReplacedyA2EmF": {"name": "plusReplaced", "abstract": "<p>Encodes spaces as <code>+</code>.</p>", "parent_name": "SpaceEncoding"}, "Classes/URLEncodedFormEncoder/NilEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11NilEncodingV7dropKeyAEvpZ": {"name": "<PERSON><PERSON><PERSON>", "abstract": "<p>Encodes <code>nil</code> by dropping the entire key / value pair.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Classes/URLEncodedFormEncoder/NilEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11NilEncodingV9dropValueAEvpZ": {"name": "dropValue", "abstract": "<p>Encodes <code>nil</code> by dropping only the value. e.g. <code>value1=one&amp;nilValue=&amp;value2=two</code>.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Classes/URLEncodedFormEncoder/NilEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11NilEncodingV4nullAEvpZ": {"name": "null", "abstract": "<p>Encodes <code>nil</code> as <code>null</code>.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Classes/URLEncodedFormEncoder/NilEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11NilEncodingV8encodingAESSSgyYbc_tcfc": {"name": "init(encoding:)", "abstract": "<p>Creates an instance with the encoding closure called for <code>nil</code> values.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Classes/URLEncodedFormEncoder/KeyPathEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC15KeyPathEncodingV8bracketsAEvpZ": {"name": "brackets", "abstract": "<p>Encodes key paths by wrapping each component in brackets. e.g. <code>parent[child][grandchild]</code>.</p>", "parent_name": "KeyPathEncoding"}, "Classes/URLEncodedFormEncoder/KeyPathEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC15KeyPathEncodingV4dotsAEvpZ": {"name": "dots", "abstract": "<p>Encodes key paths by separating each component with dots. e.g. <code>parent.child.grandchild</code>.</p>", "parent_name": "KeyPathEncoding"}, "Classes/URLEncodedFormEncoder/KeyPathEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC15KeyPathEncodingV8encodingAES2SYbc_tcfc": {"name": "init(encoding:)", "abstract": "<p>Creates an instance with the encoding closure called for each sub-key in a key path.</p>", "parent_name": "KeyPathEncoding"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO14useDefaultKeysyA2EmF": {"name": "useDefaultKeys", "abstract": "<p>Use the keys specified by each type. This is the default encoding.</p>", "parent_name": "KeyEncoding"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO18convertToSnakeCaseyA2EmF": {"name": "convertToSnakeCase", "abstract": "<p>Convert from &ldquo;camelCaseKeys&rdquo; to &ldquo;snake_case_keys&rdquo; before writing a key.</p>", "parent_name": "KeyEncoding"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO18convertToKebabCaseyA2EmF": {"name": "convertToKebabCase", "abstract": "<p>Same as convertToSnakeCase, but using <code>-</code> instead of <code>_</code>.", "parent_name": "KeyEncoding"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO11capitalizedyA2EmF": {"name": "capitalized", "abstract": "<p>Capitalize the first letter only.", "parent_name": "KeyEncoding"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO10uppercasedyA2EmF": {"name": "uppercased", "abstract": "<p>Uppercase all letters.", "parent_name": "KeyEncoding"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO10lowercasedyA2EmF": {"name": "lowercased", "abstract": "<p>Lowercase all letters.", "parent_name": "KeyEncoding"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO6customyAES2SccAEmF": {"name": "custom(_:)", "abstract": "<p>A custom encoding using the provided closure.</p>", "parent_name": "KeyEncoding"}, "Classes/URLEncodedFormEncoder/DateEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO010deferredToE0yA2EmF": {"name": "deferredToDate", "abstract": "<p>Defers encoding to the <code>Date</code> type. This is the default encoding.</p>", "parent_name": "DateEncoding"}, "Classes/URLEncodedFormEncoder/DateEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO16secondsSince1970yA2EmF": {"name": "secondsSince1970", "abstract": "<p>Encodes <code>Date</code>s as seconds since midnight UTC on January 1, 1970.</p>", "parent_name": "DateEncoding"}, "Classes/URLEncodedFormEncoder/DateEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO21millisecondsSince1970yA2EmF": {"name": "millisecondsSince1970", "abstract": "<p>Encodes <code>Date</code>s as milliseconds since midnight UTC on January 1, 1970.</p>", "parent_name": "DateEncoding"}, "Classes/URLEncodedFormEncoder/DateEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO7iso8601yA2EmF": {"name": "iso8601", "abstract": "<p>Encodes <code>Date</code>s according to the ISO8601 and RFC3339 standards.</p>", "parent_name": "DateEncoding"}, "Classes/URLEncodedFormEncoder/DateEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO9formattedyAESo15NSDateFormatterCcAEmF": {"name": "formatted(_:)", "abstract": "<p>Encodes <code>Date</code>s using the given <code>DateFormatter</code>.</p>", "parent_name": "DateEncoding"}, "Classes/URLEncodedFormEncoder/DateEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO6customyAESS10Foundation0E0VKccAEmF": {"name": "custom(_:)", "abstract": "<p>Encodes <code>Date</code>s using the given closure.</p>", "parent_name": "DateEncoding"}, "Classes/URLEncodedFormEncoder/DataEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DataEncodingO010deferredToE0yA2EmF": {"name": "deferredToData", "abstract": "<p>Defers encoding to the <code>Data</code> type.</p>", "parent_name": "DataEncoding"}, "Classes/URLEncodedFormEncoder/DataEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DataEncodingO6base64yA2EmF": {"name": "base64", "abstract": "<p>Encodes <code>Data</code> as a Base64-encoded string. This is the default encoding.</p>", "parent_name": "DataEncoding"}, "Classes/URLEncodedFormEncoder/DataEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12DataEncodingO6customyAESS10Foundation0E0VKccAEmF": {"name": "custom(_:)", "abstract": "<p>Encode the <code>Data</code> as a custom value encoded by the given closure.</p>", "parent_name": "DataEncoding"}, "Classes/URLEncodedFormEncoder/BoolEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12BoolEncodingO7numericyA2EmF": {"name": "numeric", "abstract": "<p>Encodes <code>true</code> as <code>1</code>, <code>false</code> as <code>0</code>.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Classes/URLEncodedFormEncoder/BoolEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC12BoolEncodingO7literalyA2EmF": {"name": "literal", "abstract": "<p>Encodes <code>true</code> as &ldquo;true&rdquo;, <code>false</code> as &ldquo;false&rdquo;. This is the default encoding.</p>", "parent_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "Classes/URLEncodedFormEncoder/ArrayEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC13ArrayEncodingO8bracketsyA2EmF": {"name": "brackets", "abstract": "<p>An empty set of square brackets (&ldquo;[]&rdquo;) are appended to the key for every value. This is the default encoding.</p>", "parent_name": "ArrayEncoding"}, "Classes/URLEncodedFormEncoder/ArrayEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC13ArrayEncodingO10noBracketsyA2EmF": {"name": "noBrackets", "abstract": "<p>No brackets are appended to the key and the key is encoded as is.</p>", "parent_name": "ArrayEncoding"}, "Classes/URLEncodedFormEncoder/ArrayEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC13ArrayEncodingO15indexInBracketsyA2EmF": {"name": "indexInBrackets", "abstract": "<p>Brackets containing the item index are appended. This matches the jQuery and Node.js behavior.</p>", "parent_name": "ArrayEncoding"}, "Classes/URLEncodedFormEncoder/ArrayEncoding.html#/s:9Alamofire21URLEncodedFormEncoderC13ArrayEncodingO6customyAES2S_SitccAEmF": {"name": "custom(_:)", "abstract": "<p>Provide a custom array key encoding with the given closure.</p>", "parent_name": "ArrayEncoding"}, "Classes/URLEncodedFormEncoder/ArrayEncoding.html": {"name": "ArrayEncoding", "abstract": "<p>Encoding to use for <code>Array</code> values.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/BoolEncoding.html": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": "<p>Encoding to use for <code>Bool</code> values.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/DataEncoding.html": {"name": "DataEncoding", "abstract": "<p>Encoding to use for <code>Data</code> values.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/DateEncoding.html": {"name": "DateEncoding", "abstract": "<p>Encoding to use for <code>Date</code> values.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/KeyEncoding.html": {"name": "KeyEncoding", "abstract": "<p>Encoding to use for keys.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/KeyPathEncoding.html": {"name": "KeyPathEncoding", "abstract": "<p>Encoding to use for nested object and <code>Encodable</code> value key paths.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/NilEncoding.html": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "abstract": "<p>Encoding to use for <code>nil</code> values.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/SpaceEncoding.html": {"name": "SpaceEncoding", "abstract": "<p>Encoding to use for spaces.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder/Error.html": {"name": "Error", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder.html\">URLEncodedFormEncoder</a></code> error.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC24alphabetizeKeyValuePairsSbvp": {"name": "alphabetizeKeyValuePairs", "abstract": "<p>Whether or not to sort the encoded key value pairs.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC13arrayEncodingAC05ArrayF0Ovp": {"name": "arrayEncoding", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/ArrayEncoding.html\">ArrayEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC12boolEncodingAC04BoolF0Ovp": {"name": "boolEncoding", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/BoolEncoding.html\">BoolEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC12dataEncodingAC04DataF0Ovp": {"name": "dataEncoding", "abstract": "<p>THe <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/DataEncoding.html\">DataEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC12dateEncodingAC04DateF0Ovp": {"name": "dateEncoding", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/DateEncoding.html\">DateEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC11keyEncodingAC03KeyF0Ovp": {"name": "keyEncoding", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/KeyEncoding.html\">KeyEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC15keyPathEncodingAC03KeyfG0Vvp": {"name": "keyPathEncoding", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/KeyPathEncoding.html\">KeyPathEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC11nilEncodingAC03NilF0Vvp": {"name": "nil<PERSON><PERSON><PERSON>", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/NilEncoding.html\">NilEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC13spaceEncodingAC05SpaceF0Ovp": {"name": "spaceEncoding", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder/SpaceEncoding.html\">SpaceEncoding</a></code> to use.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC17allowedCharacters10Foundation12CharacterSetVvp": {"name": "allowedCharacters", "abstract": "<p>The <code>CharacterSet</code> of allowed (non-escaped) characters.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC24alphabetizeKeyValuePairs13arrayEncoding04boolJ004dataJ004dateJ003keyJ00n4PathJ003nilJ005spaceJ017allowedCharactersACSb_AC05ArrayJ0OAC04BoolJ0OAC04DataJ0OAC04DateJ0OAC0fJ0OAC0foJ0VAC03NilJ0VAC05SpaceJ0O10Foundation12CharacterSetVtcfc": {"name": "init(alphabetizeKeyValuePairs:arrayEncoding:boolEncoding:dataEncoding:dateEncoding:keyEncoding:keyPathEncoding:nilEncoding:spaceEncoding:allowedCharacters:)", "abstract": "<p>Creates an instance from the supplied parameters.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC6encodeySSSE_pKF": {"name": "encode(_:)", "abstract": "<p>Encodes the <code>value</code> as a URL form encoded <code>String</code>.</p>", "parent_name": "URLEncodedFormEncoder"}, "Classes/URLEncodedFormEncoder.html#/s:9Alamofire21URLEncodedFormEncoderC6encodey10Foundation4DataVSE_pKF": {"name": "encode(_:)", "abstract": "<p>Encodes the value as <code>Data</code>. This is performed by first creating an encoded <code>String</code> and then returning the", "parent_name": "URLEncodedFormEncoder"}, "Classes/DisabledTrustEvaluator.html#/s:9Alamofire22DisabledTrustEvaluatorCACycfc": {"name": "init()", "abstract": "<p>Creates an instance.</p>", "parent_name": "DisabledTrustEvaluator"}, "Classes/DisabledTrustEvaluator.html#/s:9Alamofire21ServerTrustEvaluatingP8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "parent_name": "DisabledTrustEvaluator"}, "Classes/CompositeTrustEvaluator.html#/s:9Alamofire23CompositeTrustEvaluatorC10evaluatorsACSayAA06ServerC10Evaluating_pG_tcfc": {"name": "init(evaluators:)", "abstract": "<p>Creates a <code>CompositeTrustEvaluator</code> from the provided evaluators.</p>", "parent_name": "CompositeTrustEvaluator"}, "Classes/CompositeTrustEvaluator.html#/s:9Alamofire21ServerTrustEvaluatingP8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "parent_name": "CompositeTrustEvaluator"}, "Classes/PublicKeysTrustEvaluator.html#/s:9Alamofire24PublicKeysTrustEvaluatorC4keys24performDefaultValidation12validateHostACSaySo9SecKeyRefaG_S2btcfc": {"name": "init(keys:performDefaultValidation:validateHost:)", "abstract": "<p>Creates a <code>PublicKeysTrustEvaluator</code> from the provided parameters.</p>", "parent_name": "PublicKeysTrustEvaluator"}, "Classes/PublicKeysTrustEvaluator.html#/s:9Alamofire21ServerTrustEvaluatingP8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "parent_name": "PublicKeysTrustEvaluator"}, "Classes/PinnedCertificatesTrustEvaluator.html#/s:9Alamofire32PinnedCertificatesTrustEvaluatorC12certificates016acceptSelfSignedC024performDefaultValidation12validateHostACSaySo17SecCertificateRefaG_S3btcfc": {"name": "init(certificates:acceptSelfSignedCertificates:performDefaultValidation:validateHost:)", "abstract": "<p>Creates a <code>PinnedCertificatesTrustEvaluator</code> from the provided parameters.</p>", "parent_name": "PinnedCertificatesTrustEvaluator"}, "Classes/PinnedCertificatesTrustEvaluator.html#/s:9Alamofire21ServerTrustEvaluatingP8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "parent_name": "PinnedCertificatesTrustEvaluator"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV3crlAEvpZ": {"name": "crl", "abstract": "<p>Perform revocation checking using the CRL (Certification Revocation List) method.</p>", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV21networkAccessDisabledAEvpZ": {"name": "networkAccessDisabled", "abstract": "<p>Consult only locally cached replies; do not use network access.</p>", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV4ocspAEvpZ": {"name": "ocsp", "abstract": "<p>Perform revocation checking using OCSP (Online Certificate Status Protocol).</p>", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV9preferCRLAEvpZ": {"name": "preferCRL", "abstract": "<p>Prefer CRL revocation checking over OCSP; by default, OCSP is preferred.</p>", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV23requirePositiveResponseAEvpZ": {"name": "requirePositiveResponse", "abstract": "<p>Require a positive response to pass the policy. If the flag is not set, revocation checking is done on a", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV3anyAEvpZ": {"name": "any", "abstract": "<p>Perform either OCSP or CRL checking. The checking is performed according to the method(s) specified in the", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV8rawValueSuvp": {"name": "rawValue", "abstract": "<p>The raw value of the option.</p>", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html#/s:9Alamofire24RevocationTrustEvaluatorC7OptionsV8rawValueAESu_tcfc": {"name": "init(rawValue:)", "abstract": "<p>Creates an <code>Options</code> value with the given <code>CFOptionFlags</code>.</p>", "parent_name": "Options"}, "Classes/RevocationTrustEvaluator/Options.html": {"name": "Options", "abstract": "<p>Represents the options to be use when evaluating the status of a certificate.", "parent_name": "RevocationTrustEvaluator"}, "Classes/RevocationTrustEvaluator.html#/s:9Alamofire24RevocationTrustEvaluatorC24performDefaultValidation12validateHost7optionsACSb_SbAC7OptionsVtcfc": {"name": "init(performDefaultValidation:validateHost:options:)", "abstract": "<p>Creates a <code>RevocationTrustEvaluator</code> using the provided parameters.</p>", "parent_name": "RevocationTrustEvaluator"}, "Classes/RevocationTrustEvaluator.html#/s:9Alamofire21ServerTrustEvaluatingP8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "parent_name": "RevocationTrustEvaluator"}, "Classes/DefaultTrustEvaluator.html#/s:9Alamofire21DefaultTrustEvaluatorC12validateHostACSb_tcfc": {"name": "init(validateHost:)", "abstract": "<p>Creates a <code>DefaultTrustEvaluator</code>.</p>", "parent_name": "DefaultTrustEvaluator"}, "Classes/DefaultTrustEvaluator.html#/s:9Alamofire21ServerTrustEvaluatingP8evaluate_7forHostySo03SecC3Refa_SStKF": {"name": "evaluate(_:forHost:)", "parent_name": "DefaultTrustEvaluator"}, "Classes/ServerTrustManager.html#/s:9Alamofire18ServerTrustManagerC23allHostsMustBeEvaluatedSbvp": {"name": "allHostsMustBeEvaluated", "abstract": "<p>Determines whether all hosts for this <code>ServerTrustManager</code> must be evaluated. <code>true</code> by default.</p>", "parent_name": "ServerTrustManager"}, "Classes/ServerTrustManager.html#/s:9Alamofire18ServerTrustManagerC10evaluatorsSDySSAA0bC10Evaluating_pGvp": {"name": "evaluators", "abstract": "<p>The dictionary of policies mapped to a particular host.</p>", "parent_name": "ServerTrustManager"}, "Classes/ServerTrustManager.html#/s:9Alamofire18ServerTrustManagerC23allHostsMustBeEvaluated10evaluatorsACSb_SDySSAA0bC10Evaluating_pGtcfc": {"name": "init(allHostsMustBeEvaluated:evaluators:)", "abstract": "<p>Initializes the <code>ServerTrustManager</code> instance with the given evaluators.</p>", "parent_name": "ServerTrustManager"}, "Classes/ServerTrustManager.html#/s:9Alamofire18ServerTrustManagerC06serverC9Evaluator7forHostAA0bC10Evaluating_pSgSS_tKF": {"name": "serverTrustEvaluator(forHost:)", "abstract": "<p>Returns the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ServerTrustEvaluating.html\">ServerTrustEvaluating</a></code> value for the given host, if one is set.</p>", "parent_name": "ServerTrustManager"}, "Classes/ConnectionLostRetryPolicy.html#/s:9Alamofire25ConnectionLostRetryPolicyC10retryLimit22exponentialBackoffBase0hI5Scale20retryableHTTPMethodsACSu_SuSdShyAA10HTTPMethodVGtcfc": {"name": "init(retryLimit:exponentialBackoffBase:exponentialBackoffScale:retryableHTTPMethods:)", "abstract": "<p>Creates a <code>ConnectionLostRetryPolicy</code> instance from the specified parameters.</p>", "parent_name": "ConnectionLostRetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC07defaultB5LimitSuvpZ": {"name": "defaultRetryLimit", "abstract": "<p>The default retry limit for retry policies.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC29defaultExponentialBackoffBaseSuvpZ": {"name": "defaultExponentialBackoffBase", "abstract": "<p>The default exponential backoff base for retry policies (must be a minimum of 2).</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC30defaultExponentialBackoffScaleSdvpZ": {"name": "defaultExponentialBackoffScale", "abstract": "<p>The default exponential backoff scale for retry policies.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC27defaultRetryableHTTPMethodsShyAA10HTTPMethodVGvpZ": {"name": "defaultRetryableHTTPMethods", "abstract": "<p>The default HTTP methods to retry.", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC31defaultRetryableHTTPStatusCodesShySiGvpZ": {"name": "defaultRetryableHTTPStatusCodes", "abstract": "<p>The default HTTP status codes to retry.", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC29defaultRetryableURLErrorCodesShy10Foundation0F0V4CodeVGvpZ": {"name": "defaultRetryableURLErrorCodes", "abstract": "<p>The default URL error codes to retry.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC10retryLimitSuvp": {"name": "retryLimit", "abstract": "<p>The total number of times the request is allowed to be retried.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC22exponentialBackoffBaseSuvp": {"name": "exponentialBackoffBase", "abstract": "<p>The base of the exponential backoff policy (should always be greater than or equal to 2).</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC23exponentialBackoffScaleSdvp": {"name": "exponentialBackoffScale", "abstract": "<p>The scale of the exponential backoff.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC20retryableHTTPMethodsShyAA10HTTPMethodVGvp": {"name": "retryableHTTPMethods", "abstract": "<p>The HTTP methods that are allowed to be retried.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC24retryableHTTPStatusCodesShySiGvp": {"name": "retryableHTTPStatusCodes", "abstract": "<p>The HTTP status codes that are automatically retried by the policy.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC22retryableURLErrorCodesShy10Foundation0E0V4CodeVGvp": {"name": "retryableURLErrorCodes", "abstract": "<p>The URL error codes that are automatically retried by the policy.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC10retryLimit22exponentialBackoffBase0fG5Scale20retryableHTTPMethods0J15HTTPStatusCodes0j8URLErrorM0ACSu_SuSdShyAA10HTTPMethodVGShySiGShy10Foundation0N0V4CodeVGtcfc": {"name": "init(retryLimit:exponentialBackoffBase:exponentialBackoffScale:retryableHTTPMethods:retryableHTTPStatusCodes:retryableURLErrorCodes:)", "abstract": "<p>Creates a <code>RetryPolicy</code> from the specified parameters.</p>", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF": {"name": "retry(_:for:dueTo:completion:)", "parent_name": "RetryPolicy"}, "Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC06shouldB07request5dueToSbAA7RequestC_s5Error_ptF": {"name": "shouldRetry(request:dueTo:)", "abstract": "<p>Determines whether or not to retry the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>.</p>", "parent_name": "RetryPolicy"}, "Classes/DecodableResponseSerializer.html#/s:9Alamofire18ResponseSerializerP16dataPreprocessorAA04DataE0_pvp": {"name": "dataPreprocessor", "parent_name": "DecodableResponseSerializer"}, "Classes/DecodableResponseSerializer.html#/s:9Alamofire27DecodableResponseSerializerC7decoderAA11DataDecoder_pvp": {"name": "decoder", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code> instance used to decode responses.</p>", "parent_name": "DecodableResponseSerializer"}, "Classes/DecodableResponseSerializer.html#/s:9Alamofire18ResponseSerializerP05emptyB5CodesShySiGvp": {"name": "emptyResponseCodes", "parent_name": "DecodableResponseSerializer"}, "Classes/DecodableResponseSerializer.html#/s:9Alamofire18ResponseSerializerP19emptyRequestMethodsShyAA10HTTPMethodVGvp": {"name": "emptyRequestMethods", "parent_name": "DecodableResponseSerializer"}, "Classes/DecodableResponseSerializer.html#/s:9Alamofire27DecodableResponseSerializerC16dataPreprocessor7decoder05emptyC5Codes0H14RequestMethodsACyxGAA04DataF0_p_AA0L7Decoder_pShySiGShyAA10HTTPMethodVGtcfc": {"name": "init(dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates an instance using the values provided.</p>", "parent_name": "DecodableResponseSerializer"}, "Classes/DecodableResponseSerializer.html#/s:9Alamofire30DataResponseSerializerProtocolP9serialize7request8response4data5error16SerializedObjectQz10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0B0VSgs5Error_pSgtKF": {"name": "serialize(request:response:data:error:)", "parent_name": "DecodableResponseSerializer"}, "Classes/JSONResponseSerializer.html#/s:9Alamofire18ResponseSerializerP16dataPreprocessorAA04DataE0_pvp": {"name": "dataPreprocessor", "parent_name": "JSONResponseSerializer"}, "Classes/JSONResponseSerializer.html#/s:9Alamofire18ResponseSerializerP05emptyB5CodesShySiGvp": {"name": "emptyResponseCodes", "parent_name": "JSONResponseSerializer"}, "Classes/JSONResponseSerializer.html#/s:9Alamofire18ResponseSerializerP19emptyRequestMethodsShyAA10HTTPMethodVGvp": {"name": "emptyRequestMethods", "parent_name": "JSONResponseSerializer"}, "Classes/JSONResponseSerializer.html#/s:9Alamofire22JSONResponseSerializerC7optionsSo20NSJSONReadingOptionsVvp": {"name": "options", "abstract": "<p><code>JSONSerialization.ReadingOptions</code> used when serializing a response.</p>", "parent_name": "JSONResponseSerializer"}, "Classes/JSONResponseSerializer.html#/s:9Alamofire22JSONResponseSerializerC16dataPreprocessor18emptyResponseCodes0F14RequestMethods7optionsAcA04DataE0_p_ShySiGShyAA10HTTPMethodVGSo20NSJSONReadingOptionsVtcfc": {"name": "init(dataPreprocessor:emptyResponseCodes:emptyRequestMethods:options:)", "abstract": "<p>Creates an instance with the provided values.</p>", "parent_name": "JSONResponseSerializer"}, "Classes/JSONResponseSerializer.html#/s:9Alamofire30DataResponseSerializerProtocolP9serialize7request8response4data5error16SerializedObjectQz10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0B0VSgs5Error_pSgtKF": {"name": "serialize(request:response:data:error:)", "parent_name": "JSONResponseSerializer"}, "Classes/StringResponseSerializer.html#/s:9Alamofire18ResponseSerializerP16dataPreprocessorAA04DataE0_pvp": {"name": "dataPreprocessor", "parent_name": "StringResponseSerializer"}, "Classes/StringResponseSerializer.html#/s:9Alamofire24StringResponseSerializerC8encodingSS10FoundationE8EncodingVSgvp": {"name": "encoding", "abstract": "<p>Optional string encoding used to validate the response.</p>", "parent_name": "StringResponseSerializer"}, "Classes/StringResponseSerializer.html#/s:9Alamofire18ResponseSerializerP05emptyB5CodesShySiGvp": {"name": "emptyResponseCodes", "parent_name": "StringResponseSerializer"}, "Classes/StringResponseSerializer.html#/s:9Alamofire18ResponseSerializerP19emptyRequestMethodsShyAA10HTTPMethodVGvp": {"name": "emptyRequestMethods", "parent_name": "StringResponseSerializer"}, "Classes/StringResponseSerializer.html#/s:9Alamofire24StringResponseSerializerC16dataPreprocessor8encoding05emptyC5Codes0H14RequestMethodsAcA04DataF0_p_SS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtcfc": {"name": "init(dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates an instance with the provided values.</p>", "parent_name": "StringResponseSerializer"}, "Classes/StringResponseSerializer.html#/s:9Alamofire30DataResponseSerializerProtocolP9serialize7request8response4data5error16SerializedObjectQz10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0B0VSgs5Error_pSgtKF": {"name": "serialize(request:response:data:error:)", "parent_name": "StringResponseSerializer"}, "Classes/DataResponseSerializer.html#/s:9Alamofire18ResponseSerializerP16dataPreprocessorAA04DataE0_pvp": {"name": "dataPreprocessor", "parent_name": "DataResponseSerializer"}, "Classes/DataResponseSerializer.html#/s:9Alamofire18ResponseSerializerP05emptyB5CodesShySiGvp": {"name": "emptyResponseCodes", "parent_name": "DataResponseSerializer"}, "Classes/DataResponseSerializer.html#/s:9Alamofire18ResponseSerializerP19emptyRequestMethodsShyAA10HTTPMethodVGvp": {"name": "emptyRequestMethods", "parent_name": "DataResponseSerializer"}, "Classes/DataResponseSerializer.html#/s:9Alamofire22DataResponseSerializerC16dataPreprocessor05emptyC5Codes0G14RequestMethodsAcA0bF0_p_ShySiGShyAA10HTTPMethodVGtcfc": {"name": "init(dataPreprocessor:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code>DataResponseSerializer</code> using the provided parameters.</p>", "parent_name": "DataResponseSerializer"}, "Classes/DataResponseSerializer.html#/s:9Alamofire30DataResponseSerializerProtocolP9serialize7request8response4data5error16SerializedObjectQz10Foundation10URLRequestVSg_So17NSHTTPURLResponseCSgAK0B0VSgs5Error_pSgtKF": {"name": "serialize(request:response:data:error:)", "parent_name": "DataResponseSerializer"}, "Classes/Interceptor.html#/s:9Alamofire11InterceptorC8adaptersSayAA14RequestAdapter_pGvp": {"name": "adapters", "abstract": "<p>All <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code>s associated with the instance. These adapters will be run until one fails.</p>", "parent_name": "Interceptor"}, "Classes/Interceptor.html#/s:9Alamofire11InterceptorC8retriersSayAA14RequestRetrier_pGvp": {"name": "retriers", "abstract": "<p>All <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code>s associated with the instance. These retriers will be run one at a time until one triggers retry.</p>", "parent_name": "Interceptor"}, "Classes/Interceptor.html#/s:9Alamofire11InterceptorC12adaptHandler05retryD0ACy10Foundation10URLRequestV_AA7SessionCys6ResultOyAHs5Error_pGYbctYbc_yAA7RequestC_AJsAM_pyAA05RetryI0OYbctYbctcfc": {"name": "init(adaptHandler:retryHandler:)", "abstract": "<p>Creates an instance from <code><a href=\"36f8f5912051ae747ef441d6511ca4cbTypealiases.html#/s:9Alamofire12AdaptHandlera\">AdaptHandler</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbTypealiases.html#/s:9Alamofire12RetryHandlera\">RetryHandler</a></code> closures.</p>", "parent_name": "Interceptor"}, "Classes/Interceptor.html#/s:9Alamofire11InterceptorC7adapter7retrierAcA14RequestAdapter_p_AA0E7Retrier_ptcfc": {"name": "init(adapter:retrier:)", "abstract": "<p>Creates an instance from <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code> values.</p>", "parent_name": "Interceptor"}, "Classes/Interceptor.html#/s:9Alamofire11InterceptorC8adapters8retriers12interceptorsACSayAA14RequestAdapter_pG_SayAA0F7Retrier_pGSayAA0fB0_pGtcfc": {"name": "init(adapters:retriers:interceptors:)", "abstract": "<p>Creates an instance from the arrays of <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code> values.</p>", "parent_name": "Interceptor"}, "Classes/Interceptor.html#/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:for:completion:)", "parent_name": "Interceptor"}, "Classes/Interceptor.html#/s:9Alamofire14RequestAdapterP5adapt_5using10completiony10Foundation10URLRequestV_AA0bC5StateVys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:using:completion:)", "parent_name": "Interceptor"}, "Classes/Interceptor.html#/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF": {"name": "retry(_:for:dueTo:completion:)", "parent_name": "Interceptor"}, "Classes/Retrier.html#/s:9Alamofire7RetrierCyACyAA7RequestC_AA7SessionCs5Error_pyAA11RetryResultOctccfc": {"name": "init(_:)", "abstract": "<p>Creates an instance using the provided closure.</p>", "parent_name": "Retrier"}, "Classes/Retrier.html#/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF": {"name": "retry(_:for:dueTo:completion:)", "parent_name": "Retrier"}, "Classes/Adapter.html#/s:9Alamofire7AdapterCyACy10Foundation10URLRequestV_AA7SessionCys6ResultOyAFs5Error_pGctccfc": {"name": "init(_:)", "abstract": "<p>Creates an instance using the provided closure.</p>", "parent_name": "Adapter"}, "Classes/Adapter.html#/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:for:completion:)", "parent_name": "Adapter"}, "Classes/Adapter.html#/s:9Alamofire14RequestAdapterP5adapt_5using10completiony10Foundation10URLRequestV_AA0bC5StateVys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:using:completion:)", "parent_name": "Adapter"}, "Classes/NetworkReachabilityManager/NetworkReachabilityStatus/ConnectionType.html#/s:9Alamofire26NetworkReachabilityManagerC0bC6StatusO14ConnectionTypeO14ethernetOrWiFiyA2GmF": {"name": "ethernetOrWiFi", "abstract": "<p>The connection type is either over Ethernet or WiFi.</p>", "parent_name": "ConnectionType"}, "Classes/NetworkReachabilityManager/NetworkReachabilityStatus/ConnectionType.html#/s:9Alamofire26NetworkReachabilityManagerC0bC6StatusO14ConnectionTypeO8cellularyA2GmF": {"name": "cellular", "abstract": "<p>The connection type is a cellular connection.</p>", "parent_name": "ConnectionType"}, "Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html#/s:9Alamofire26NetworkReachabilityManagerC0bC6StatusO7unknownyA2EmF": {"name": "unknown", "abstract": "<p>It is unknown whether the network is reachable.</p>", "parent_name": "NetworkReachabilityStatus"}, "Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html#/s:9Alamofire26NetworkReachabilityManagerC0bC6StatusO12notReachableyA2EmF": {"name": "notReachable", "abstract": "<p>The network is not reachable.</p>", "parent_name": "NetworkReachabilityStatus"}, "Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html#/s:9Alamofire26NetworkReachabilityManagerC0bC6StatusO9reachableyA2E14ConnectionTypeOcAEmF": {"name": "reachable(_:)", "abstract": "<p>The network is reachable on the associated <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/NetworkReachabilityManager/NetworkReachabilityStatus/ConnectionType.html\">ConnectionType</a></code>.</p>", "parent_name": "NetworkReachabilityStatus"}, "Classes/NetworkReachabilityManager/NetworkReachabilityStatus/ConnectionType.html": {"name": "ConnectionType", "abstract": "<p>Defines the various connection types detected by reachability flags.</p>", "parent_name": "NetworkReachabilityStatus"}, "Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html": {"name": "NetworkReachabilityStatus", "abstract": "<p>Defines the various states of network reachability.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC8Listenera": {"name": "Listener", "abstract": "<p>A closure executed when the network reachability status changes. The closure takes a single argument: the", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC7defaultACSgvpZ": {"name": "default", "abstract": "<p>Default <code>NetworkReachabilityManager</code> for the zero address and a <code>listenerQueue</code> of <code>.main</code>.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC11isReachableSbvp": {"name": "isReachable", "abstract": "<p>Whether the network is currently reachable.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC21isReachableOnCellularSbvp": {"name": "isReachableOnCellular", "abstract": "<p>Whether the network is currently reachable over the cellular interface.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC27isReachableOnEthernetOrWiFiSbvp": {"name": "isReachableOnEthernetOrWiFi", "abstract": "<p>Whether the network is currently reachable over Ethernet or WiFi interface.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC17reachabilityQueueSo17OS_dispatch_queueCvp": {"name": "reachabilityQueue", "abstract": "<p><code>DispatchQueue</code> on which reachability will update.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC5flagsSo09SCNetworkC5FlagsVSgvp": {"name": "flags", "abstract": "<p>Flags of the current reachability type, if any.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC6statusAC0bC6StatusOvp": {"name": "status", "abstract": "<p>The current network reachability status.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC4hostACSgSS_tcfc": {"name": "init(host:)", "abstract": "<p>Creates an instance with the specified host.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerCACSgycfc": {"name": "init()", "abstract": "<p>Creates an instance that monitors the address 0.0.0.0.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC14startListening7onQueue0G16UpdatePerformingSbSo17OS_dispatch_queueC_yAC0bC6StatusOctF": {"name": "startListening(onQueue:onUpdatePerforming:)", "abstract": "<p>Starts listening for changes in network reachability status.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/NetworkReachabilityManager.html#/s:9Alamofire26NetworkReachabilityManagerC13stopListeningyyF": {"name": "stopListening()", "abstract": "<p>Stops listening for changes in network reachability status.</p>", "parent_name": "NetworkReachabilityManager"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ": {"name": "encodingMemoryThreshold", "abstract": "<p>Default memory threshold used when encoding <code>MultipartFormData</code>, in bytes.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC11contentTypeSSvp": {"name": "contentType", "abstract": "<p>The <code>Content-Type</code> header value containing the boundary used to generate the <code>multipart/form-data</code>.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC13contentLengths6UInt64Vvp": {"name": "contentLength", "abstract": "<p>The content length of all body parts used to generate the <code>multipart/form-data</code> not including the boundaries.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC8boundarySSvp": {"name": "boundary", "abstract": "<p>The boundary used to separate the body parts in the encoded form data.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC11fileManager8boundaryACSo06NSFileF0C_SSSgtcfc": {"name": "init(fileManager:boundary:)", "abstract": "<p>Creates an instance.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC6append_8withName04fileG08mimeTypey10Foundation0D0V_S2SSgAKtF": {"name": "append(_:withName:fileName:mimeType:)", "abstract": "<p>Creates a body part from the data and appends it to the instance.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC6append_8withNamey10Foundation3URLV_SStF": {"name": "append(_:withName:)", "abstract": "<p>Creates a body part from the file and appends it to the instance.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC6append_8withName04fileG08mimeTypey10Foundation3URLV_S3StF": {"name": "append(_:withName:fileName:mimeType:)", "abstract": "<p>Creates a body part from the file and appends it to the instance.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC6append_10withLength4name8fileName8mimeTypeySo13NSInputStreamC_s6UInt64VS3StF": {"name": "append(_:withLength:name:fileName:mimeType:)", "abstract": "<p>Creates a body part from the stream and appends it to the instance.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC6append_10withLength7headersySo13NSInputStreamC_s6UInt64VAA11HTTPHeadersVtF": {"name": "append(_:withLength:headers:)", "abstract": "<p>Creates a body part with the stream, length, and headers and appends it to the instance.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC6encode10Foundation0D0VyKF": {"name": "encode()", "abstract": "<p>Encodes all appended body parts into a single <code>Data</code> value.</p>", "parent_name": "MultipartFormData"}, "Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC012writeEncodedD02toy10Foundation3URLV_tKF": {"name": "writeEncodedData(to:)", "abstract": "<p>Writes all appended body parts to the given file <code>URL</code>.</p>", "parent_name": "MultipartFormData"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC32sessionDidBecomeInvalidWithErrorySo12NSURLSessionC_s0J0_pSgtcSgvp": {"name": "sessionDidBecomeInvalidWithError", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_25didBecomeInvalidWithErrorySo12NSURLSessionC_s0J0_pSgtF\">urlSession(_:didBecomeInvalidWithError:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC23taskDidReceiveChallengeySo12NSURLSessionC_So0I4TaskCSo019NSURLAuthenticationH0CtcSgvp": {"name": "taskDidReceiveChallenge", "abstract": "<p>Closure called on the <code>urlSession(_:task:didReceive:completionHandler:)</code>.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC19taskDidSendBodyDataySo12NSURLSessionC_So0J4TaskCs5Int64VA2JtcSgvp": {"name": "taskDidSendBodyData", "abstract": "<p>Closure that receives <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task15didSendBodyData14totalBytesSent0kl10ExpectedToH0ySo12NSURLSessionC_So0P4TaskCs5Int64VA2NtF\">urlSession(_:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC21taskNeedNewBodyStreamySo12NSURLSessionC_So0J4TaskCtcSgvp": {"name": "taskNeedNewBodyStream", "abstract": "<p>Closure called on the <code>urlSession(_:task:needNewBodyStream:)</code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC30taskWillPerformHTTPRedirectionySo12NSURLSessionC_So0I4TaskCSo17NSHTTPURLResponseC10Foundation10URLRequestVtcSgvp": {"name": "taskWillPerformHTTPRedirection", "abstract": "<p>Closure called on the <code>urlSession(_:task:willPerformHTTPRedirection:newRequest:completionHandler:)</code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC30taskDidFinishCollectingMetricsySo12NSURLSessionC_So0J4TaskCSo0jkI0CtcSgvp": {"name": "taskDidFinishCollectingMetrics", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task19didFinishCollectingySo12NSURLSessionC_So0J4TaskCSo0jK7MetricsCtF\">urlSession(_:task:didFinishCollecting:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC15taskDidCompleteySo12NSURLSessionC_So0H4TaskCs5Error_pSgtcSgvp": {"name": "taskDidComplete", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task20didCompleteWithErrorySo12NSURLSessionC_So0K4TaskCs0J0_pSgtF\">urlSession(_:task:didCompleteWithError:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC28taskIsWaitingForConnectivityySo12NSURLSessionC_So0J4TaskCtcSgvp": {"name": "taskIsWaitingForConnectivity", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_28taskIsWaitingForConnectivityySo12NSURLSessionC_So0K4TaskCtF\">urlSession(_:taskIsWaitingForConnectivity:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC26dataTaskDidReceiveResponseySo12NSURLSessionC_So0j4DataF0CSo13NSURLResponseCtcSgvp": {"name": "dataTaskDidReceiveResponse", "abstract": "<p>Closure called on the <code>urlSession(_:dataTask:didReceive:completionHandler:)</code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC22dataTaskDidReceiveDataySo12NSURLSessionC_So0jiF0C10Foundation0I0VtcSgvp": {"name": "dataTaskDidReceiveData", "abstract": "<p>Closure that receives the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0CSo13NSURLResponseCtF\">urlSession(_:dataTask:didReceive:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC25dataTaskWillCacheResponseySo12NSURLSessionC_So0j4DataF0CSo19NSCachedURLResponseCtcSgvp": {"name": "dataTaskWillCacheResponse", "abstract": "<p>Closure called on the <code>urlSession(_:dataTask:willCacheResponse:completionHandler:)</code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC37downloadTaskDidFinishDownloadingToURLySo12NSURLSessionC_So0l8DownloadF0C10Foundation0K0VtcSgvp": {"name": "downloadTaskDidFinishDownloadingToURL", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask22didFinishDownloadingToySo12NSURLSessionC_So0l8DownloadG0C10Foundation3URLVtF\">urlSession(_:downloadTask:didFinishDownloadingTo:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC24downloadTaskDidWriteDataySo12NSURLSessionC_So0j8DownloadF0Cs5Int64VA2JtcSgvp": {"name": "downloadTaskDidWriteData", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask12didWriteData17totalBytesWritten0kl10ExpectedToI0ySo12NSURLSessionC_So0p8DownloadG0Cs5Int64VA2NtF\">urlSession(_:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:)</a></code>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC29downloadTaskDidResumeAtOffsetySo12NSURLSessionC_So0k8DownloadF0Cs5Int64VAJtcSgvp": {"name": "downloadTaskDidResumeAtOffset", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask17didResumeAtOffset18expectedTotalBytesySo12NSURLSessionC_So0o8DownloadG0Cs5Int64VAMtF\">urlSession(_:downloadTask:didResumeAtOffset:expectedTotalBytes:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC33requestDidCreateInitialURLRequestyAA7RequestC_10Foundation0I0VtcSgvp": {"name": "requestDidCreateInitialURLRequest", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_26didCreateInitialURLRequestyAA7RequestC_10Foundation0H0VtF\">request(_:didCreateInitialURLRequest:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC41requestDidFailToCreateURLRequestWithErroryAA7RequestC_AA7AFErrorOtcSgvp": {"name": "requestDidFailToCreateURLRequestWithError", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateURLRequestWithErroryAA7RequestC_AA7AFErrorOtF\">request(_:didFailToCreateURLRequestWithError:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC038requestDidAdaptInitialRequestToAdaptedI0yAA0I0C_10Foundation10URLRequestVAItcSgvp": {"name": "requestDidAdaptInitialRequestToAdaptedRequest", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_22didAdaptInitialRequest2toyAA0H0C_10Foundation10URLRequestVAKtF\">request(_:didAdaptInitialRequest:to:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC40requestDidFailToAdaptURLRequestWithErroryAA7RequestC_10Foundation0J0VAA7AFErrorOtcSgvp": {"name": "requestDidFailToAdaptURLRequestWithError", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_24didFailToAdaptURLRequest9withErroryAA7RequestC_10Foundation0I0VAA7AFErrorOtF\">request(_:didFailToAdaptURLRequest:withError:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC26requestDidCreateURLRequestyAA7RequestC_10Foundation0H0VtcSgvp": {"name": "requestDidCreateURLRequest", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateURLRequestyAA7RequestC_10Foundation0G0VtF\">request(_:didCreateURLRequest:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC20requestDidCreateTaskyAA7RequestC_So012NSURLSessionH0CtcSgvp": {"name": "requestDidCreateTask", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCreateTaskyAA7RequestC_So012NSURLSessionG0CtF\">request(_:didCreateTask:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC23requestDidGatherMetricsyAA7RequestC_So016NSURLSessionTaskH0CtcSgvp": {"name": "requestDidGatherMetrics", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didGatherMetricsyAA7RequestC_So016NSURLSessionTaskG0CtF\">request(_:didGatherMetrics:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC32requestDidFailTaskEarlyWithErroryAA7RequestC_So012NSURLSessionH0CAA7AFErrorOtcSgvp": {"name": "requestDidFailTaskEarlyWithError", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_11didFailTask14earlyWithErroryAA7RequestC_So012NSURLSessionG0CAA7AFErrorOtF\">request(_:didFailTask:earlyWithError:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC31requestDidCompleteTaskWithErroryAA7RequestC_So012NSURLSessionH0CAA7AFErrorOSgtcSgvp": {"name": "requestDidCompleteTaskWithError", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_15didCompleteTask4withyAA7RequestC_So012NSURLSessionG0CAA7AFErrorOSgtF\">request(_:didCompleteTask:with:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC17requestIsRetryingyAA7RequestCcSgvp": {"name": "requestIsRetrying", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP17requestIsRetryingyyAA7RequestCF\">requestIsRetrying(_:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC16requestDidFinishyAA7RequestCcSgvp": {"name": "requestDidFinish", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidFinishyyAA7RequestCF\">requestDidFinish(_:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC16requestDidResumeyAA7RequestCcSgvp": {"name": "requestDidResume", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidResumeyyAA7RequestCF\">requestDidResume(_:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC20requestDidResumeTaskyAA7RequestC_So012NSURLSessionH0CtcSgvp": {"name": "requestDidResumeTask", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didResumeTaskyAA7RequestC_So012NSURLSessionG0CtF\">request(_:didResumeTask:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC17requestDidSuspendyAA7RequestCcSgvp": {"name": "requestDidSuspend", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP17requestDidSuspendyyAA7RequestCF\">requestDidSuspend(_:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC21requestDidSuspendTaskyAA7RequestC_So012NSURLSessionH0CtcSgvp": {"name": "requestDidSuspendTask", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_14didSuspendTaskyAA7RequestC_So012NSURLSessionG0CtF\">request(_:didSuspendTask:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC16requestDidCancelyAA7RequestCcSgvp": {"name": "requestDidCancel", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidCancelyyAA7RequestCF\">requestDidCancel(_:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC20requestDidCancelTaskyAA7RequestC_So012NSURLSessionH0CtcSgvp": {"name": "requestDidCancelTask", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCancelTaskyAA7RequestC_So012NSURLSessionG0CtF\">request(_:didCancelTask:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC47requestDidValidateRequestResponseDataWithResultyAA0jH0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAG0J0VSgs0L0Oyyts5Error_pGtcSgvp": {"name": "requestDidValidateRequestResponseDataWithResult", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response4data10withResultyAA04DataG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAK0L0VSgs0K0Oyyts5Error_pGtF\">request(_:didValidateRequest:response:data:withResult:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC23requestDidParseResponseyAA11DataRequestC_AA0iH0Vy10Foundation0I0VSgAA7AFErrorOGtcSgvp": {"name": "requestDidParseResponse", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA11DataRequestC_AA0hG0Vy10Foundation0H0VSgAA7AFErrorOGtF\">request(_:didParseResponse:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC43requestDidValidateRequestResponseWithResultyAA010DataStreamH0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCs0K0Oyyts5Error_pGtcSgvp": {"name": "requestDidValidateRequestResponseWithResult", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response10withResultyAA010DataStreamG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCs0J0Oyyts5Error_pGtF\">request(_:didValidateRequest:response:withResult:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC26requestDidCreateUploadableyAA13UploadRequestC_AF0H0OtcSgvp": {"name": "requestDidCreateUploadable", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateUploadableyAA13UploadRequestC_AG0G0OtF\">request(_:didCreateUploadable:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC41requestDidFailToCreateUploadableWithErroryAA13UploadRequestC_AA7AFErrorOtcSgvp": {"name": "requestDidFailToCreateUploadableWithError", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateUploadableWithErroryAA13UploadRequestC_AA7AFErrorOtF\">request(_:didFailToCreateUploadableWithError:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC28requestDidProvideInputStreamyAA13UploadRequestC_So07NSInputI0CtcSgvp": {"name": "requestDidProvideInputStream", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_21didProvideInputStreamyAA13UploadRequestC_So07NSInputH0CtF\">request(_:didProvideInputStream:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC46requestDidFinishDownloadingUsingTaskWithResultyAA15DownloadRequestC_So012NSURLSessionJ0Cs0L0Oy10Foundation3URLVAA7AFErrorOGtcSgvp": {"name": "requestDidFinishDownloadingUsingTaskWithResult", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_25didFinishDownloadingUsing4withyAA15DownloadRequestC_So16NSURLSessionTaskCs6ResultOy10Foundation3URLVAA7AFErrorOGtF\">request(_:didFinishDownloadingUsing:with:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC30requestDidCreateDestinationURLyAA15DownloadRequestC_10Foundation0I0VtcSgvp": {"name": "requestDidCreateDestinationURL", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_23didCreateDestinationURLyAA15DownloadRequestC_10Foundation0H0VtF\">request(_:didCreateDestinationURL:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC50requestDidValidateRequestResponseFileURLWithResultyAA08DownloadH0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAG3URLVSgs0L0Oyyts5Error_pGtcSgvp": {"name": "requestDidValidateRequestResponseFileURLWithResult", "abstract": "<p>Closure called on the <code>request(_:didValidateRequest:response:temporaryURL:destinationURL:withResult:)</code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC31requestDidParseDownloadResponseyAA0H7RequestC_AA0hI0Vy10Foundation3URLVSgAA7AFErrorOGtcSgvp": {"name": "requestDidParseDownloadResponse", "abstract": "<p>Closure called on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA11DataRequestC_AA0hG0Vy10Foundation0H0VSgAA7AFErrorOGtF\">request(_:didParseResponse:)</a></code> event.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP5queueSo012OS_dispatch_D0Cvp": {"name": "queue", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire19ClosureEventMonitorC5queueACSo012OS_dispatch_E0C_tcfc": {"name": "init(queue:)", "abstract": "<p>Creates an instance using the provided queue.</p>", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_25didBecomeInvalidWithErrorySo12NSURLSessionC_s0J0_pSgtF": {"name": "urlSession(_:didBecomeInvalidWithError:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task10didReceiveySo12NSURLSessionC_So0I4TaskCSo28NSURLAuthenticationChallengeCtF": {"name": "urlSession(_:task:didReceive:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task15didSendBodyData14totalBytesSent0kl10ExpectedToH0ySo12NSURLSessionC_So0P4TaskCs5Int64VA2NtF": {"name": "urlSession(_:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_22taskNeedsNewBodyStreamySo12NSURLSessionC_So0K4TaskCtF": {"name": "urlSession(_:taskNeedsNewBodyStream:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task26willPerformHTTPRedirection10newRequestySo12NSURLSessionC_So0L4TaskCSo17NSHTTPURLResponseC10Foundation10URLRequestVtF": {"name": "urlSession(_:task:willPerformHTTPRedirection:newRequest:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task19didFinishCollectingySo12NSURLSessionC_So0J4TaskCSo0jK7MetricsCtF": {"name": "urlSession(_:task:didFinishCollecting:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task20didCompleteWithErrorySo12NSURLSessionC_So0K4TaskCs0J0_pSgtF": {"name": "urlSession(_:task:didCompleteWithError:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_28taskIsWaitingForConnectivityySo12NSURLSessionC_So0K4TaskCtF": {"name": "urlSession(_:taskIsWaitingForConnectivity:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0CSo13NSURLResponseCtF": {"name": "urlSession(_:dataTask:didReceive:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0C10Foundation0K0VtF": {"name": "urlSession(_:dataTask:didReceive:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask17willCacheResponseySo12NSURLSessionC_So0k4DataG0CSo19NSCachedURLResponseCtF": {"name": "urlSession(_:dataTask:willCacheResponse:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask17didResumeAtOffset18expectedTotalBytesySo12NSURLSessionC_So0o8DownloadG0Cs5Int64VAMtF": {"name": "urlSession(_:downloadTask:didResumeAtOffset:expectedTotalBytes:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask12didWriteData17totalBytesWritten0kl10ExpectedToI0ySo12NSURLSessionC_So0p8DownloadG0Cs5Int64VA2NtF": {"name": "urlSession(_:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask22didFinishDownloadingToySo12NSURLSessionC_So0l8DownloadG0C10Foundation3URLVtF": {"name": "urlSession(_:downloadTask:didFinishDownloadingTo:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_26didCreateInitialURLRequestyAA7RequestC_10Foundation0H0VtF": {"name": "request(_:didCreateInitialURLRequest:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateURLRequestWithErroryAA7RequestC_AA7AFErrorOtF": {"name": "request(_:didFailToCreateURLRequestWithError:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_22didAdaptInitialRequest2toyAA0H0C_10Foundation10URLRequestVAKtF": {"name": "request(_:didAdaptInitialRequest:to:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_24didFailToAdaptURLRequest9withErroryAA7RequestC_10Foundation0I0VAA7AFErrorOtF": {"name": "request(_:didFailToAdaptURLRequest:withError:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateURLRequestyAA7RequestC_10Foundation0G0VtF": {"name": "request(_:didCreateURLRequest:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCreateTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didCreateTask:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didGatherMetricsyAA7RequestC_So016NSURLSessionTaskG0CtF": {"name": "request(_:didGatherMetrics:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_11didFailTask14earlyWithErroryAA7RequestC_So012NSURLSessionG0CAA7AFErrorOtF": {"name": "request(_:didFailTask:earlyWithError:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_15didCompleteTask4withyAA7RequestC_So012NSURLSessionG0CAA7AFErrorOSgtF": {"name": "request(_:didCompleteTask:with:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP17requestIsRetryingyyAA7RequestCF": {"name": "requestIsRetrying(_:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidFinishyyAA7RequestCF": {"name": "requestDidFinish(_:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidResumeyyAA7RequestCF": {"name": "requestDidResume(_:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didResumeTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didResumeTask:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP17requestDidSuspendyyAA7RequestCF": {"name": "requestDidSuspend(_:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_14didSuspendTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didSuspendTask:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidCancelyyAA7RequestCF": {"name": "requestDidCancel(_:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCancelTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didCancelTask:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response4data10withResultyAA04DataG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAK0L0VSgs0K0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:data:withResult:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA11DataRequestC_AA0hG0Vy10Foundation0H0VSgAA7AFErrorOGtF": {"name": "request(_:didParseResponse:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response10withResultyAA010DataStreamG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCs0J0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:withResult:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateUploadableyAA13UploadRequestC_AG0G0OtF": {"name": "request(_:didCreateUploadable:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateUploadableWithErroryAA13UploadRequestC_AA7AFErrorOtF": {"name": "request(_:didFailToCreateUploadableWithError:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_21didProvideInputStreamyAA13UploadRequestC_So07NSInputH0CtF": {"name": "request(_:didProvideInputStream:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_25didFinishDownloadingUsing4withyAA15DownloadRequestC_So16NSURLSessionTaskCs6ResultOy10Foundation3URLVAA7AFErrorOGtF": {"name": "request(_:didFinishDownloadingUsing:with:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_23didCreateDestinationURLyAA15DownloadRequestC_10Foundation0H0VtF": {"name": "request(_:didCreateDestinationURL:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response7fileURL10withResultyAA08DownloadG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAK0J0VSgs0L0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:fileURL:withResult:)", "parent_name": "ClosureEventMonitor"}, "Classes/ClosureEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA15DownloadRequestC_AA0hG0Vy10Foundation3URLVSgAA7AFErrorOGtF": {"name": "request(_:didParseResponse:)", "parent_name": "ClosureEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP5queueSo012OS_dispatch_D0Cvp": {"name": "queue", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_25didBecomeInvalidWithErrorySo12NSURLSessionC_s0J0_pSgtF": {"name": "urlSession(_:didBecomeInvalidWithError:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task10didReceiveySo12NSURLSessionC_So0I4TaskCSo28NSURLAuthenticationChallengeCtF": {"name": "urlSession(_:task:didReceive:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task15didSendBodyData14totalBytesSent0kl10ExpectedToH0ySo12NSURLSessionC_So0P4TaskCs5Int64VA2NtF": {"name": "urlSession(_:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_22taskNeedsNewBodyStreamySo12NSURLSessionC_So0K4TaskCtF": {"name": "urlSession(_:taskNeedsNewBodyStream:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task26willPerformHTTPRedirection10newRequestySo12NSURLSessionC_So0L4TaskCSo17NSHTTPURLResponseC10Foundation10URLRequestVtF": {"name": "urlSession(_:task:willPerformHTTPRedirection:newRequest:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task19didFinishCollectingySo12NSURLSessionC_So0J4TaskCSo0jK7MetricsCtF": {"name": "urlSession(_:task:didFinishCollecting:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_4task20didCompleteWithErrorySo12NSURLSessionC_So0K4TaskCs0J0_pSgtF": {"name": "urlSession(_:task:didCompleteWithError:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_28taskIsWaitingForConnectivityySo12NSURLSessionC_So0K4TaskCtF": {"name": "urlSession(_:taskIsWaitingForConnectivity:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0CSo13NSURLResponseCtF": {"name": "urlSession(_:dataTask:didReceive:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask10didReceiveySo12NSURLSessionC_So0j4DataG0C10Foundation0K0VtF": {"name": "urlSession(_:dataTask:didReceive:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_8dataTask17willCacheResponseySo12NSURLSessionC_So0k4DataG0CSo19NSCachedURLResponseCtF": {"name": "urlSession(_:dataTask:willCacheResponse:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask17didResumeAtOffset18expectedTotalBytesySo12NSURLSessionC_So0o8DownloadG0Cs5Int64VAMtF": {"name": "urlSession(_:downloadTask:didResumeAtOffset:expectedTotalBytes:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask12didWriteData17totalBytesWritten0kl10ExpectedToI0ySo12NSURLSessionC_So0p8DownloadG0Cs5Int64VA2NtF": {"name": "urlSession(_:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP10urlSession_12downloadTask22didFinishDownloadingToySo12NSURLSessionC_So0l8DownloadG0C10Foundation3URLVtF": {"name": "urlSession(_:downloadTask:didFinishDownloadingTo:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_26didCreateInitialURLRequestyAA7RequestC_10Foundation0H0VtF": {"name": "request(_:didCreateInitialURLRequest:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateURLRequestWithErroryAA7RequestC_AA7AFErrorOtF": {"name": "request(_:didFailToCreateURLRequestWithError:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_22didAdaptInitialRequest2toyAA0H0C_10Foundation10URLRequestVAKtF": {"name": "request(_:didAdaptInitialRequest:to:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_24didFailToAdaptURLRequest9withErroryAA7RequestC_10Foundation0I0VAA7AFErrorOtF": {"name": "request(_:didFailToAdaptURLRequest:withError:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateURLRequestyAA7RequestC_10Foundation0G0VtF": {"name": "request(_:didCreateURLRequest:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCreateTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didCreateTask:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didGatherMetricsyAA7RequestC_So016NSURLSessionTaskG0CtF": {"name": "request(_:didGatherMetrics:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_11didFailTask14earlyWithErroryAA7RequestC_So012NSURLSessionG0CAA7AFErrorOtF": {"name": "request(_:didFailTask:earlyWithError:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_15didCompleteTask4withyAA7RequestC_So012NSURLSessionG0CAA7AFErrorOSgtF": {"name": "request(_:didCompleteTask:with:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP17requestIsRetryingyyAA7RequestCF": {"name": "requestIsRetrying(_:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidFinishyyAA7RequestCF": {"name": "requestDidFinish(_:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidResumeyyAA7RequestCF": {"name": "requestDidResume(_:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didResumeTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didResumeTask:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP17requestDidSuspendyyAA7RequestCF": {"name": "requestDidSuspend(_:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_14didSuspendTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didSuspendTask:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP16requestDidCancelyyAA7RequestCF": {"name": "requestDidCancel(_:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_13didCancelTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didCancelTask:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response4data10withResultyAA04DataG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAK0L0VSgs0K0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:data:withResult:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA11DataRequestC_AA0hG0Vy10Foundation0H0VSgAA7AFErrorOGtF": {"name": "request(_:didParseResponse:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA11DataRequestC_AA0hG0Vyqd__AA7AFErrorOGts8SendableRd__lF": {"name": "request(_:didParseResponse:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response10withResultyAA010DataStreamG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCs0J0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:withResult:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_14didParseStreamyAA04DataG7RequestC_s6ResultOyqd__AA7AFErrorOGts8SendableRd__lF": {"name": "request(_:didParseStream:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_19didCreateUploadableyAA13UploadRequestC_AG0G0OtF": {"name": "request(_:didCreateUploadable:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_34didFailToCreateUploadableWithErroryAA13UploadRequestC_AA7AFErrorOtF": {"name": "request(_:didFailToCreateUploadableWithError:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_21didProvideInputStreamyAA13UploadRequestC_So07NSInputH0CtF": {"name": "request(_:didProvideInputStream:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_25didFinishDownloadingUsing4withyAA15DownloadRequestC_So16NSURLSessionTaskCs6ResultOy10Foundation3URLVAA7AFErrorOGtF": {"name": "request(_:didFinishDownloadingUsing:with:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_23didCreateDestinationURLyAA15DownloadRequestC_10Foundation0H0VtF": {"name": "request(_:didCreateDestinationURL:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_18didValidateRequest8response7fileURL10withResultyAA08DownloadG0C_10Foundation10URLRequestVSgSo17NSHTTPURLResponseCAK0J0VSgs0L0Oyyts5Error_pGtF": {"name": "request(_:didValidateRequest:response:fileURL:withResult:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA15DownloadRequestC_AA0hG0Vy10Foundation3URLVSgAA7AFErrorOGtF": {"name": "request(_:didParseResponse:)", "parent_name": "CompositeEventMonitor"}, "Classes/CompositeEventMonitor.html#/s:9Alamofire12EventMonitorP7request_16didParseResponseyAA15DownloadRequestC_AA0hG0Vyqd__AA7AFErrorOGts8SendableRd__lF": {"name": "request(_:didParseResponse:)", "parent_name": "CompositeEventMonitor"}, "Classes/AuthenticationInterceptor/RefreshWindow.html#/s:9Alamofire25AuthenticationInterceptorC13RefreshWindowV8intervalSdvp": {"name": "interval", "abstract": "<p><code>TimeInterval</code> defining the duration of the time window before the current time in which the number of", "parent_name": "RefreshWindow"}, "Classes/AuthenticationInterceptor/RefreshWindow.html#/s:9Alamofire25AuthenticationInterceptorC13RefreshWindowV15maximumAttemptsSivp": {"name": "maximumAttempts", "abstract": "<p>Total refresh attempts allowed within <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/AuthenticationInterceptor/RefreshWindow.html#/s:9Alamofire25AuthenticationInterceptorC13RefreshWindowV8intervalSdvp\">interval</a></code> before throwing an <code>.excessiveRefresh</code> error.</p>", "parent_name": "RefreshWindow"}, "Classes/AuthenticationInterceptor/RefreshWindow.html#/s:9Alamofire25AuthenticationInterceptorC13RefreshWindowV8interval15maximumAttemptsAEyx_GSd_Sitcfc": {"name": "init(interval:maximumAttempts:)", "abstract": "<p>Creates a <code>RefreshWindow</code> instance from the specified <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/AuthenticationInterceptor/RefreshWindow.html#/s:9Alamofire25AuthenticationInterceptorC13RefreshWindowV8intervalSdvp\">interval</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/AuthenticationInterceptor/RefreshWindow.html#/s:9Alamofire25AuthenticationInterceptorC13RefreshWindowV15maximumAttemptsSivp\">maximumAttempts</a></code>.</p>", "parent_name": "RefreshWindow"}, "Classes/AuthenticationInterceptor.html#/s:9Alamofire25AuthenticationInterceptorC10Credentiala": {"name": "Credential", "abstract": "<p>Type of credential used to authenticate requests.</p>", "parent_name": "AuthenticationInterceptor"}, "Classes/AuthenticationInterceptor/RefreshWindow.html": {"name": "RefreshWindow", "abstract": "<p>Type that defines a time window used to identify excessive refresh calls. When enabled, prior to executing a", "parent_name": "AuthenticationInterceptor"}, "Classes/AuthenticationInterceptor.html#/s:9Alamofire25AuthenticationInterceptorC10credential10CredentialQzSgvp": {"name": "credential", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/AuthenticationInterceptor.html#/s:9Alamofire25AuthenticationInterceptorC10Credentiala\">Credential</a></code> used to authenticate requests.</p>", "parent_name": "AuthenticationInterceptor"}, "Classes/AuthenticationInterceptor.html#/s:9Alamofire25AuthenticationInterceptorC13authenticator10credential13refreshWindowACyxGx_10CredentialQzSgAC07RefreshG0Vyx_GSgtcfc": {"name": "init(authenticator:credential:refreshWindow:)", "abstract": "<p>Creates an <code>AuthenticationInterceptor</code> instance from the specified parameters.</p>", "parent_name": "AuthenticationInterceptor"}, "Classes/AuthenticationInterceptor.html#/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF": {"name": "adapt(_:for:completion:)", "parent_name": "AuthenticationInterceptor"}, "Classes/AuthenticationInterceptor.html#/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF": {"name": "retry(_:for:dueTo:completion:)", "parent_name": "AuthenticationInterceptor"}, "Classes/UploadRequest/Uploadable.html#/s:9Alamofire13UploadRequestC10UploadableO4datayAE10Foundation4DataVcAEmF": {"name": "data(_:)", "abstract": "<p>Upload from the provided <code>Data</code> value.</p>", "parent_name": "Uploadable"}, "Classes/UploadRequest/Uploadable.html#/s:9Alamofire13UploadRequestC10UploadableO4fileyAE10Foundation3URLV_SbtcAEmF": {"name": "file(_:shouldRemove:)", "abstract": "<p>Upload from the provided file <code>URL</code>, as well as a <code>Bool</code> determining whether the source file should be", "parent_name": "Uploadable"}, "Classes/UploadRequest/Uploadable.html#/s:9Alamofire13UploadRequestC10UploadableO6streamyAESo13NSInputStreamCcAEmF": {"name": "stream(_:)", "abstract": "<p>Upload from the provided <code>InputStream</code>.</p>", "parent_name": "Uploadable"}, "Classes/UploadRequest/Uploadable.html#/s:9Alamofire21UploadableConvertibleP06createB0AA13UploadRequestC0B0OyKF": {"name": "createUploadable()", "parent_name": "Uploadable"}, "Classes/UploadRequest/Uploadable.html": {"name": "Uploadable", "abstract": "<p>Type describing the origin of the upload, whether <code>Data</code>, file, or stream.</p>", "parent_name": "UploadRequest"}, "Classes/UploadRequest.html#/s:9Alamofire13UploadRequestC6uploadAA21UploadableConvertible_pvp": {"name": "upload", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/UploadableConvertible.html\">UploadableConvertible</a></code> value used to produce the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest/Uploadable.html\">Uploadable</a></code> value for this instance.</p>", "parent_name": "UploadRequest"}, "Classes/UploadRequest.html#/s:9Alamofire13UploadRequestC11fileManagerSo06NSFileE0Cvp": {"name": "fileManager", "abstract": "<p><code>FileManager</code> used to perform cleanup tasks, including the removal of multipart form encoded payloads written", "parent_name": "UploadRequest"}, "Classes/UploadRequest.html#/s:9Alamofire13UploadRequestC10uploadableAC10UploadableOSgvp": {"name": "uploadable", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest/Uploadable.html\">Uploadable</a></code> value used by the instance.</p>", "parent_name": "UploadRequest"}, "Classes/UploadRequest.html#/s:9Alamofire13UploadRequestC7cleanupyyF": {"name": "cleanup()", "parent_name": "UploadRequest"}, "Classes/SessionDelegate.html#/s:9Alamofire15SessionDelegateC11fileManagerACSo06NSFileE0C_tcfc": {"name": "init(fileManager:)", "abstract": "<p>Creates an instance from the given <code>FileManager</code>.</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:didBecomeInvalidWithError:": {"name": "urlSession(_:didBecomeInvalidWithError:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:task:didReceiveChallenge:completionHandler:": {"name": "urlSession(_:task:didReceive:completionHandler:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:": {"name": "urlSession(_:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:task:needNewBodyStream:": {"name": "urlSession(_:task:needNewBodyStream:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:task:willPerformHTTPRedirection:newRequest:completionHandler:": {"name": "urlSession(_:task:willPerformHTTPRedirection:newRequest:completionHandler:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:task:didFinishCollectingMetrics:": {"name": "urlSession(_:task:didFinishCollecting:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:task:didCompleteWithError:": {"name": "urlSession(_:task:didCompleteWithError:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:taskIsWaitingForConnectivity:": {"name": "urlSession(_:taskIsWaitingForConnectivity:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:dataTask:didReceiveResponse:completionHandler:": {"name": "urlSession(_:dataTask:didReceive:completionHandler:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:dataTask:didReceiveData:": {"name": "urlSession(_:dataTask:didReceive:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:dataTask:willCacheResponse:completionHandler:": {"name": "urlSession(_:dataTask:willCacheResponse:completionHandler:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:webSocketTask:didOpenWithProtocol:": {"name": "urlSession(_:webSocketTask:didOpenWithProtocol:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:webSocketTask:didCloseWithCode:reason:": {"name": "urlSession(_:webSocketTask:didCloseWith:reason:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:downloadTask:didResumeAtOffset:expectedTotalBytes:": {"name": "urlSession(_:downloadTask:didResumeAtOffset:expectedTotalBytes:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:": {"name": "urlSession(_:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/SessionDelegate.html#/c:@CM@Alamofire@objc(cs)SessionDelegate(im)URLSession:downloadTask:didFinishDownloadingToURL:": {"name": "urlSession(_:downloadTask:didFinishDownloadingTo:)", "abstract": "<p>Undocumented</p>", "parent_name": "SessionDelegate"}, "Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ": {"name": "default", "abstract": "<p>Shared singleton instance used by all <code>AF.request</code> APIs. Cannot be modified.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC7sessionSo12NSURLSessionCvp": {"name": "session", "abstract": "<p>Underlying <code>URLSession</code> used to create <code>URLSessionTasks</code> for this instance, and for which this instance&rsquo;s", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC8delegateAA0B8DelegateCvp": {"name": "delegate", "abstract": "<p>Instance&rsquo;s <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/SessionDelegate.html\">SessionDelegate</a></code>, which handles the <code>URLSessionDelegate</code> methods and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> interaction.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp": {"name": "rootQueue", "abstract": "<p>Root <code>DispatchQueue</code> for all internal callbacks and state update. <strong>MUST</strong> be a serial queue.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC24startRequestsImmediatelySbvp": {"name": "startRequestsImmediately", "abstract": "<p>Value determining whether this instance automatically calls <code>resume()</code> on all created <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>s.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC12requestQueueSo17OS_dispatch_queueCvp": {"name": "requestQueue", "abstract": "<p><code>DispatchQueue</code> on which <code>URLRequest</code>s are created asynchronously. By default this queue uses <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp\">rootQueue</a></code> as its", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC18serializationQueueSo17OS_dispatch_queueCvp": {"name": "serializationQueue", "abstract": "<p><code>DispatchQueue</code> passed to all <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>s on which they perform their response serialization. By default this", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC11interceptorAA18RequestInterceptor_pSgvp": {"name": "interceptor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code> used for all <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> created by the instance. <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>s can also be set on a", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC18serverTrustManagerAA06ServerdE0CSgvp": {"name": "serverTrustManager", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/ServerTrustManager.html\">ServerTrustManager</a></code> instance used to evaluate all trust challenges and provide certificate and key pinning.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC15redirectHandlerAA08RedirectD0_pSgvp": {"name": "redirectHandler", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RedirectHandler.html\">RedirectHandler</a></code> instance used to provide customization for request redirection.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC21cachedResponseHandlerAA06CacheddE0_pSgvp": {"name": "cachedResponseHandler", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/CachedResponseHandler.html\">CachedResponseHandler</a></code> instance used to provide customization of cached response handling.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC12eventMonitorAA014CompositeEventD0Cvp": {"name": "eventMonitor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/CompositeEventMonitor.html\">CompositeEventMonitor</a></code> used to compose any passed <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html\">EventMonitor</a></code>s.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC20defaultEventMonitorsSayAA0D7Monitor_pGvp": {"name": "defaultEventMonitors", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html\">EventMonitor</a></code>s included in all instances unless overwritten. <code>[AlamofireNotifications()]</code> by default.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC7session8delegate9rootQueue24startRequestsImmediately07requestF0013serializationF011interceptor18serverTrustManager15redirectHandler014cachedResponseQ013eventMonitorsACSo12NSURLSessionC_AA0B8DelegateCSo17OS_dispatch_queueCSbATSgAuA18RequestInterceptor_pSgAA06ServernO0CSgAA08RedirectQ0_pSgAA06CachedsQ0_pSgSayAA12EventMonitor_pGtcfc": {"name": "init(session:delegate:rootQueue:startRequestsImmediately:requestQueue:serializationQueue:interceptor:serverTrustManager:redirectHandler:cachedResponseHandler:eventMonitors:)", "abstract": "<p>Creates a <code>Session</code> from a <code>URLSession</code> and other parameters.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC13configuration8delegate9rootQueue24startRequestsImmediately07requestF0013serializationF011interceptor18serverTrustManager15redirectHandler014cachedResponseQ013eventMonitorsACSo25NSURLSessionConfigurationC_AA0B8DelegateCSo17OS_dispatch_queueCSbATSgAuA18RequestInterceptor_pSgAA06ServernO0CSgAA08RedirectQ0_pSgAA06CachedsQ0_pSgSayAA12EventMonitor_pGtcfc": {"name": "init(configuration:delegate:rootQueue:startRequestsImmediately:requestQueue:serializationQueue:interceptor:serverTrustManager:redirectHandler:cachedResponseHandler:eventMonitors:)", "abstract": "<p>Creates a <code>Session</code> from a <code>URLSessionConfiguration</code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC15withAllRequests7performyyShyAA7RequestCGYbc_tF": {"name": "withAllRequests(perform:)", "abstract": "<p>Perform an action on all active <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>s.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC17cancelAllRequests17completingOnQueue10completionySo17OS_dispatch_queueC_yyYbcSgtF": {"name": "cancelAllRequests(completingOnQueue:completion:)", "abstract": "<p>Cancel all active <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>s, optionally calling a completion handler when complete.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera": {"name": "RequestModifier", "abstract": "<p>Closure which provides a <code>URLRequest</code> for mutation.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC7request_6method10parameters8encoding7headers11interceptor0C8ModifierAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSs8Sendable_pGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0K11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtF": {"name": "request(_:method:parameters:encoding:headers:interceptor:requestModifier:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> from a <code>URLRequest</code> created using the passed components and a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC7request_6method10parameters7encoder7headers11interceptor0C8ModifierAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0K11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtSERzs8SendableRzlF": {"name": "request(_:method:parameters:encoder:headers:interceptor:requestModifier:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> from a <code>URLRequest</code> created using the passed components, <code>Encodable</code> parameters, and a", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC7request_11interceptorAA11DataRequestCAA21URLRequestConvertible_p_AA0F11Interceptor_pSgtF": {"name": "request(_:interceptor:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> from a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value and a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC13streamRequest_6method10parameters7encoder7headers32automaticallyCancelOnStreamError11interceptor15requestModifierAA04DatalD0CAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgSbAA0D11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtSERzs8SendableRzlF": {"name": "streamRequest(_:method:parameters:encoder:headers:automaticallyCancelOnStreamError:interceptor:requestModifier:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code> from the passed components, <code>Encodable</code> parameters, and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC13streamRequest_6method7headers32automaticallyCancelOnStreamError11interceptor15requestModifierAA04DatajD0CAA14URLConvertible_p_AA10HTTPMethodVAA11HTTPHeadersVSgSbAA0D11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtF": {"name": "streamRequest(_:method:headers:automaticallyCancelOnStreamError:interceptor:requestModifier:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code> from the passed components and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC13streamRequest_32automaticallyCancelOnStreamError11interceptorAA04DatahD0CAA21URLRequestConvertible_p_SbAA0D11Interceptor_pSgtF": {"name": "streamRequest(_:automaticallyCancelOnStreamError:interceptor:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code> from the passed <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC8download_6method10parameters8encoding7headers11interceptor15requestModifier2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSs8Sendable_pGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0M11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgAZ3URLV011destinationW0_AM7OptionsV7optionstA3__So17NSHTTPURLResponseCtYbcSgtF": {"name": "download(_:method:parameters:encoding:headers:interceptor:requestModifier:to:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> using a <code>URLRequest</code> created using the passed components, <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>, and", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC8download_6method10parameters7encoder7headers11interceptor15requestModifier2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0M11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgAX3URLV011destinationV0_AM7OptionsV7optionstA1__So17NSHTTPURLResponseCtYbcSgtSERzs8SendableRzlF": {"name": "download(_:method:parameters:encoder:headers:interceptor:requestModifier:to:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> from a <code>URLRequest</code> created using the passed components, <code>Encodable</code> parameters, and", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC8download_11interceptor2toAA15DownloadRequestCAA21URLRequestConvertible_p_AA0G11Interceptor_pSg10Foundation3URLV011destinationL0_AH7OptionsV7optionstAN_So17NSHTTPURLResponseCtYbcSgtF": {"name": "download(_:interceptor:to:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> from a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value, a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>, and a <code>Destination</code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC8download12resumingWith11interceptor2toAA15DownloadRequestC10Foundation4DataV_AA0I11Interceptor_pSgAJ3URLV011destinationM0_AI7OptionsV7optionstAP_So17NSHTTPURLResponseCtYbcSgtF": {"name": "download(resumingWith:interceptor:to:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code> from the <code>resumeData</code> produced from a previously cancelled <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest.html\">DownloadRequest</a></code>, as", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestC10Foundation4DataV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0CyAM10URLRequestVzYbKcSgtF": {"name": "upload(_:to:method:headers:interceptor:fileManager:requestModifier:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> for the given <code>Data</code>, <code>URLRequest</code> components, and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation4DataV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF": {"name": "upload(_:with:interceptor:fileManager:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> for the given <code>Data</code> using the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code>.</p>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestC10Foundation3URLV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0CyAM10URLRequestVzYbKcSgtF": {"name": "upload(_:to:method:headers:interceptor:fileManager:requestModifier:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> for the file at the given file <code>URL</code>, using a <code>URLRequest</code> from the provided", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation3URLV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF": {"name": "upload(_:with:interceptor:fileManager:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> for the file at the given file <code>URL</code> using the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value and", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCSo13NSInputStreamC_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0Cy10Foundation10URLRequestVzYbKcSgtF": {"name": "upload(_:to:method:headers:interceptor:fileManager:requestModifier:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> from the <code>InputStream</code> provided using a <code>URLRequest</code> from the provided components and", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestCSo13NSInputStreamC_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF": {"name": "upload(_:with:interceptor:fileManager:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> from the provided <code>InputStream</code> using the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value and", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCyAA09MultiparteF0Cc_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0R11Interceptor_pSgSo06NSFileN0Cy10Foundation10URLRequestVzYbKcSgtF": {"name": "upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:requestModifier:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> for the multipart form data built using a closure and sent using the provided", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCyAA09MultiparteF0Cc_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtF": {"name": "upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/MultipartFormData.html\">MultipartFormData</a></code> building closure, the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCAA09MultiparteF0C_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0R11Interceptor_pSgSo06NSFileN0Cy10Foundation10URLRequestVzYbKcSgtF": {"name": "upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:requestModifier:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> for the prebuilt <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/MultipartFormData.html\">MultipartFormData</a></code> value using the provided <code>URLRequest</code> components", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire7SessionC6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCAA09MultiparteF0C_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtF": {"name": "upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)", "abstract": "<p>Creates an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/UploadRequest.html\">UploadRequest</a></code> for the prebuilt <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/MultipartFormData.html\">MultipartFormData</a></code> value using the providing <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code>", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire15RequestDelegateP20sessionConfigurationSo012NSURLSessionE0Cvp": {"name": "sessionConfiguration", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire15RequestDelegateP16startImmediatelySbvp": {"name": "startImmediately", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire15RequestDelegateP7cleanup5afteryAA0B0C_tF": {"name": "cleanup(after:)", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire15RequestDelegateP11retryResult3for5dueTo10completionyAA0B0C_AA7AFErrorOyAA05RetryE0OYbctF": {"name": "retryResult(for:dueTo:completion:)", "parent_name": "Session"}, "Classes/Session.html#/s:9Alamofire15RequestDelegateP05retryB0_9withDelayyAA0B0C_SdSgtF": {"name": "retryRequest(_:withDelay:)", "parent_name": "Session"}, "Classes/URLEncodedFormParameterEncoder/Destination.html#/s:9Alamofire30URLEncodedFormParameterEncoderC11DestinationO15methodDependentyA2EmF": {"name": "methodDependent", "abstract": "<p>Applies the encoded query string to any existing query string for <code>.get</code>, <code>.head</code>, and <code>.delete</code> request.", "parent_name": "Destination"}, "Classes/URLEncodedFormParameterEncoder/Destination.html#/s:9Alamofire30URLEncodedFormParameterEncoderC11DestinationO11queryStringyA2EmF": {"name": "queryString", "abstract": "<p>Applies the encoded query string to any existing query string from the <code>URLRequest</code>.</p>", "parent_name": "Destination"}, "Classes/URLEncodedFormParameterEncoder/Destination.html#/s:9Alamofire30URLEncodedFormParameterEncoderC11DestinationO8httpBodyyA2EmF": {"name": "httpBody", "abstract": "<p>Applies the encoded query string to the <code>httpBody</code> of the <code>URLRequest</code>.</p>", "parent_name": "Destination"}, "Classes/URLEncodedFormParameterEncoder/Destination.html": {"name": "Destination", "abstract": "<p>Defines where the URL-encoded string should be set for each <code>URLRequest</code>.</p>", "parent_name": "URLEncodedFormParameterEncoder"}, "Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7defaultACvpZ": {"name": "default", "abstract": "<p>Returns an encoder with default parameters.</p>", "parent_name": "URLEncodedFormParameterEncoder"}, "Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7encoderAA0bcE0Cvp": {"name": "encoder", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder.html\">URLEncodedFormEncoder</a></code> to use.</p>", "parent_name": "URLEncodedFormParameterEncoder"}, "Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC11destinationAC11DestinationOvp": {"name": "destination", "abstract": "<p>The <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormParameterEncoder/Destination.html\">Destination</a></code> for the URL-encoded string.</p>", "parent_name": "URLEncodedFormParameterEncoder"}, "Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7encoder11destinationAcA0bcE0C_AC11DestinationOtcfc": {"name": "init(encoder:destination:)", "abstract": "<p>Creates an instance with the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormEncoder.html\">URLEncodedFormEncoder</a></code> instance and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/URLEncodedFormParameterEncoder/Destination.html\">Destination</a></code> value.</p>", "parent_name": "URLEncodedFormParameterEncoder"}, "Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire16ParameterEncoderP6encode_4into10Foundation10URLRequestVqd__Sg_AHtKSERd__s8SendableRd__lF": {"name": "encode(_:into:)", "parent_name": "URLEncodedFormParameterEncoder"}, "Classes/JSONParameterEncoder.html#/s:9Alamofire20JSONParameterEncoderC7defaultACvpZ": {"name": "default", "abstract": "<p>Returns an encoder with default parameters.</p>", "parent_name": "JSONParameterEncoder"}, "Classes/JSONParameterEncoder.html#/s:9Alamofire20JSONParameterEncoderC13prettyPrintedACvpZ": {"name": "prettyPrinted", "abstract": "<p>Returns an encoder with <code>JSONEncoder.outputFormatting</code> set to <code>.prettyPrinted</code>.</p>", "parent_name": "JSONParameterEncoder"}, "Classes/JSONParameterEncoder.html#/s:9Alamofire20JSONParameterEncoderC10sortedKeysACvpZ": {"name": "sortedKeys", "abstract": "<p>Returns an encoder with <code>JSONEncoder.outputFormatting</code> set to <code>.sortedKeys</code>.</p>", "parent_name": "JSONParameterEncoder"}, "Classes/JSONParameterEncoder.html#/s:9Alamofire20JSONParameterEncoderC7encoder10Foundation11JSONEncoderCvp": {"name": "encoder", "abstract": "<p><code>JSONEncoder</code> used to encode parameters.</p>", "parent_name": "JSONParameterEncoder"}, "Classes/JSONParameterEncoder.html#/s:9Alamofire20JSONParameterEncoderC7encoderAC10Foundation11JSONEncoderC_tcfc": {"name": "init(encoder:)", "abstract": "<p>Creates an instance with the provided <code>JSONEncoder</code>.</p>", "parent_name": "JSONParameterEncoder"}, "Classes/JSONParameterEncoder.html#/s:9Alamofire16ParameterEncoderP6encode_4into10Foundation10URLRequestVqd__Sg_AHtKSERd__s8SendableRd__lF": {"name": "encode(_:into:)", "parent_name": "JSONParameterEncoder"}, "Classes/AlamofireNotifications.html#/s:9Alamofire0A13NotificationsCACycfc": {"name": "init()", "abstract": "<p>Creates an instance.</p>", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP16requestDidResumeyyAA7RequestCF": {"name": "requestDidResume(_:)", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP17requestDidSuspendyyAA7RequestCF": {"name": "requestDidSuspend(_:)", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP16requestDidCancelyyAA7RequestCF": {"name": "requestDidCancel(_:)", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP16requestDidFinishyyAA7RequestCF": {"name": "requestDidFinish(_:)", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP7request_13didResumeTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didResumeTask:)", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP7request_14didSuspendTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didSuspendTask:)", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP7request_13didCancelTaskyAA7RequestC_So012NSURLSessionG0CtF": {"name": "request(_:didCancelTask:)", "parent_name": "AlamofireNotifications"}, "Classes/AlamofireNotifications.html#/s:9Alamofire12EventMonitorP7request_15didCompleteTask4withyAA7RequestC_So012NSURLSessionG0CAA7AFErrorOSgtF": {"name": "request(_:didCompleteTask:with:)", "parent_name": "AlamofireNotifications"}, "Classes/Request/ResponseDisposition.html#/s:9Alamofire7RequestC19ResponseDispositionO5allowyA2EmF": {"name": "allow", "abstract": "<p>Allow the request to continue normally.</p>", "parent_name": "ResponseDisposition"}, "Classes/Request/ResponseDisposition.html#/s:9Alamofire7RequestC19ResponseDispositionO6cancelyA2EmF": {"name": "cancel", "abstract": "<p>Cancel the request, similar to calling <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC6cancelACXDyF\">cancel()</a></code>.</p>", "parent_name": "ResponseDisposition"}, "Classes/Request/State.html#/s:9Alamofire7RequestC5StateO11initializedyA2EmF": {"name": "initialized", "abstract": "<p>Initial state of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>.</p>", "parent_name": "State"}, "Classes/Request/State.html#/s:9Alamofire7RequestC5StateO7resumedyA2EmF": {"name": "resumed", "abstract": "<p><code>State</code> set when <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC6resumeACXDyF\">resume()</a></code> is called. Any tasks created for the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> will have <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC6resumeACXDyF\">resume()</a></code> called on", "parent_name": "State"}, "Classes/Request/State.html#/s:9Alamofire7RequestC5StateO9suspendedyA2EmF": {"name": "suspended", "abstract": "<p><code>State</code> set when <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC7suspendACXDyF\">suspend()</a></code> is called. Any tasks created for the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> will have <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC7suspendACXDyF\">suspend()</a></code> called on", "parent_name": "State"}, "Classes/Request/State.html#/s:9Alamofire7RequestC5StateO9cancelledyA2EmF": {"name": "cancelled", "abstract": "<p><code>State</code> set when <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC6cancelACXDyF\">cancel()</a></code> is called. Any tasks created for the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> will have <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC6cancelACXDyF\">cancel()</a></code> called on", "parent_name": "State"}, "Classes/Request/State.html#/s:9Alamofire7RequestC5StateO8finishedyA2EmF": {"name": "finished", "abstract": "<p><code>State</code> set when all response serialization completion closures have been cleared on the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> and", "parent_name": "State"}, "Classes/Request/State.html": {"name": "State", "abstract": "<p>State of the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code>, with managed transitions between states set when calling <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC6resumeACXDyF\">resume()</a></code>, <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC7suspendACXDyF\">suspend()</a></code>, or", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC2id10Foundation4UUIDVvp": {"name": "id", "abstract": "<p><code>UUID</code> providing a unique identifier for the <code>Request</code>, used in the <code>Hashable</code> and <code>Equatable</code> conformances.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC15underlyingQueueSo17OS_dispatch_queueCvp": {"name": "underlyingQueue", "abstract": "<p>The serial queue for all internal async actions.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC18serializationQueueSo17OS_dispatch_queueCvp": {"name": "serializationQueue", "abstract": "<p>The queue used for all serialization actions. By default it&rsquo;s a serial queue that targets <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC15underlyingQueueSo17OS_dispatch_queueCvp\">underlyingQueue</a></code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC12eventMonitorAA05EventD0_pSgvp": {"name": "eventMonitor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html\">EventMonitor</a></code> used for event callbacks.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC11interceptorAA0B11Interceptor_pSgvp": {"name": "interceptor", "abstract": "<p>The <code>Request</code>&lsquo;s interceptor.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC8delegateAA0B8Delegate_pSgvp": {"name": "delegate", "abstract": "<p>The <code>Request</code>&lsquo;s delegate.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC5stateAC5StateOvp": {"name": "state", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request/State.html\">State</a></code> of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC13isInitializedSbvp": {"name": "isInitialized", "abstract": "<p>Returns whether <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC5stateAC5StateOvp\">state</a></code> is <code>.initialized</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC9isResumedSbvp": {"name": "isResumed", "abstract": "<p>Returns whether <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC5stateAC5StateOvp\">state</a></code> is <code>.resumed</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC11isSuspendedSbvp": {"name": "isSuspended", "abstract": "<p>Returns whether <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC5stateAC5StateOvp\">state</a></code> is <code>.suspended</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC11isCancelledSbvp": {"name": "isCancelled", "abstract": "<p>Returns whether <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC5stateAC5StateOvp\">state</a></code> is <code>.cancelled</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC10isFinishedSbvp": {"name": "isFinished", "abstract": "<p>Returns whether <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC5stateAC5StateOvp\">state</a></code> is <code>.finished</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC15ProgressHandlera": {"name": "ProgressHandler", "abstract": "<p>Closure type executed when monitoring the upload or download progress of a request.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC14uploadProgressSo10NSProgressCvp": {"name": "uploadProgress", "abstract": "<p><code>Progress</code> of the upload of the body of the executed <code>URLRequest</code>. Reset to <code>0</code> if the <code>Request</code> is retried.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC16downloadProgressSo10NSProgressCvp": {"name": "downloadProgress", "abstract": "<p><code>Progress</code> of the download of any response data. Reset to <code>0</code> if the <code>Request</code> is retried.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC21uploadProgressHandlerySo10NSProgressCYbc7handler_So17OS_dispatch_queueC0J0tSgvp": {"name": "uploadProgressHandler", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC15ProgressHandlera\">ProgressHandler</a></code> called when <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC14uploadProgressSo10NSProgressCvp\">uploadProgress</a></code> is updated, on the provided <code>DispatchQueue</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC23downloadProgressHandlerySo10NSProgressCYbc7handler_So17OS_dispatch_queueC0J0tSgvp": {"name": "downloadProgressHandler", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC15ProgressHandlera\">ProgressHandler</a></code> called when <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC16downloadProgressSo10NSProgressCvp\">downloadProgress</a></code> is updated, on the provided <code>DispatchQueue</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC15redirectHandlerAA08RedirectD0_pSgvp": {"name": "redirectHandler", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RedirectHandler.html\">RedirectHandler</a></code> set on the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC21cachedResponseHandlerAA06CacheddE0_pSgvp": {"name": "cachedResponseHandler", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/CachedResponseHandler.html\">CachedResponseHandler</a></code> set on the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC10credentialSo15NSURLCredentialCSgvp": {"name": "credential", "abstract": "<p><code>URLCredential</code> used for authentication challenges. Created by calling one of the <code>authenticate</code> methods.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC8requestsSay10Foundation10URLRequestVGvp": {"name": "requests", "abstract": "<p>All <code>URLRequest</code>s created on behalf of the <code>Request</code>, including original and adapted requests.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC05firstB010Foundation10URLRequestVSgvp": {"name": "firstRequest", "abstract": "<p>First <code>URLRequest</code> created on behalf of the <code>Request</code>. May not be the first one actually executed.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC04lastB010Foundation10URLRequestVSgvp": {"name": "lastRequest", "abstract": "<p>Last <code>URLRequest</code> created on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC7request10Foundation10URLRequestVSgvp": {"name": "request", "abstract": "<p>Current <code>URLRequest</code> created on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC17performedRequestsSay10Foundation10URLRequestVGvp": {"name": "performedRequests", "abstract": "<p><code>URLRequest</code>s from all of the <code>URLSessionTask</code>s executed on behalf of the <code>Request</code>. May be different from", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC8responseSo17NSHTTPURLResponseCSgvp": {"name": "response", "abstract": "<p><code>HTTPURLResponse</code> received from the server, if any. If the <code>Request</code> was retried, this is the response of the", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC5tasksSaySo16NSURLSessionTaskCGvp": {"name": "tasks", "abstract": "<p>All <code>URLSessionTask</code>s created on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC9firstTaskSo012NSURLSessionD0CSgvp": {"name": "firstTask", "abstract": "<p>First <code>URLSessionTask</code> created on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC8lastTaskSo012NSURLSessionD0CSgvp": {"name": "lastTask", "abstract": "<p>Last <code>URLSessionTask</code> created on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC4taskSo16NSURLSessionTaskCSgvp": {"name": "task", "abstract": "<p>Current <code>URLSessionTask</code> created on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC10allMetricsSaySo016NSURLSessionTaskD0CGvp": {"name": "allMetrics", "abstract": "<p>All <code>URLSessionTaskMetrics</code> gathered on behalf of the <code>Request</code>. Should correspond to the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html#/s:9Alamofire7RequestC5tasksSaySo16NSURLSessionTaskCGvp\">tasks</a></code> created.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC12firstMetricsSo016NSURLSessionTaskD0CSgvp": {"name": "firstMetrics", "abstract": "<p>First <code>URLSessionTaskMetrics</code> gathered on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC11lastMetricsSo016NSURLSessionTaskD0CSgvp": {"name": "lastMetrics", "abstract": "<p>Last <code>URLSessionTaskMetrics</code> gathered on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC7metricsSo23NSURLSessionTaskMetricsCSgvp": {"name": "metrics", "abstract": "<p>Current <code>URLSessionTaskMetrics</code> gathered on behalf of the <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC10retryCountSivp": {"name": "retryCount", "abstract": "<p>Number of times the <code>Request</code> has been retried.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC5errorAA7AFErrorOSgvp": {"name": "error", "abstract": "<p><code>Error</code> returned from Alamofire internally, from the network request directly, or any validators executed.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC6cancelACXDyF": {"name": "cancel()", "abstract": "<p>Cancels the instance. Once cancelled, a <code>Request</code> can no longer be resumed or suspended.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC7suspendACXDyF": {"name": "suspend()", "abstract": "<p>Suspends the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC6resumeACXDyF": {"name": "resume()", "abstract": "<p>Resumes the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC12authenticate8username8password11persistenceACXDSS_SSSo26NSURLCredentialPersistenceVtF": {"name": "authenticate(username:password:persistence:)", "abstract": "<p>Associates a credential using the provided values with the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC12authenticate4withACXDSo15NSURLCredentialC_tF": {"name": "authenticate(with:)", "abstract": "<p>Associates the provided credential with the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC16downloadProgress5queue7closureACXDSo012OS_dispatch_E0C_ySo10NSProgressCctF": {"name": "downloadProgress(queue:closure:)", "abstract": "<p>Sets a closure to be called periodically during the lifecycle of the instance as data is read from the server.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC14uploadProgress5queue7closureACXDSo012OS_dispatch_E0C_ySo10NSProgressCctF": {"name": "uploadProgress(queue:closure:)", "abstract": "<p>Sets a closure to be called periodically during the lifecycle of the instance as data is sent to the server.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC8redirect5usingACXDAA15RedirectHandler_p_tF": {"name": "redirect(using:)", "abstract": "<p>Sets the redirect handler for the instance which will be used if a redirect response is encountered.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC13cacheResponse5usingACXDAA06CachedD7Handler_p_tF": {"name": "cacheResponse(using:)", "abstract": "<p>Sets the cached response handler for the <code>Request</code> which will be used when attempting to cache a response.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC15cURLDescription2on7callingACXDSo17OS_dispatch_queueC_ySSctF": {"name": "cURLDescription(on:calling:)", "abstract": "<p>Sets a handler to be called when the cURL description of the request is available.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC15cURLDescription7callingACXDySSc_tF": {"name": "cURLDescription(calling:)", "abstract": "<p>Sets a handler to be called when the cURL description of the request is available.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC20onURLRequestCreation0C07performACXDSo17OS_dispatch_queueC_y10Foundation0D0VctF": {"name": "onURLRequestCreation(on:perform:)", "abstract": "<p>Sets a closure to called whenever Alamofire creates a <code>URLRequest</code> for this instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC24onURLSessionTaskCreation0C07performACXDSo17OS_dispatch_queueC_ySo012NSURLSessionE0CctF": {"name": "onURLSessionTaskCreation(on:perform:)", "abstract": "<p>Sets a closure to be called whenever the instance creates a <code>URLSessionTask</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC21didResumeNotificationSo18NSNotificationNameavpZ": {"name": "didResumeNotification", "abstract": "<p>Posted when a <code>Request</code> is resumed. The <code>Notification</code> contains the resumed <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC22didSuspendNotificationSo18NSNotificationNameavpZ": {"name": "didSuspendNotification", "abstract": "<p>Posted when a <code>Request</code> is suspended. The <code>Notification</code> contains the suspended <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC21didCancelNotificationSo18NSNotificationNameavpZ": {"name": "didCancelNotification", "abstract": "<p>Posted when a <code>Request</code> is cancelled. The <code>Notification</code> contains the cancelled <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC21didFinishNotificationSo18NSNotificationNameavpZ": {"name": "didFinishNotification", "abstract": "<p>Posted when a <code>Request</code> is finished. The <code>Notification</code> contains the completed <code>Request</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC25didResumeTaskNotificationSo18NSNotificationNameavpZ": {"name": "didResumeTaskNotification", "abstract": "<p>Posted when a <code>URLSessionTask</code> is resumed. The <code>Notification</code> contains the <code>Request</code> associated with the <code>URLSessionTask</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC26didSuspendTaskNotificationSo18NSNotificationNameavpZ": {"name": "didSuspendTaskNotification", "abstract": "<p>Posted when a <code>URLSessionTask</code> is suspended. The <code>Notification</code> contains the <code>Request</code> associated with the <code>URLSessionTask</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC25didCancelTaskNotificationSo18NSNotificationNameavpZ": {"name": "didCancelTaskNotification", "abstract": "<p>Posted when a <code>URLSessionTask</code> is cancelled. The <code>Notification</code> contains the <code>Request</code> associated with the <code>URLSessionTask</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC27didCompleteTaskNotificationSo18NSNotificationNameavpZ": {"name": "didCompleteTaskNotification", "abstract": "<p>Posted when a <code>URLSessionTask</code> is completed. The <code>Notification</code> contains the <code>Request</code> associated with the <code>URLSessionTask</code>.</p>", "parent_name": "Request"}, "Classes/Request/ResponseDisposition.html": {"name": "ResponseDisposition", "abstract": "<p>Type indicating how a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> or <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code> should proceed after receiving an <code>HTTPURLResponse</code>.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:SQ2eeoiySbx_xtFZ": {"name": "==(_:_:)", "parent_name": "Request"}, "Classes/Request.html#/s:SH4hash4intoys6HasherVz_tF": {"name": "hash(into:)", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC11descriptionSSvp": {"name": "description", "abstract": "<p>A textual representation of this instance, including the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/HTTPMethod.html\">HTTPMethod</a></code> and <code>URL</code> if the <code>URLRequest</code> has been", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC15cURLDescriptionSSyF": {"name": "cURLDescription()", "abstract": "<p>cURL representation of the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC14uploadProgress15bufferingPolicyAA8StreamOfVySo10NSProgressCGScS12ContinuationV09BufferingF0OyAI__G_tF": {"name": "uploadProgress(bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StreamOf.html\">StreamOf&lt;Progress&gt;</a></code> for the instance&rsquo;s upload progress.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC16downloadProgress15bufferingPolicyAA8StreamOfVySo10NSProgressCGScS12ContinuationV09BufferingF0OyAI__G_tF": {"name": "downloadProgress(bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StreamOf.html\">StreamOf&lt;Progress&gt;</a></code> for the instance&rsquo;s download progress.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC11urlRequests15bufferingPolicyAA8StreamOfVy10Foundation10URLRequestVGScS12ContinuationV09BufferingF0OyAJ__G_tF": {"name": "urlRequests(bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StreamOf.html\">StreamOf&lt;URLRequest&gt;</a></code> for the <code>URLRequest</code>s produced for the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC15urlSessionTasks15bufferingPolicyAA8StreamOfVySo16NSURLSessionTaskCGScS12ContinuationV09BufferingG0OyAI__G_tF": {"name": "urlSessionTasks(bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StreamOf.html\">StreamOf&lt;URLSessionTask&gt;</a></code> for the <code>URLSessionTask</code>s produced for the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC16cURLDescriptions15bufferingPolicyAA8StreamOfVySSGScS12ContinuationV09BufferingE0OySS__G_tF": {"name": "cURLDescriptions(bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StreamOf.html\">StreamOf&lt;String&gt;</a></code> for the cURL descriptions produced for the instance.</p>", "parent_name": "Request"}, "Classes/Request.html#/s:9Alamofire7RequestC16ValidationResulta": {"name": "ValidationResult", "abstract": "<p>Used to represent whether a validation succeeded or failed.</p>", "parent_name": "Request"}, "Classes/DownloadRequest/Downloadable.html#/s:9Alamofire15DownloadRequestC12DownloadableO7requestyAeA21URLRequestConvertible_pcAEmF": {"name": "request(_:)", "abstract": "<p>Download should be started from the <code>URLRequest</code> produced by the associated <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value.</p>", "parent_name": "Downloadable"}, "Classes/DownloadRequest/Downloadable.html#/s:9Alamofire15DownloadRequestC12DownloadableO10resumeDatayAE10Foundation0F0VcAEmF": {"name": "resumeData(_:)", "abstract": "<p>Download should be started from the associated resume <code>Data</code> value.</p>", "parent_name": "Downloadable"}, "Classes/DownloadRequest/Options.html#/s:9Alamofire15DownloadRequestC7OptionsV29createIntermediateDirectoriesAEvpZ": {"name": "createIntermediateDirectories", "abstract": "<p>Specifies that intermediate directories for the destination URL should be created.</p>", "parent_name": "Options"}, "Classes/DownloadRequest/Options.html#/s:9Alamofire15DownloadRequestC7OptionsV18removePreviousFileAEvpZ": {"name": "removePreviousFile", "abstract": "<p>Specifies that any previous file at the destination <code>URL</code> should be removed.</p>", "parent_name": "Options"}, "Classes/DownloadRequest/Options.html#/s:SY8rawValue03RawB0Qzvp": {"name": "rawValue", "parent_name": "Options"}, "Classes/DownloadRequest/Options.html#/s:s9OptionSetP8rawValuex03RawD0Qz_tcfc": {"name": "init(rawValue:)", "parent_name": "Options"}, "Classes/DownloadRequest/Options.html": {"name": "Options", "abstract": "<p>A set of options to be executed prior to moving a downloaded file from the temporary <code>URL</code> to the destination", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa": {"name": "Destination", "abstract": "<p>A closure executed once a <code>DownloadRequest</code> has successfully completed in order to determine where to move the", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC09suggestedB11Destination3for2in7options10Foundation3URLV011destinationJ0_AC7OptionsVAGtAJ_So17NSHTTPURLResponseCtYbcSo21NSSearchPathDirectoryV_So0nO10DomainMaskVAMtFZ": {"name": "suggestedDownloadDestination(for:in:options:)", "abstract": "<p>Creates a download file destination closure which uses the default file manager to move the temporary file to a", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest/Downloadable.html": {"name": "Downloadable", "abstract": "<p>Type describing the source used to create the underlying <code>URLSessionDownloadTask</code>.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC10resumeData10Foundation0E0VSgvp": {"name": "resumeData", "abstract": "<p>If the download is resumable and is eventually cancelled or fails, this value may be used to resume the download", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC7fileURL10Foundation0E0VSgvp": {"name": "fileURL", "abstract": "<p>If the download is successful, the <code>URL</code> where the file was downloaded.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC12downloadableAC12DownloadableOvp": {"name": "downloadable", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DownloadRequest/Downloadable.html\">Downloadable</a></code> value used for this instance.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC4task13forResumeData5usingSo16NSURLSessionTaskC10Foundation0G0V_So0I0CtF": {"name": "task(forResumeData:using:)", "abstract": "<p>Creates a <code>URLSessionTask</code> from the provided resume data.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC6cancelACXDyF": {"name": "cancel()", "abstract": "<p>Cancels the instance. Once cancelled, a <code>DownloadRequest</code> can no longer be resumed or suspended.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC6cancel19producingResumeDataACXDSb_tF": {"name": "cancel(producingResumeData:)", "abstract": "<p>Cancels the instance, optionally producing resume data. Once cancelled, a <code>DownloadRequest</code> can no longer be", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC6cancel21byProducingResumeDataACXDy10Foundation0H0VSgc_tF": {"name": "cancel(byProducingResumeData:)", "abstract": "<p>Cancels the instance while producing resume data. Once cancelled, a <code>DownloadRequest</code> can no longer be resumed", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCAI3URLVSgtYbcF": {"name": "validate(_:)", "abstract": "<p>Validates the request, using the specified closure.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC8response5queue17completionHandlerACXDSo012OS_dispatch_E0C_yAA0B8ResponseVy10Foundation3URLVSgAA7AFErrorOGctF": {"name": "response(queue:completion<PERSON>andler:)", "abstract": "<p>Adds a handler to be called once the request has finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGYbctAA0bkF8ProtocolRzlF": {"name": "response(queue:responseSerializer:completionHandler:)", "abstract": "<p>Adds a handler to be called once the request has finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGYbctAA0kF0RzlF": {"name": "response(queue:responseSerializer:completionHandler:)", "abstract": "<p>Adds a handler to be called once the request has finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11responseURL5queue17completionHandlerACXDSo012OS_dispatch_F0C_yAA0B8ResponseVy10Foundation0E0VAA7AFErrorOGctF": {"name": "responseURL(queue:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/URLResponseSerializer.html\">URLResponseSerializer</a></code> to be called once the request is finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC12responseData5queue16dataPreprocessor18emptyResponseCodes0iC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA0eH0_pShySiGShyAA10HTTPMethodVGyAA0bJ0Vy10Foundation0E0VAA7AFErrorOGctF": {"name": "responseData(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataResponseSerializer.html\">DataResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC14responseString5queue16dataPreprocessor8encoding18emptyResponseCodes0jC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA04DataH0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGyAA0bK0VySSAA7AFErrorOGctF": {"name": "responseString(queue:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/StringResponseSerializer.html\">StringResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC12responseJSON5queue16dataPreprocessor18emptyResponseCodes0iC7Methods7options17completionHandlerACXDSo012OS_dispatch_F0C_AA04DataH0_pShySiGShyAA10HTTPMethodVGSo20NSJSONReadingOptionsVyAA0bJ0VyypAA7AFErrorOGctF": {"name": "responseJSON(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:options:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/JSONResponseSerializer.html\">JSONResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC17responseDecodable2of5queue16dataPreprocessor7decoder18emptyResponseCodes0kC7Methods17completionHandlerACXDxm_So012OS_dispatch_G0CAA04DataI0_pAA0S7Decoder_pShySiGShyAA10HTTPMethodVGyAA0bL0VyxAA7AFErrorOGctSeRzlF": {"name": "responseDecodable(of:queue:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DecodableResponseSerializer.html\">DecodableResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0E10SerializerRz16SerializedObjectQzRs_r0_lF": {"name": "publishResponse(using:on:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponsePublisher.html\">DownloadResponsePublisher</a></code> for this instance using the given <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> and <code>DispatchQueue</code>.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0bE18SerializerProtocolRz16SerializedObjectQzRs_r0_lF": {"name": "publishResponse(using:on:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponsePublisher.html\">DownloadResponsePublisher</a></code> for this instance using the given <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DownloadResponseSerializerProtocol.html\">DownloadResponseSerializerProtocol</a></code> and", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC10publishURL5queueAA0B17ResponsePublisherVy10Foundation0E0VGSo012OS_dispatch_F0C_tF": {"name": "publishURL(queue:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponsePublisher.html\">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/URLResponseSerializer.html\">URLResponseSerializer</a></code> to serialize the", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11publishData5queue12preprocessor18emptyResponseCodes0hC7MethodsAA0bI9PublisherVy10Foundation0E0VGSo012OS_dispatch_F0C_AA0E12Preprocessor_pShySiGShyAA10HTTPMethodVGtF": {"name": "publishData(queue:preprocessor:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponsePublisher.html\">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataResponseSerializer.html\">DataResponseSerializer</a></code> to serialize the", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC13publishString5queue12preprocessor8encoding18emptyResponseCodes0iC7MethodsAA0bJ9PublisherVySSGSo012OS_dispatch_F0C_AA16DataPreprocessor_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF": {"name": "publishString(queue:preprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponsePublisher.html\">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/StringResponseSerializer.html\">StringResponseSerializer</a></code> to serialize the", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jK7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA16DataPreprocessor_pAA0Q7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF": {"name": "publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyResponseMethods:)", "abstract": "<p>Undocumented</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jC7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA16DataPreprocessor_pAA0Q7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF": {"name": "publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponsePublisher.html\">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DecodableResponseSerializer.html\">DecodableResponseSerializer</a></code> to serialize", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC19publishUnserialized2onAA0B17ResponsePublisherVy10Foundation3URLVSgGSo17OS_dispatch_queueC_tF": {"name": "publishUnserialized(on:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadResponsePublisher.html\">DownloadResponsePublisher</a></code> for this instance which does not serialize the response before publishing.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC15serializingData23automaticallyCancelling16dataPreprocessor18emptyResponseCodes0jC7MethodsAA0B4TaskVy10Foundation0E0VGSb_AA0eI0_pShySiGShyAA10HTTPMethodVGtF": {"name": "serializingData(automaticallyCancelling:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html\">DownloadTask</a></code> to <code>await</code> a <code>Data</code> value.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC20serializingDecodable_23automaticallyCancelling16dataPreprocessor7decoder18emptyResponseCodes0kC7MethodsAA0B4TaskVyxGxm_SbAA04DataI0_pAA0P7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF": {"name": "serializingDecodable(_:automaticallyCancelling:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html\">DownloadTask</a></code> to <code>await</code> serialization of a <code>Decodable</code> value.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC28serializingDownloadedFileURL23automaticallyCancellingAA0B4TaskVy10Foundation0G0VGSb_tF": {"name": "serializingDownloadedFileURL(automaticallyCancelling:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html\">DownloadTask</a></code> to <code>await</code> serialization of the downloaded file&rsquo;s <code>URL</code> on disk.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC17serializingString23automaticallyCancelling16dataPreprocessor8encoding18emptyResponseCodes0kC7MethodsAA0B4TaskVySSGSb_AA04DataI0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF": {"name": "serializingString(automaticallyCancelling:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html\">DownloadTask</a></code> to <code>await</code> serialization of a <code>String</code> value.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC011serializingB05using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA18ResponseSerializerRzlF": {"name": "serializingDownload(using:automaticallyCancelling:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html\">DownloadTask</a></code> to <code>await</code> serialization using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> instance.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC011serializingB05using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0B26ResponseSerializerProtocolRzlF": {"name": "serializingDownload(using:automaticallyCancelling:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DownloadTask.html\">DownloadTask</a></code> to <code>await</code> serialization using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DownloadResponseSerializerProtocol.html\">DownloadResponseSerializerProtocol</a></code>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC10Validationa": {"name": "Validation", "abstract": "<p>A closure used to validate a request that takes a URL request, a URL response, a temporary URL and a", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF": {"name": "validate(statusCode:)", "abstract": "<p>Validates that the response has a status code in the specified sequence.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF": {"name": "validate(contentType:)", "abstract": "<p>Validates that the response has a <code>Content-Type</code> in the specified sequence.</p>", "parent_name": "DownloadRequest"}, "Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC8validateACXDyF": {"name": "validate()", "abstract": "<p>Validates that the response has a status code in the default acceptable range of 200&hellip;299, and that the content", "parent_name": "DownloadRequest"}, "Classes/DataStreamRequest/CancellationToken.html#/s:9Alamofire17DataStreamRequestC17CancellationTokenV6cancelyyF": {"name": "cancel()", "abstract": "<p>Cancel the ongoing stream by canceling the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "CancellationToken"}, "Classes/DataStreamRequest/Completion.html#/s:9Alamofire17DataStreamRequestC10CompletionV7request10Foundation10URLRequestVSgvp": {"name": "request", "abstract": "<p>Last <code>URLRequest</code> issued by the instance.</p>", "parent_name": "Completion"}, "Classes/DataStreamRequest/Completion.html#/s:9Alamofire17DataStreamRequestC10CompletionV8responseSo17NSHTTPURLResponseCSgvp": {"name": "response", "abstract": "<p>Last <code>HTTPURLResponse</code> received by the instance.</p>", "parent_name": "Completion"}, "Classes/DataStreamRequest/Completion.html#/s:9Alamofire17DataStreamRequestC10CompletionV7metricsSo23NSURLSessionTaskMetricsCSgvp": {"name": "metrics", "abstract": "<p>Last <code>URLSessionTaskMetrics</code> produced for the instance.</p>", "parent_name": "Completion"}, "Classes/DataStreamRequest/Completion.html#/s:9Alamofire17DataStreamRequestC10CompletionV5errorAA7AFErrorOSgvp": {"name": "error", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbEnums/AFError.html\">AFError</a></code> produced for the instance, if any.</p>", "parent_name": "Completion"}, "Classes/DataStreamRequest/Event.html#/s:9Alamofire17DataStreamRequestC5EventO6streamyAEy_xq_Gs6ResultOyxq_GcAGms8SendableRzs5ErrorR_r0_lF": {"name": "stream(_:)", "abstract": "<p>Output produced every time the instance receives additional <code>Data</code>. The associated value contains the", "parent_name": "Event"}, "Classes/DataStreamRequest/Event.html#/s:9Alamofire17DataStreamRequestC5EventO8completeyAEy_xq_GAC10CompletionVcAGms8SendableRzs5ErrorR_r0_lF": {"name": "complete(_:)", "abstract": "<p>Output produced when the instance has completed, whether due to stream end, cancellation, or an error.", "parent_name": "Event"}, "Classes/DataStreamRequest/Stream.html#/s:9Alamofire17DataStreamRequestC0C0V5eventAC5EventOy_xq_Gvp": {"name": "event", "abstract": "<p>Latest <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest/Event.html\">Event</a></code> from the stream.</p>", "parent_name": "Stream"}, "Classes/DataStreamRequest/Stream.html#/s:9Alamofire17DataStreamRequestC0C0V5tokenAC17CancellationTokenVvp": {"name": "token", "abstract": "<p><PERSON><PERSON> used to cancel the stream.</p>", "parent_name": "Stream"}, "Classes/DataStreamRequest/Stream.html#/s:9Alamofire17DataStreamRequestC0C0V6cancelyyF": {"name": "cancel()", "abstract": "<p>Cancel the ongoing stream by canceling the underlying <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code>.</p>", "parent_name": "Stream"}, "Classes/DataStreamRequest/Stream.html#/s:9Alamofire17DataStreamRequestC0C0V6results6ResultOyxq_GSgvp": {"name": "result", "abstract": "<p>Incoming <code>Result</code> values from <code>Event.stream</code>.</p>", "parent_name": "Stream"}, "Classes/DataStreamRequest/Stream.html#/s:9Alamofire17DataStreamRequestC0C0V5valuexSgvp": {"name": "value", "abstract": "<p><code>Success</code> value of the instance, if any.</p>", "parent_name": "Stream"}, "Classes/DataStreamRequest/Stream.html#/s:9Alamofire17DataStreamRequestC0C0V5errorq_Sgvp": {"name": "error", "abstract": "<p><code>Failure</code> value of the instance, if any.</p>", "parent_name": "Stream"}, "Classes/DataStreamRequest/Stream.html#/s:9Alamofire17DataStreamRequestC0C0V10completionAC10CompletionVSgvp": {"name": "completion", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest/Completion.html\">Completion</a></code> value of the instance, if any.</p>", "parent_name": "Stream"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC7Handlera": {"name": "Handler", "abstract": "<p>Closure type handling <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest/Stream.html\">DataStreamRequest.Stream</a></code> values.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest/Stream.html": {"name": "Stream", "abstract": "<p>Type encapsulating an <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest/Event.html\">Event</a></code> as it flows through the stream, as well as a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest/CancellationToken.html\">CancellationToken</a></code> which can be used", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest/Event.html": {"name": "Event", "abstract": "<p>Type representing an event flowing through the stream. Contains either the <code>Result</code> of processing streamed", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest/Completion.html": {"name": "Completion", "abstract": "<p>Value containing the state of a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html\">DataStreamRequest</a></code> when the stream was completed.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest/CancellationToken.html": {"name": "CancellationToken", "abstract": "<p>Type used to cancel an ongoing stream.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC11convertibleAA21URLRequestConvertible_pvp": {"name": "convertible", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value used to create <code>URLRequest</code>s for this instance.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC021automaticallyCancelOnC5ErrorSbvp": {"name": "automaticallyCancelOnStreamError", "abstract": "<p>Whether or not the instance will be cancelled if stream parsing encounters an error.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCtYbcF": {"name": "validate(_:)", "abstract": "<p>Validates the <code>URLRequest</code> and <code>HTTPURLResponse</code> received for the instance using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC10Validationa\">Validation</a></code> closure.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC07asInputC010bufferSizeSo07NSInputC0CSgSi_tF": {"name": "asInputStream(bufferSize:)", "abstract": "<p>Produces an <code>InputStream</code> that receives the <code>Data</code> received by the instance.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC14onHTTPResponse0E07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseC_yAA0D0C19ResponseDispositionOctctF": {"name": "onHTTPResponse(on:perform:)", "abstract": "<p>Sets a closure called whenever the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> produces an <code>HTTPURLResponse</code> and providing a completion", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC14onHTTPResponse0E07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseCctF": {"name": "onHTTPResponse(on:perform:)", "abstract": "<p>Sets a closure called whenever the <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> produces an <code>HTTPURLResponse</code>.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC08responseC02on6streamACXDSo17OS_dispatch_queueC_yAC0C0Vy_10Foundation0B0Vs5NeverOGKctF": {"name": "responseStream(on:stream:)", "abstract": "<p>Adds a <code>StreamHandler</code> which performs no parsing on incoming <code>Data</code>.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC08responseC05using2on6streamACXDx_So17OS_dispatch_queueCyAC0C0Vy_16SerializedObjectQzAA7AFErrorOGKctAA0bC10SerializerRzlF": {"name": "responseStream(using:on:stream:)", "abstract": "<p>Adds a <code>StreamHandler</code> which uses the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code> to process incoming <code>Data</code>.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC08responseC6String2on6streamACXDSo17OS_dispatch_queueC_yAC0C0Vy_SSs5NeverOGKctF": {"name": "responseStreamString(on:stream:)", "abstract": "<p>Adds a <code>StreamHandler</code> which parses incoming <code>Data</code> as a UTF8 <code>String</code>.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC08responseC9Decodable2of2on5using12preprocessor6streamACXDxm_So17OS_dispatch_queueCAA0B7Decoder_pAA0B12Preprocessor_pyAC0C0Vy_xAA7AFErrorOGKctSeRzlF": {"name": "responseStreamDecodable(of:on:using:preprocessor:stream:)", "abstract": "<p>Adds a <code>StreamHandler</code> which parses incoming <code>Data</code> using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataDecoder.html\">DataDecoder</a></code>.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC07publishC05using2onAA0bC9PublisherVy16SerializedObjectQzGx_So17OS_dispatch_queueCtAA0bC10SerializerRzlF": {"name": "publishStream(using:on:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamPublisher.html\">DataStreamPublisher</a></code> for this instance using the given <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataStreamSerializer.html\">DataStreamSerializer</a></code> and <code>DispatchQueue</code>.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC07publishB05queueAA0bC9PublisherVy10Foundation0B0VGSo012OS_dispatch_F0C_tF": {"name": "publishData(queue:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamPublisher.html\">DataStreamPublisher</a></code> for this instance which uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/PassthroughStreamSerializer.html\">PassthroughStreamSerializer</a></code> to stream <code>Data</code>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC13publishString5queueAA0bC9PublisherVySSGSo012OS_dispatch_G0C_tF": {"name": "publishString(queue:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamPublisher.html\">DataStreamPublisher</a></code> for this instance which uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StringStreamSerializer.html\">StringStreamSerializer</a></code> to serialize stream", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC16publishDecodable4type5queue7decoder12preprocessorAA0bC9PublisherVyxGxm_So012OS_dispatch_H0CAA0B7Decoder_pAA0B12Preprocessor_ptSeRzs8SendableRzlF": {"name": "publishDecodable(type:queue:decoder:preprocessor:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamPublisher.html\">DataStreamPublisher</a></code> for this instance which uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DecodableStreamSerializer.html\">DecodableStreamSerializer</a></code> with the provided", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC13httpResponses15bufferingPolicyAA0C2OfVySo17NSHTTPURLResponseCGScS12ContinuationV09BufferingH0OyAI__G_tF": {"name": "httpResponses(bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StreamOf.html\">StreamOf&lt;HTTPURLResponse&gt;</a></code> for the instance&rsquo;s responses.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC14onHTTPResponse7performACXDAA0D0C19ResponseDispositionOSo17NSHTTPURLResponseCYaYbc_tF": {"name": "onHTTPResponse(perform:)", "abstract": "<p>Sets an async closure returning a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request/ResponseDisposition.html\">Request.ResponseDisposition</a></code>, called whenever the <code>DataStreamRequest</code>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC14onHTTPResponse7performACXDySo17NSHTTPURLResponseCYaYbc_tF": {"name": "onHTTPResponse(perform:)", "abstract": "<p>Sets an async closure called whenever the <code>DataStreamRequest</code> produces an <code>HTTPURLResponse</code>.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC10streamTaskAA0bcF0VyF": {"name": "streamTask()", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataStreamTask.html\">DataStreamTask</a></code> used to <code>await</code> streams of serialized values.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC10Validationa": {"name": "Validation", "abstract": "<p>A closure used to validate a request that takes a <code>URLRequest</code> and <code>HTTPURLResponse</code> and returns whether the", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF": {"name": "validate(statusCode:)", "abstract": "<p>Validates that the response has a status code in the specified sequence.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF": {"name": "validate(contentType:)", "abstract": "<p>Validates that the response has a content type in the specified sequence.</p>", "parent_name": "DataStreamRequest"}, "Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC8validateACXDyF": {"name": "validate()", "abstract": "<p>Validates that the response has a status code in the default acceptable range of 200&hellip;299, and that the content", "parent_name": "DataStreamRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC11convertibleAA21URLRequestConvertible_pvp": {"name": "convertible", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/URLRequestConvertible.html\">URLRequestConvertible</a></code> value used to create <code>URLRequest</code>s for this instance.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC4data10Foundation0B0VSgvp": {"name": "data", "abstract": "<p><code>Data</code> read from the server so far.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCAI0B0VSgtcF": {"name": "validate(_:)", "abstract": "<p>Validates the request, using the specified closure.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC14onHTTPResponse0D07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseC_yAA0C0C19ResponseDispositionOctctF": {"name": "onHTTPResponse(on:perform:)", "abstract": "<p>Sets a closure called whenever the <code>DataRequest</code> produces an <code>HTTPURLResponse</code> and providing a completion", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC14onHTTPResponse0D07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseCctF": {"name": "onHTTPResponse(on:perform:)", "abstract": "<p>Sets a closure called whenever the <code>DataRequest</code> produces an <code>HTTPURLResponse</code>.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC8response5queue17completionHandlerACXDSo012OS_dispatch_E0C_yAA0B8ResponseVy10Foundation0B0VSgAA7AFErrorOGctF": {"name": "response(queue:completion<PERSON>andler:)", "abstract": "<p>Adds a handler to be called once the request has finished.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGctAA0bkF8ProtocolRzlF": {"name": "response(queue:responseSerializer:completionHandler:)", "abstract": "<p>Adds a handler to be called once the request has finished.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGctAA0kF0RzlF": {"name": "response(queue:responseSerializer:completionHandler:)", "abstract": "<p>Adds a handler to be called once the request has finished.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC08responseB05queue16dataPreprocessor18emptyResponseCodes0hC7Methods17completionHandlerACXDSo012OS_dispatch_E0C_AA0bG0_pShySiGShyAA10HTTPMethodVGyAA0bI0Vy10Foundation0B0VAA7AFErrorOGctF": {"name": "responseData(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataResponseSerializer.html\">DataResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC14responseString5queue16dataPreprocessor8encoding18emptyResponseCodes0jC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA0bH0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGyAA0bK0VySSAA7AFErrorOGctF": {"name": "responseString(queue:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/StringResponseSerializer.html\">StringResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC12responseJSON5queue16dataPreprocessor18emptyResponseCodes0iC7Methods7options17completionHandlerACXDSo012OS_dispatch_F0C_AA0bH0_pShySiGShyAA10HTTPMethodVGSo20NSJSONReadingOptionsVyAA0bJ0VyypAA7AFErrorOGctF": {"name": "responseJSON(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:options:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/JSONResponseSerializer.html\">JSONResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC17responseDecodable2of5queue16dataPreprocessor7decoder18emptyResponseCodes0kC7Methods17completionHandlerACXDxm_So012OS_dispatch_G0CAA0bI0_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGyAA0bL0VyxAA7AFErrorOGctSeRzlF": {"name": "responseDecodable(of:queue:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:completionHandler:)", "abstract": "<p>Adds a handler using a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DecodableResponseSerializer.html\">DecodableResponseSerializer</a></code> to be called once the request has finished.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0E10SerializerRz16SerializedObjectQzRs_r0_lF": {"name": "publishResponse(using:on:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponsePublisher.html\">DataResponsePublisher</a></code> for this instance using the given <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> and <code>DispatchQueue</code>.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC07publishB05queue12preprocessor18emptyResponseCodes0gC7MethodsAA0bH9PublisherVy10Foundation0B0VGSo012OS_dispatch_E0C_AA0B12Preprocessor_pShySiGShyAA10HTTPMethodVGtF": {"name": "publishData(queue:preprocessor:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponsePublisher.html\">DataResponsePublisher</a></code> for this instance and uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataResponseSerializer.html\">DataResponseSerializer</a></code> to serialize the", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC13publishString5queue12preprocessor8encoding18emptyResponseCodes0iC7MethodsAA0bJ9PublisherVySSGSo012OS_dispatch_F0C_AA0B12Preprocessor_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF": {"name": "publishString(queue:preprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponsePublisher.html\">DataResponsePublisher</a></code> for this instance and uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/StringResponseSerializer.html\">StringResponseSerializer</a></code> to serialize the", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jK7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA0B12Preprocessor_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF": {"name": "publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyResponseMethods:)", "abstract": "<p>Undocumented</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jC7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA0B12Preprocessor_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF": {"name": "publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponsePublisher.html\">DataResponsePublisher</a></code> for this instance and uses a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DecodableResponseSerializer.html\">DecodableResponseSerializer</a></code> to serialize the", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC19publishUnserialized5queueAA0B17ResponsePublisherVy10Foundation0B0VSgGSo012OS_dispatch_F0C_tF": {"name": "publishUnserialized(queue:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataResponsePublisher.html\">DataResponsePublisher</a></code> for this instance which does not serialize the response before publishing.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC13httpResponses15bufferingPolicyAA8StreamOfVySo17NSHTTPURLResponseCGScS12ContinuationV09BufferingG0OyAI__G_tF": {"name": "httpResponses(bufferingPolicy:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/StreamOf.html\">StreamOf&lt;HTTPURLResponse&gt;</a></code> for the instance&rsquo;s responses.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC14onHTTPResponse7performACXDAA0C0C19ResponseDispositionOSo17NSHTTPURLResponseCYaYbc_tF": {"name": "onHTTPResponse(perform:)", "abstract": "<p>Sets an async closure returning a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request/ResponseDisposition.html\">Request.ResponseDisposition</a></code>, called whenever the <code>DataRequest</code> produces an", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC14onHTTPResponse7performACXDySo17NSHTTPURLResponseCYaYbc_tF": {"name": "onHTTPResponse(perform:)", "abstract": "<p>Sets an async closure called whenever the <code>DataRequest</code> produces an <code>HTTPURLResponse</code>.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC011serializingB023automaticallyCancelling16dataPreprocessor18emptyResponseCodes0iC7MethodsAA0B4TaskVy10Foundation0B0VGSb_AA0bH0_pShySiGShyAA10HTTPMethodVGtF": {"name": "serializingData(automaticallyCancelling:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataTask.html\">DataTask</a></code> to <code>await</code> a <code>Data</code> value.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC20serializingDecodable_23automaticallyCancelling16dataPreprocessor7decoder18emptyResponseCodes0kC7MethodsAA0B4TaskVyxGxm_SbAA0bI0_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF": {"name": "serializingDecodable(_:automaticallyCancelling:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataTask.html\">DataTask</a></code> to <code>await</code> serialization of a <code>Decodable</code> value.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC17serializingString23automaticallyCancelling16dataPreprocessor8encoding18emptyResponseCodes0kC7MethodsAA0B4TaskVySSGSb_AA0bI0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF": {"name": "serializingString(automaticallyCancelling:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataTask.html\">DataTask</a></code> to <code>await</code> serialization of a <code>String</code> value.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC19serializingResponse5using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0E10SerializerRzlF": {"name": "serializingResponse(using:automaticallyCancelling:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataTask.html\">DataTask</a></code> to <code>await</code> serialization using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> instance.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC19serializingResponse5using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0bE18SerializerProtocolRzlF": {"name": "serializingResponse(using:automaticallyCancelling:)", "abstract": "<p>Creates a <code><a href=\"36f8f5912051ae747ef441d6511ca4cbStructs/DataTask.html\">DataTask</a></code> to <code>await</code> serialization using the provided <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/DataResponseSerializerProtocol.html\">DataResponseSerializerProtocol</a></code> instance.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC10Validationa": {"name": "Validation", "abstract": "<p>A closure used to validate a request that takes a URL request, a URL response and data, and returns whether the", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF": {"name": "validate(statusCode:)", "abstract": "<p>Validates that the response has a status code in the specified sequence.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF": {"name": "validate(contentType:)", "abstract": "<p>Validates that the response has a content type in the specified sequence.</p>", "parent_name": "DataRequest"}, "Classes/DataRequest.html#/s:9Alamofire11DataRequestC8validateACXDyF": {"name": "validate()", "abstract": "<p>Validates that the response has a status code in the default acceptable range of 200&hellip;299, and that the content", "parent_name": "DataRequest"}, "Classes/DataRequest.html": {"name": "DataRequest", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> subclass which handles in-memory <code>Data</code> download using <code>URLSessionDataTask</code>.</p>"}, "Classes/DataStreamRequest.html": {"name": "DataStreamRequest", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> subclass which streams HTTP response <code>Data</code> through a <code>Handler</code> closure.</p>"}, "Classes/DownloadRequest.html": {"name": "DownloadRequest", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> subclass which downloads <code>Data</code> to a file on disk using <code>URLSessionDownloadTask</code>.</p>"}, "Classes/Request.html": {"name": "Request", "abstract": "<p><code>Request</code> is the common superclass of all Alamofire request types and provides common state, delegate, and callback"}, "Classes/AlamofireNotifications.html": {"name": "AlamofireNotifications", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html\">EventMonitor</a></code> that provides Alamofire&rsquo;s notifications.</p>"}, "Classes/JSONParameterEncoder.html": {"name": "JSONParameterEncoder", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ParameterEncoder.html\">ParameterEncoder</a></code> that encodes types as JSON body data.</p>"}, "Classes/URLEncodedFormParameterEncoder.html": {"name": "URLEncodedFormParameterEncoder", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ParameterEncoder.html\">ParameterEncoder</a></code> that encodes types as URL-encoded query strings to be set on the URL or as body data, depending"}, "Classes/Session.html": {"name": "Session", "abstract": "<p><code>Session</code> creates and manages Alamofire&rsquo;s <code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/Request.html\">Request</a></code> types during their lifetimes. It also provides common"}, "Classes/SessionDelegate.html": {"name": "SessionDelegate", "abstract": "<p>Class which implements the various <code>URLSessionDelegate</code> methods to connect various Alamofire features.</p>"}, "Classes/UploadRequest.html": {"name": "UploadRequest", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbClasses/DataRequest.html\">DataRequest</a></code> subclass which handles <code>Data</code> upload from memory, file, or stream using <code>URLSessionUploadTask</code>.</p>"}, "Classes/AuthenticationInterceptor.html": {"name": "AuthenticationInterceptor", "abstract": "<p>The <code>AuthenticationInterceptor</code> class manages the queuing and threading complexity of authenticating requests."}, "Classes/CompositeEventMonitor.html": {"name": "CompositeEventMonitor", "abstract": "<p>An <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html\">EventMonitor</a></code> which can contain multiple <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html\">EventMonitor</a></code>s and calls their methods on their queues.</p>"}, "Classes/ClosureEventMonitor.html": {"name": "ClosureEventMonitor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/EventMonitor.html\">EventMonitor</a></code> that allows optional closures to be set to receive events.</p>"}, "Classes/MultipartFormData.html": {"name": "MultipartFormData", "abstract": "<p>Constructs <code>multipart/form-data</code> for uploads within an HTTP or HTTPS body. There are currently two ways to encode"}, "Classes/NetworkReachabilityManager.html": {"name": "NetworkReachabilityManager", "abstract": "<p>The <code>NetworkReachabilityManager</code> class listens for reachability changes of hosts and addresses for both cellular and"}, "Classes/Adapter.html": {"name": "Adapter", "abstract": "<p>Closure-based <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code>.</p>"}, "Classes/Retrier.html": {"name": "Retrier", "abstract": "<p>Closure-based <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code>.</p>"}, "Classes/Interceptor.html": {"name": "Interceptor", "abstract": "<p><code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestInterceptor.html\">RequestInterceptor</a></code> which can use multiple <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestAdapter.html\">RequestAdapter</a></code> and <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/RequestRetrier.html\">RequestRetrier</a></code> values.</p>"}, "Classes/DataResponseSerializer.html": {"name": "DataResponseSerializer", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> that performs minimal response checking and returns any response <code>Data</code> as-is. By default, a"}, "Classes/StringResponseSerializer.html": {"name": "StringResponseSerializer", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> that decodes the response data as a <code>String</code>. By default, a request returning <code>nil</code> or no"}, "Classes/JSONResponseSerializer.html": {"name": "JSONResponseSerializer", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> that decodes the response data using <code>JSONSerialization</code>. By default, a request returning"}, "Classes/DecodableResponseSerializer.html": {"name": "DecodableResponseSerializer", "abstract": "<p>A <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ResponseSerializer.html\">ResponseSerializer</a></code> that decodes the response data as a <code>Decodable</code> value using any decoder that conforms to"}, "Classes/RetryPolicy.html": {"name": "RetryPolicy", "abstract": "<p>A retry policy that retries requests using an exponential backoff for allowed HTTP methods and HTTP status codes"}, "Classes/ConnectionLostRetryPolicy.html": {"name": "ConnectionLostRetryPolicy", "abstract": "<p>A retry policy that automatically retries idempotent requests for network connection lost errors. For more"}, "Classes/ServerTrustManager.html": {"name": "ServerTrustManager", "abstract": "<p>Responsible for managing the mapping of <code><a href=\"36f8f5912051ae747ef441d6511ca4cbProtocols/ServerTrustEvaluating.html\">ServerTrustEvaluating</a></code> values to given hosts.</p>"}, "Classes/DefaultTrustEvaluator.html": {"name": "DefaultTrustEvaluator", "abstract": "<p>An evaluator which uses the default server trust evaluation while allowing you to control whether to validate the"}, "Classes/RevocationTrustEvaluator.html": {"name": "RevocationTrustEvaluator", "abstract": "<p>An evaluator which Uses the default and revoked server trust evaluations allowing you to control whether to validate"}, "Classes/PinnedCertificatesTrustEvaluator.html": {"name": "PinnedCertificatesTrustEvaluator", "abstract": "<p>Uses the pinned certificates to validate the server trust. The server trust is considered valid if one of the pinned"}, "Classes/PublicKeysTrustEvaluator.html": {"name": "PublicKeysTrustEvaluator", "abstract": "<p>Uses the pinned public keys to validate the server trust. The server trust is considered valid if one of the pinned"}, "Classes/CompositeTrustEvaluator.html": {"name": "CompositeTrustEvaluator", "abstract": "<p>Uses the provided evaluators to validate the server trust. The trust is only considered valid if all of the"}, "Classes/DisabledTrustEvaluator.html": {"name": "DisabledTrustEvaluator", "abstract": "<p>Disables all evaluation which in turn will always consider any server trust as valid.</p>"}, "Classes/URLEncodedFormEncoder.html": {"name": "URLEncodedFormEncoder", "abstract": "<p>An object that encodes instances into URL-encoded query strings.</p>"}, "Classes.html": {"name": "Classes", "abstract": "<p>The following classes are available globally.</p>"}, "Global%20Variables.html": {"name": "Global Variables", "abstract": "<p>The following global variables are available globally.</p>"}, "Enums.html": {"name": "Enumerations", "abstract": "<p>The following enumerations are available globally.</p>"}, "Extensions.html": {"name": "Extensions", "abstract": "<p>The following extensions are available globally.</p>"}, "Protocols.html": {"name": "Protocols", "abstract": "<p>The following protocols are available globally.</p>"}, "Structs.html": {"name": "Structures", "abstract": "<p>The following structures are available globally.</p>"}, "Typealiases.html": {"name": "Type Aliases", "abstract": "<p>The following type aliases are available globally.</p>"}}