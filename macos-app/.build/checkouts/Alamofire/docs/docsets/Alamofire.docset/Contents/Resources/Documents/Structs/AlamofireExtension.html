<!DOCTYPE html>
<html lang="en">
  <head>
    <title>AlamofireExtension Structure Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Struct/AlamofireExtension" class="dashAnchor"></a>

    <a title="AlamofireExtension Structure Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Structs.html">Structures</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      AlamofireExtension Structure Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>AlamofireExtension</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">AlamofireExtension</span><span class="o">&lt;</span><span class="kt">ExtendedType</span><span class="o">&gt;</span></code></pre>

                </div>
              </div>
            <p>Type that acts as a generic extension point for all <code><a href="../Protocols/AlamofireExtended.html">AlamofireExtended</a></code> types.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionV4typexvp"></a>
                    <a name="//apple_ref/swift/Property/type" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionV4typexvp">type</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Stores the type or meta-type of any extended type.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">private(set)</span> <span class="k">var</span> <span class="nv">type</span><span class="p">:</span> <span class="kt">ExtendedType</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVyACyxGxcfc"></a>
                    <a name="//apple_ref/swift/Method/init(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVyACyxGxcfc">init(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Create an instance from the provided value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="nf">init</span><span class="p">(</span><span class="n">_</span> <span class="nv">type</span><span class="p">:</span> <span class="kt">ExtendedType</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Instance being extended.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%3A%20%60URLSessionConfiguration%60"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType`: `URLSessionConfiguration`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%3A%20%60URLSessionConfiguration%60"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code>: <code>URLSessionConfiguration</code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo25NSURLSessionConfigurationCRbzlE7defaultAEvpZ"></a>
                    <a name="//apple_ref/swift/Variable/default" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo25NSURLSessionConfigurationCRbzlE7defaultAEvpZ">default</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Alamofire&rsquo;s default configuration. Same as <code>URLSessionConfiguration.default</code> but adds Alamofire default
<code>Accept-Language</code>, <code>Accept-Encoding</code>, and <code>User-Agent</code> headers.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">var</span> <span class="p">`</span><span class="nv">default</span><span class="p">`:</span> <span class="kt">URLSessionConfiguration</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo25NSURLSessionConfigurationCRbzlE9ephemeralAEvpZ"></a>
                    <a name="//apple_ref/swift/Variable/ephemeral" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo25NSURLSessionConfigurationCRbzlE9ephemeralAEvpZ">ephemeral</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>.ephemeral</code> configuration with Alamofire&rsquo;s default <code>Accept-Language</code>, <code>Accept-Encoding</code>, and <code>User-Agent</code>
headers.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="k">var</span> <span class="nv">ephemeral</span><span class="p">:</span> <span class="kt">URLSessionConfiguration</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%3A%20%60Bundle%60"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType`: `Bundle`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%3A%20%60Bundle%60"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code>: <code>Bundle</code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE12certificatesSaySo17SecCertificateRefaGvp"></a>
                    <a name="//apple_ref/swift/Property/certificates" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE12certificatesSaySo17SecCertificateRefaGvp">certificates</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns all valid <code>cer</code>, <code>crt</code>, and <code>der</code> certificates in the bundle.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">certificates</span><span class="p">:</span> <span class="p">[</span><span class="kt">SecCertificate</span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE10publicKeysSaySo9SecKeyRefaGvp"></a>
                    <a name="//apple_ref/swift/Property/publicKeys" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE10publicKeysSaySo9SecKeyRefaGvp">publicKeys</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns all public keys for the valid certificates in the bundle.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">publicKeys</span><span class="p">:</span> <span class="p">[</span><span class="kt">SecKey</span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE5paths19forResourcesOfTypesSaySSGAH_tF"></a>
                    <a name="//apple_ref/swift/Method/paths(forResourcesOfTypes:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo8NSBundleCRbzlE5paths19forResourcesOfTypesSaySSGAH_tF">paths(forResourcesOfTypes:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns all pathnames for the resources identified by the provided file extensions.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">paths</span><span class="p">(</span><span class="n">forResourcesOfTypes</span> <span class="nv">types</span><span class="p">:</span> <span class="p">[</span><span class="kt">String</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="p">[</span><span class="kt">String</span><span class="p">]</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>types</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The filename extensions locate.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>All pathnames for the given filename extensions.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecTrust%60"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType` == `SecTrust`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecTrust%60"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code> == <code>SecTrust</code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8evaluate13afterApplyingySo0c6PolicyE0a_tKF"></a>
                    <a name="//apple_ref/swift/Method/evaluate(afterApplying:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8evaluate13afterApplyingySo0c6PolicyE0a_tKF">evaluate(afterApplying:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Evaluates <code>self</code> after applying the <code>SecPolicy</code> value provided.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    <p>Any <code>Error</code> from applying the <code>SecPolicy</code> or from evaluation.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">iOS</span> <span class="mi">12</span><span class="p">,</span> <span class="n">macOS</span> <span class="mf">10.14</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">12</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">5</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">evaluate</span><span class="p">(</span><span class="n">afterApplying</span> <span class="nv">policy</span><span class="p">:</span> <span class="kt">SecPolicy</span><span class="p">)</span> <span class="k">throws</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>policy</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>SecPolicy</code> to apply to <code>self</code> before evaluation.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8validate6policy13errorProducerySo0c6PolicyE0a_s5Error_ps5Int32V_So0cD10ResultTypeVtXEtKF"></a>
                    <a name="//apple_ref/swift/Method/validate(policy:errorProducer:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8validate6policy13errorProducerySo0c6PolicyE0a_s5Error_ps5Int32V_So0cD10ResultTypeVtXEtKF">validate(policy:<wbr>errorProducer:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Attempts to validate <code>self</code> using the <code>SecPolicy</code> provided and transforming any error produced using the closure passed.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    Any <code>Error</code> from applying the <code>policy</code>, or the result of <code>errorProducer</code> if validation fails.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">iOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mi">10</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mi">12</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(afterApplying:﹚"</span><span class="p">)</span>
<span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mf">10.12</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mf">10.14</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(afterApplying:﹚"</span><span class="p">)</span>
<span class="kd">@available</span><span class="p">(</span><span class="n">tvOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mi">10</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mi">12</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(afterApplying:﹚"</span><span class="p">)</span>
<span class="kd">@available</span><span class="p">(</span><span class="n">watchOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(afterApplying:﹚"</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">(</span><span class="nv">policy</span><span class="p">:</span> <span class="kt">SecPolicy</span><span class="p">,</span> <span class="nv">errorProducer</span><span class="p">:</span> <span class="p">(</span><span class="n">_</span> <span class="nv">status</span><span class="p">:</span> <span class="kt">OSStatus</span><span class="p">,</span> <span class="n">_</span> <span class="nv">result</span><span class="p">:</span> <span class="kt">SecTrustResultType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kd">any</span> <span class="kt">Error</span><span class="p">)</span> <span class="k">throws</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>policy</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>SecPolicy</code> used to evaluate <code>self</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>errorProducer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The closure used transform the failed <code>OSStatus</code> and <code>SecTrustResultType</code>.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE5apply6policyAESo0c6PolicyE0a_tKF"></a>
                    <a name="//apple_ref/swift/Method/apply(policy:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE5apply6policyAESo0c6PolicyE0a_tKF">apply(policy:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Applies a <code>SecPolicy</code> to <code>self</code>, throwing if it fails.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    <p>An <code>AFError.serverTrustEvaluationFailed</code> instance with a <code>.policyApplicationFailed</code> reason.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">apply</span><span class="p">(</span><span class="nv">policy</span><span class="p">:</span> <span class="kt">SecPolicy</span><span class="p">)</span> <span class="k">throws</span> <span class="o">-&gt;</span> <span class="kt">SecTrust</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>policy</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>SecPolicy</code>.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p><code>self</code>, with the policy applied.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8evaluateyyKF"></a>
                    <a name="//apple_ref/swift/Method/evaluate()" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8evaluateyyKF">evaluate()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Evaluate <code>self</code>, throwing an <code>Error</code> if evaluation fails.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    <code>AFError.serverTrustEvaluationFailed</code> with reason <code>.trustValidationFailed</code> and associated error from
      the underlying evaluation.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">iOS</span> <span class="mi">12</span><span class="p">,</span> <span class="n">macOS</span> <span class="mf">10.14</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">12</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">5</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">evaluate</span><span class="p">()</span> <span class="k">throws</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8validate13errorProducerys5Error_ps5Int32V_So0cD10ResultTypeVtXE_tKF"></a>
                    <a name="//apple_ref/swift/Method/validate(errorProducer:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE8validate13errorProducerys5Error_ps5Int32V_So0cD10ResultTypeVtXE_tKF">validate(errorProducer:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validate <code>self</code>, passing any failure values through <code>errorProducer</code>.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    The <code>Error</code> produced by the <code>errorProducer</code> closure.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">iOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mi">10</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mi">12</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(﹚"</span><span class="p">)</span>
<span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mf">10.12</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mf">10.14</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(﹚"</span><span class="p">)</span>
<span class="kd">@available</span><span class="p">(</span><span class="n">tvOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mi">10</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mi">12</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(﹚"</span><span class="p">)</span>
<span class="kd">@available</span><span class="p">(</span><span class="n">watchOS</span><span class="p">,</span> <span class="nv">introduced</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span> <span class="nv">deprecated</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span> <span class="nv">renamed</span><span class="p">:</span> <span class="s">"evaluate(﹚"</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">(</span><span class="nv">errorProducer</span><span class="p">:</span> <span class="p">(</span><span class="n">_</span> <span class="nv">status</span><span class="p">:</span> <span class="kt">OSStatus</span><span class="p">,</span> <span class="n">_</span> <span class="nv">result</span><span class="p">:</span> <span class="kt">SecTrustResultType</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kd">any</span> <span class="kt">Error</span><span class="p">)</span> <span class="k">throws</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>errorProducer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The closure used to transform the failed <code>OSStatus</code> and <code>SecTrustResultType</code> into an
                       <code>Error</code>.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE21setAnchorCertificatesyySaySo0c11CertificateE0aGKF"></a>
                    <a name="//apple_ref/swift/Method/setAnchorCertificates(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE21setAnchorCertificatesyySaySo0c11CertificateE0aGKF">setAnchorCertificates(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets a custom certificate chain on <code>self</code>, allowing full validation of a self-signed certificate and its chain.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    Any error produced when applying the new certificate chain.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">setAnchorCertificates</span><span class="p">(</span><span class="n">_</span> <span class="nv">certificates</span><span class="p">:</span> <span class="p">[</span><span class="kt">SecCertificate</span><span class="p">])</span> <span class="k">throws</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>certificates</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>SecCertificate</code>s to add to the chain.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE10publicKeysSaySo0c3KeyE0aGvp"></a>
                    <a name="//apple_ref/swift/Property/publicKeys" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE10publicKeysSaySo0c3KeyE0aGvp">publicKeys</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The public keys contained in <code>self</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">publicKeys</span><span class="p">:</span> <span class="p">[</span><span class="kt">SecKey</span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE12certificatesSaySo0c11CertificateE0aGvp"></a>
                    <a name="//apple_ref/swift/Property/certificates" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE12certificatesSaySo0c11CertificateE0aGvp">certificates</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code>SecCertificate</code>s contained in <code>self</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">certificates</span><span class="p">:</span> <span class="p">[</span><span class="kt">SecCertificate</span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE15certificateDataSay10Foundation0G0VGvp"></a>
                    <a name="//apple_ref/swift/Property/certificateData" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE15certificateDataSay10Foundation0G0VGvp">certificateData</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code>Data</code> values for all certificates contained in <code>self</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">certificateData</span><span class="p">:</span> <span class="p">[</span><span class="kt">Data</span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE24performDefaultValidation7forHostySS_tKF"></a>
                    <a name="//apple_ref/swift/Method/performDefaultValidation(forHost:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE24performDefaultValidation7forHostySS_tKF">performDefaultValidation(forHost:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates <code>self</code> after applying <code>SecPolicy.af.default</code>. This evaluation does not validate the hostname.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    An <code>AFError.serverTrustEvaluationFailed</code> instance with a <code>.defaultEvaluationFailed</code> reason.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">performDefaultValidation</span><span class="p">(</span><span class="n">forHost</span> <span class="nv">host</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span> <span class="k">throws</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>host</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The hostname, used only in the error output if validation fails.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE17performValidation7forHostySS_tKF"></a>
                    <a name="//apple_ref/swift/Method/performValidation(forHost:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo11SecTrustRefaRszlE17performValidation7forHostySS_tKF">performValidation(forHost:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates <code>self</code> after applying <code>SecPolicy.af.hostname(host)</code>, which performs the default validation as well as
hostname validation.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    An <code>AFError.serverTrustEvaluationFailed</code> instance with a <code>.defaultEvaluationFailed</code> reason.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">performValidation</span><span class="p">(</span><span class="n">forHost</span> <span class="nv">host</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span> <span class="k">throws</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>host</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The hostname to use in the validation.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecPolicy%60"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType` == `SecPolicy`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecPolicy%60"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code> == <code>SecPolicy</code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE7defaultAEvpZ"></a>
                    <a name="//apple_ref/swift/Variable/default" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE7defaultAEvpZ">default</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code>SecPolicy</code> instance which will validate server certificates but not require a host name match.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">let</span> <span class="p">`</span><span class="nv">default</span><span class="p">`:</span> <span class="kt">SecPolicy</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE8hostnameyAESSFZ"></a>
                    <a name="//apple_ref/swift/Method/hostname(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE8hostnameyAESSFZ">hostname(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code>SecPolicy</code> instance which will validate server certificates and much match the provided hostname.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">hostname</span><span class="p">(</span><span class="n">_</span> <span class="nv">hostname</span><span class="p">:</span> <span class="kt">String</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">SecPolicy</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>hostname</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The hostname to validate against.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>SecPolicy</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE10revocation7optionsAeA24RevocationTrustEvaluatorC7OptionsV_tKFZ"></a>
                    <a name="//apple_ref/swift/Method/revocation(options:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo12SecPolicyRefaRszlE10revocation7optionsAeA24RevocationTrustEvaluatorC7OptionsV_tKFZ">revocation(options:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code>SecPolicy</code> which checks the revocation of certificates.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    <p>An <code>AFError.serverTrustEvaluationFailed</code> error with reason <code>.revocationPolicyCreationFailed</code>
                 if the policy cannot be created.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">revocation</span><span class="p">(</span><span class="nv">options</span><span class="p">:</span> <span class="kt"><a href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a></span><span class="o">.</span><span class="kt">Options</span><span class="p">)</span> <span class="k">throws</span> <span class="o">-&gt;</span> <span class="kt">SecPolicy</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>options</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/RevocationTrustEvaluator/Options.html">RevocationTrustEvaluator.Options</a></code> for evaluation.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>SecPolicy</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%20%3D%3D%20%5B%60SecCertificate%60%5D"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType` == [`SecCertificate`]" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%20%3D%3D%20%5B%60SecCertificate%60%5D"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code> == [<code>SecCertificate</code>]</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASaySo17SecCertificateRefaGRszlE4dataSay10Foundation4DataVGvp"></a>
                    <a name="//apple_ref/swift/Property/data" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASaySo17SecCertificateRefaGRszlE4dataSay10Foundation4DataVGvp">data</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>All <code>Data</code> values for the contained <code>SecCertificate</code>s.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">data</span><span class="p">:</span> <span class="p">[</span><span class="kt">Data</span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASaySo17SecCertificateRefaGRszlE10publicKeysSaySo0c3KeyE0aGvp"></a>
                    <a name="//apple_ref/swift/Property/publicKeys" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASaySo17SecCertificateRefaGRszlE10publicKeysSaySo0c3KeyE0aGvp">publicKeys</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>All public <code>SecKey</code> values for the contained <code>SecCertificate</code>s.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">publicKeys</span><span class="p">:</span> <span class="p">[</span><span class="kt">SecKey</span><span class="p">]</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecCertificate%60"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType` == `SecCertificate`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecCertificate%60"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code> == <code>SecCertificate</code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo17SecCertificateRefaRszlE9publicKeySo0cgE0aSgvp"></a>
                    <a name="//apple_ref/swift/Property/publicKey" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo17SecCertificateRefaRszlE9publicKeySo0cgE0aSgvp">publicKey</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The public key for <code>self</code>, if it can be extracted.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    On 2020 OSes and newer, only RSA and ECDSA keys are supported.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">publicKey</span><span class="p">:</span> <span class="kt">SecKey</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60OSStatus%60"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType` == `OSStatus`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60OSStatus%60"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code> == <code>OSStatus</code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAAs5Int32VRszlE9isSuccessSbvp"></a>
                    <a name="//apple_ref/swift/Property/isSuccess" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAAs5Int32VRszlE9isSuccessSbvp">isSuccess</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns whether <code>self</code> is <code>errSecSuccess</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">isSuccess</span><span class="p">:</span> <span class="kt">Bool</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecTrustResultType%60"></a>
                <a name="//apple_ref/swift/Section/Available where `ExtendedType` == `SecTrustResultType`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60ExtendedType%60%20%3D%3D%20%60SecTrustResultType%60"></a>
                  <h3 class="section-name"><span>Available where <code>ExtendedType</code> == <code>SecTrustResultType</code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A9ExtensionVAASo18SecTrustResultTypeVRszlE9isSuccessSbvp"></a>
                    <a name="//apple_ref/swift/Property/isSuccess" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A9ExtensionVAASo18SecTrustResultTypeVRszlE9isSuccessSbvp">isSuccess</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns whether <code>self</code> is <code>.unspecified</code> or <code>.proceed</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">isSuccess</span><span class="p">:</span> <span class="kt">Bool</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
