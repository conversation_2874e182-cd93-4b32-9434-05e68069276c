<!DOCTYPE html>
<html lang="en">
  <head>
    <title>AF Enumeration Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Enum/AF" class="dashAnchor"></a>

    <a title="AF Enumeration Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire Docs
        </a>
         (98% documented)
      </p>
    
      <p class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire%2Egithub%2Eio%2FAlamofire%2Fdocsets%2FAlamofire%2Exml">
            <img class="header-icon" src="../img/dash.png"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire Reference</a>
      <img class="carat" src="../img/carat.png" />
      AF Enumeration Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledEvaluator.html">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AF.html">AF</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Array.html">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content">
            <h1>AF</h1>
              <div class="declaration">
                <div class="language">
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">AF</span></code></pre>

                </div>
              </div>
            <p>Global namespace containing API for the <code>default</code> <code><a href="../Classes/Session.html">Session</a></code> instance.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Data%20Request"></a>
                <a name="//apple_ref/swift/Section/Data Request" class="dashAnchor"></a>
                <a href="#/Data%20Request">
                  <h3 class="section-name">Data Request</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO7request_6method10parameters8encoding7headers11interceptorAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSypGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0J11Interceptor_pSgtFZ"></a>
                    <a name="//apple_ref/swift/Method/request(_:method:parameters:encoding:headers:interceptor:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO7request_6method10parameters8encoding7headers11interceptorAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSypGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0J11Interceptor_pSgtFZ">request(_:method:parameters:encoding:headers:interceptor:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataRequest.html">DataRequest</a></code> using <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">Session.default</a></code> to retrieve the contents of the specified <code>url</code> using the
<code>method</code>, <code>parameters</code>, <code>encoding</code>, and <code>headers</code> provided.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">request</span><span class="p">(</span><span class="n">_</span> <span class="nv">url</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                           <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                           <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                           <span class="nv">encoding</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></span> <span class="o">=</span> <span class="kt"><a href="../Structs/URLEncoding.html">URLEncoding</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                           <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                           <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataRequest.html">DataRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>url</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>, <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></code>, <code><a href="../Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV7defaultACvpZ">URLEncoding.default</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DataRequest.html">DataRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO7request_6method10parameters7encoder7headers11interceptorAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0J11Interceptor_pSgtSERzlFZ"></a>
                    <a name="//apple_ref/swift/Method/request(_:method:parameters:encoder:headers:interceptor:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO7request_6method10parameters7encoder7headers11interceptorAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0J11Interceptor_pSgtSERzlFZ">request(_:method:parameters:encoder:headers:interceptor:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataRequest.html">DataRequest</a></code> using <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">Session.default</a></code> to retrieve the contents of the specified <code>url</code> using the
<code>method</code>, <code>parameters</code>, <code>encoding</code>, and <code>headers</code> provided.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="n">request</span><span class="o">&lt;</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">:</span> <span class="kt">Encodable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">_</span> <span class="nv">url</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                                                  <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                                                  <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                  <span class="nv">encoder</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                                                  <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                  <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataRequest.html">DataRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>url</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>, <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Encodable</code> parameters, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></code>, <code><a href="../Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7defaultACvpZ">URLEncodedFormParameterEncoder.default</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DataRequest.html">DataRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO7request_11interceptorAA11DataRequestCAA21URLRequestConvertible_p_AA0F11Interceptor_pSgtFZ"></a>
                    <a name="//apple_ref/swift/Method/request(_:interceptor:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO7request_11interceptorAA11DataRequestCAA21URLRequestConvertible_p_AA0F11Interceptor_pSgtFZ">request(_:interceptor:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataRequest.html">DataRequest</a></code> using <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">Session.default</a></code> to execute the specified <code>urlRequest</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">request</span><span class="p">(</span><span class="n">_</span> <span class="nv">urlRequest</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span> <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataRequest.html">DataRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>urlRequest</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DataRequest.html">DataRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Download%20Request"></a>
                <a name="//apple_ref/swift/Section/Download Request" class="dashAnchor"></a>
                <a href="#/Download%20Request">
                  <h3 class="section-name">Download Request</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO8download_6method10parameters8encoding7headers11interceptor2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSypGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0K11Interceptor_pSg10Foundation3URLV011destinationS0_AL7OptionsV7optionstAZ_So17NSHTTPURLResponseCtcSgtFZ"></a>
                    <a name="//apple_ref/swift/Method/download(_:method:parameters:encoding:headers:interceptor:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO8download_6method10parameters8encoding7headers11interceptor2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSypGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0K11Interceptor_pSg10Foundation3URLV011destinationS0_AL7OptionsV7optionstAZ_So17NSHTTPURLResponseCtcSgtFZ">download(_:method:parameters:encoding:headers:interceptor:to:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> using <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">Session.default</a></code> to download the contents of the specified <code>url</code> to
the provided <code>destination</code> using the <code>method</code>, <code>parameters</code>, <code>encoding</code>, and <code>headers</code> provided.</p>

<p>If <code>destination</code> is not specified, the download will be moved to a temporary location determined by Alamofire.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">download</span><span class="p">(</span><span class="n">_</span> <span class="nv">url</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                            <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                            <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                            <span class="nv">encoding</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></span> <span class="o">=</span> <span class="kt"><a href="../Structs/URLEncoding.html">URLEncoding</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                            <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                            <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                            <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>url</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>, <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></code>, <code><a href="../Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV7defaultACvpZ">URLEncoding.default</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used the determine the destination of the
           downloaded file. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO8download_6method10parameters7encoder7headers11interceptor2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0K11Interceptor_pSg10Foundation3URLV011destinationS0_AL7OptionsV7optionstAY_So17NSHTTPURLResponseCtcSgtSERzlFZ"></a>
                    <a name="//apple_ref/swift/Method/download(_:method:parameters:encoder:headers:interceptor:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO8download_6method10parameters7encoder7headers11interceptor2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0K11Interceptor_pSg10Foundation3URLV011destinationS0_AL7OptionsV7optionstAY_So17NSHTTPURLResponseCtcSgtSERzlFZ">download(_:method:parameters:encoder:headers:interceptor:to:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> using <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">Session.default</a></code> to download the contents of the specified <code>url</code> to the
provided <code>destination</code> using the <code>method</code>, encodable <code>parameters</code>, <code>encoder</code>, and <code>headers</code> provided.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>If <code>destination</code> is not specified, the download will be moved to a temporary location determined by
    Alamofire.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="n">download</span><span class="o">&lt;</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">:</span> <span class="kt">Encodable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">_</span> <span class="nv">url</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                                                   <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                                                   <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                   <span class="nv">encoder</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                                                   <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                   <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                   <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>url</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>, <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Encodable</code> parameters, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></code>, <code><a href="../Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7defaultACvpZ">URLEncodedFormParameterEncoder.default</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used the determine the destination of the
           downloaded file. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/URLRequest"></a>
                <a name="//apple_ref/swift/Section/URLRequest" class="dashAnchor"></a>
                <a href="#/URLRequest">
                  <h3 class="section-name">URLRequest</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO8download_11interceptor2toAA15DownloadRequestCAA21URLRequestConvertible_p_AA0G11Interceptor_pSg10Foundation3URLV011destinationL0_AH7OptionsV7optionstAN_So17NSHTTPURLResponseCtcSgtFZ"></a>
                    <a name="//apple_ref/swift/Method/download(_:interceptor:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO8download_11interceptor2toAA15DownloadRequestCAA21URLRequestConvertible_p_AA0G11Interceptor_pSg10Foundation3URLV011destinationL0_AH7OptionsV7optionstAN_So17NSHTTPURLResponseCtcSgtFZ">download(_:interceptor:to:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> using <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">Session.default</a></code> to execute the specified <code>urlRequest</code> and download
the result to the provided <code>destination</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">download</span><span class="p">(</span><span class="n">_</span> <span class="nv">urlRequest</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                            <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                            <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>urlRequest</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used the determine the destination of the
           downloaded file. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Resume%20Data"></a>
                <a name="//apple_ref/swift/Section/Resume Data" class="dashAnchor"></a>
                <a href="#/Resume%20Data">
                  <h3 class="section-name">Resume Data</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO8download12resumingWith11interceptor2toAA15DownloadRequestC10Foundation4DataV_AA0I11Interceptor_pSgAJ3URLV011destinationM0_AI7OptionsV7optionstAP_So17NSHTTPURLResponseCtcSgtFZ"></a>
                    <a name="//apple_ref/swift/Method/download(resumingWith:interceptor:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO8download12resumingWith11interceptor2toAA15DownloadRequestC10Foundation4DataV_AA0I11Interceptor_pSgAJ3URLV011destinationM0_AI7OptionsV7optionstAP_So17NSHTTPURLResponseCtcSgtFZ">download(resumingWith:interceptor:to:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> using the <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">Session.default</a></code> from the <code>resumeData</code> produced from a previous
<code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> cancellation to retrieve the contents of the original request and save them to the <code>destination</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>If <code>destination</code> is not specified, the download will be moved to a temporary location determined by
    Alamofire.</p>

</div>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>On some versions of all Apple platforms (iOS 10 - 10.2, macOS 10.12 - 10.12.2, tvOS 10 - 10.1, watchOS 3 - 3.1.1),
<code>resumeData</code> is broken on background URL session configurations. There&rsquo;s an underlying bug in the <code>resumeData</code>
generation logic where the data is written incorrectly and will always fail to resume the download. For more
information about the bug and possible workarounds, please refer to the <a href="http://stackoverflow.com/a/39347461/1342462">this Stack Overflow post</a>.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">download</span><span class="p">(</span><span class="n">resumingWith</span> <span class="nv">resumeData</span><span class="p">:</span> <span class="kt">Data</span><span class="p">,</span>
                            <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                            <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>resumeData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The resume <code>Data</code>. This is an opaque blob produced by <code>URLSessionDownloadTask</code> when a task is
           cancelled. See <a href="https://developer.apple.com/documentation/foundation/urlsessiondownloadtask/1411634-cancel">Apple&rsquo;s documentation</a>
           for more information.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used to determine the destination of the downloaded
           file. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Data"></a>
                <a name="//apple_ref/swift/Section/Data" class="dashAnchor"></a>
                <a href="#/Data">
                  <h3 class="section-name">Data</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload_2to6method7headers11interceptor11fileManagerAA13UploadRequestC10Foundation4DataV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0K11Interceptor_pSgSo06NSFileI0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(_:to:method:headers:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload_2to6method7headers11interceptor11fileManagerAA13UploadRequestC10Foundation4DataV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0K11Interceptor_pSgSo06NSFileI0CtFZ">upload(_:to:method:headers:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the given <code>Data</code>, <code>URLRequest</code> components, and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">,</span>
                          <span class="n">to</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                          <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                          <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Data</code> to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>.default</code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation4DataV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(_:with:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation4DataV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtFZ">upload(_:with:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the given <code>Data</code> using the <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">,</span>
                          <span class="n">with</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Data</code> to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>.default</code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/File"></a>
                <a name="//apple_ref/swift/Section/File" class="dashAnchor"></a>
                <a href="#/File">
                  <h3 class="section-name">File</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload_2to6method7headers11interceptor11fileManagerAA13UploadRequestC10Foundation3URLV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0K11Interceptor_pSgSo06NSFileI0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(_:to:method:headers:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload_2to6method7headers11interceptor11fileManagerAA13UploadRequestC10Foundation3URLV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0K11Interceptor_pSgSo06NSFileI0CtFZ">upload(_:to:method:headers:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the file at the given file <code>URL</code>, using a <code>URLRequest</code> from the provided
components and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">fileURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">,</span>
                          <span class="n">to</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                          <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                          <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>fileURL</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>URL</code> of the file to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>.default</code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation3URLV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(_:with:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation3URLV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtFZ">upload(_:with:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the file at the given file <code>URL</code> using the <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and
<code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">fileURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">,</span>
                          <span class="n">with</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>fileURL</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>URL</code> of the file to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>.default</code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/InputStream"></a>
                <a name="//apple_ref/swift/Section/InputStream" class="dashAnchor"></a>
                <a href="#/InputStream">
                  <h3 class="section-name">InputStream</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload_2to6method7headers11interceptor11fileManagerAA13UploadRequestCSo13NSInputStreamC_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0K11Interceptor_pSgSo06NSFileI0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(_:to:method:headers:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload_2to6method7headers11interceptor11fileManagerAA13UploadRequestCSo13NSInputStreamC_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0K11Interceptor_pSgSo06NSFileI0CtFZ">upload(_:to:method:headers:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> from the <code>InputStream</code> provided using a <code>URLRequest</code> from the provided components and
<code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">stream</span><span class="p">:</span> <span class="kt">InputStream</span><span class="p">,</span>
                          <span class="n">to</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                          <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                          <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>InputStream</code> that provides the data to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>.default</code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload_4with11interceptor11fileManagerAA13UploadRequestCSo13NSInputStreamC_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(_:with:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload_4with11interceptor11fileManagerAA13UploadRequestCSo13NSInputStreamC_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtFZ">upload(_:with:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> from the provided <code>InputStream</code> using the <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and
<code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">stream</span><span class="p">:</span> <span class="kt">InputStream</span><span class="p">,</span>
                          <span class="n">with</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>InputStream</code> that provides the data to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>.default</code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/MultipartFormData"></a>
                <a name="//apple_ref/swift/Section/MultipartFormData" class="dashAnchor"></a>
                <a href="#/MultipartFormData">
                  <h3 class="section-name">MultipartFormData</h3>
                </a>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManagerAA13UploadRequestCyAA09MultiparteF0Cc_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0P11Interceptor_pSgSo06NSFileN0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManagerAA13UploadRequestCyAA09MultiparteF0Cc_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0P11Interceptor_pSgSo06NSFileN0CtFZ">upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the multipart form data built using a closure and sent using the provided
<code>URLRequest</code> components and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="p">(</span><span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">,</span>
                          <span class="n">to</span> <span class="nv">url</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                          <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                          <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                          <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> building closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> to be used if the form data exceeds the memory threshold and is
                     written to disk before being uploaded. <code>.default</code> instance by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCyAA09MultiparteF0Cc_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCyAA09MultiparteF0Cc_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtFZ">upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> using a <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> building closure, the provided <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code>
value, and a <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="p">(</span><span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">,</span>
                          <span class="n">with</span> <span class="nv">request</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                          <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> building closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>request</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> to be used if the form data exceeds the memory threshold and is
                     written to disk before being uploaded. <code>.default</code> instance by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManagerAA13UploadRequestCAA09MultiparteF0C_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0P11Interceptor_pSgSo06NSFileN0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManagerAA13UploadRequestCAA09MultiparteF0C_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0P11Interceptor_pSgSo06NSFileN0CtFZ">upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the prebuilt <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> value using the provided <code>URLRequest</code> components
and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">,</span>
                          <span class="n">to</span> <span class="nv">url</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                          <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                          <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                          <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> instance to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>url</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> to be used if the form data exceeds the memory threshold and is
                     written to disk before being uploaded. <code>.default</code> instance by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire2AFO6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCAA09MultiparteF0C_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtFZ"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire2AFO6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCAA09MultiparteF0C_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtFZ">upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the prebuilt <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> value using the providing <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code>
value and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">,</span>
                          <span class="n">with</span> <span class="nv">request</span><span class="p">:</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                          <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                          <span class="nv">interceptor</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> instance to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>request</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>.default</code> instance by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2019 <a class="link" href="http://alamofire.org/" target="_blank" rel="external">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2019-10-26)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external">jazzy ♪♫ v0.11.2</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external">Realm</a> project.</p>
    </section>
  </body>
</div>
</html>
