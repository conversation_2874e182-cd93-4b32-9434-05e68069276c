<!DOCTYPE html>
<html lang="en">
  <head>
    <title>DataStreamRequest Class Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Class/DataStreamRequest" class="dashAnchor"></a>

    <a title="DataStreamRequest Class Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Classes.html">Classes</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      DataStreamRequest Class Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>DataStreamRequest</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DataStreamRequest</span> <span class="p">:</span> <span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                </div>
              </div>
            <p><code><a href="../Classes/Request.html">Request</a></code> subclass which streams HTTP response <code>Data</code> through a <code>Handler</code> closure.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC7Handlera"></a>
                    <a name="//apple_ref/swift/Alias/Handler" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC7Handlera">Handler</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Closure type handling <code><a href="../Classes/DataStreamRequest/Stream.html">DataStreamRequest.Stream</a></code> values.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">typealias</span> <span class="kt">Handler</span><span class="o">&lt;</span><span class="kt">Success</span><span class="p">,</span> <span class="kt">Failure</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Classes/DataStreamRequest/Stream.html">Stream</a></span><span class="o">&lt;</span><span class="kt">Success</span><span class="p">,</span> <span class="kt">Failure</span><span class="o">&gt;</span><span class="p">)</span> <span class="k">throws</span> <span class="o">-&gt;</span> <span class="kt">Void</span> <span class="k">where</span> <span class="kt">Failure</span> <span class="p">:</span> <span class="kt">Error</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC0C0V"></a>
                    <a name="//apple_ref/swift/Struct/Stream" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC0C0V">Stream</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Type encapsulating an <code><a href="../Classes/DataStreamRequest/Event.html">Event</a></code> as it flows through the stream, as well as a <code><a href="../Classes/DataStreamRequest/CancellationToken.html">CancellationToken</a></code> which can be used
to stop the stream at any time.</p>

                        <a href="../Classes/DataStreamRequest/Stream.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">Stream</span><span class="o">&lt;</span><span class="kt">Success</span><span class="p">,</span> <span class="kt">Failure</span><span class="o">&gt;</span> <span class="p">:</span> <span class="kt">Sendable</span> <span class="k">where</span> <span class="kt">Success</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">Failure</span> <span class="p">:</span> <span class="kt">Error</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC5EventO"></a>
                    <a name="//apple_ref/swift/Enum/Event" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC5EventO">Event</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Type representing an event flowing through the stream. Contains either the <code>Result</code> of processing streamed
<code>Data</code> or the completion of the stream.</p>

                        <a href="../Classes/DataStreamRequest/Event.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">Event</span><span class="o">&lt;</span><span class="kt">Success</span><span class="p">,</span> <span class="kt">Failure</span><span class="o">&gt;</span> <span class="p">:</span> <span class="kt">Sendable</span> <span class="k">where</span> <span class="kt">Success</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">Failure</span> <span class="p">:</span> <span class="kt">Error</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC10CompletionV"></a>
                    <a name="//apple_ref/swift/Struct/Completion" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC10CompletionV">Completion</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Value containing the state of a <code><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></code> when the stream was completed.</p>

                        <a href="../Classes/DataStreamRequest/Completion.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">Completion</span> <span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC17CancellationTokenV"></a>
                    <a name="//apple_ref/swift/Struct/CancellationToken" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC17CancellationTokenV">CancellationToken</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Type used to cancel an ongoing stream.</p>

                        <a href="../Classes/DataStreamRequest/CancellationToken.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">CancellationToken</span> <span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC11convertibleAA21URLRequestConvertible_pvp"></a>
                    <a name="//apple_ref/swift/Property/convertible" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC11convertibleAA21URLRequestConvertible_pvp">convertible</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value used to create <code>URLRequest</code>s for this instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC021automaticallyCancelOnC5ErrorSbvp"></a>
                    <a name="//apple_ref/swift/Property/automaticallyCancelOnStreamError" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC021automaticallyCancelOnC5ErrorSbvp">automaticallyCancelOnStreamError</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Whether or not the instance will be cancelled if stream parsing encounters an error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">automaticallyCancelOnStreamError</span><span class="p">:</span> <span class="kt">Bool</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCtYbcF"></a>
                    <a name="//apple_ref/swift/Method/validate(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCtYbcF">validate(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates the <code>URLRequest</code> and <code>HTTPURLResponse</code> received for the instance using the provided <code><a href="../Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC10Validationa">Validation</a></code> closure.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">(</span><span class="n">_</span> <span class="nv">validation</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC10Validationa">Validation</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>validation</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC10Validationa">Validation</a></code> closure used to validate the request and response.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>DataStreamRequest</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC07asInputC010bufferSizeSo07NSInputC0CSgSi_tF"></a>
                    <a name="//apple_ref/swift/Method/asInputStream(bufferSize:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC07asInputC010bufferSizeSo07NSInputC0CSgSi_tF">asInputStream(bufferSize:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Produces an <code>InputStream</code> that receives the <code>Data</code> received by the instance.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>The <code>InputStream</code> produced by this method must have <code>open()</code> called before being able to read <code>Data</code>.
    Additionally, this method will automatically call <code>resume()</code> on the instance, regardless of whether or
    not the creating session has <code>startRequestsImmediately</code> set to <code>true</code>.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">asInputStream</span><span class="p">(</span><span class="nv">bufferSize</span><span class="p">:</span> <span class="kt">Int</span> <span class="o">=</span> <span class="mi">1024</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">InputStream</span><span class="p">?</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>bufferSize</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Size, in bytes, of the buffer between the <code>OutputStream</code> and <code>InputStream</code>.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>InputStream</code> bound to the internal <code>OutboundStream</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC14onHTTPResponse0E07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseC_yAA0D0C19ResponseDispositionOctctF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(on:perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC14onHTTPResponse0E07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseC_yAA0D0C19ResponseDispositionOctctF">onHTTPResponse(on:<wbr>perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets a closure called whenever the <code><a href="../Classes/DataRequest.html">DataRequest</a></code> produces an <code>HTTPURLResponse</code> and providing a completion
handler to return a <code>ResponseDisposition</code> value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@_disfavoredOverload</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span>
    <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
    <span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">response</span><span class="p">:</span> <span class="kt">HTTPURLResponse</span><span class="p">,</span>
                                          <span class="n">_</span> <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">ResponseDisposition</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the closure will be called. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Closure called when the instance produces an <code>HTTPURLResponse</code>. The <code>completionHandler</code> provided
     MUST be called, otherwise the request will never complete.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC14onHTTPResponse0E07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseCctF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(on:perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC14onHTTPResponse0E07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseCctF">onHTTPResponse(on:<wbr>perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets a closure called whenever the <code><a href="../Classes/DataRequest.html">DataRequest</a></code> produces an <code>HTTPURLResponse</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span><span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                           <span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the closure will be called. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Closure called when the instance produces an <code>HTTPURLResponse</code>.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Response%20Serialization"></a>
                <a name="//apple_ref/swift/Section/Response Serialization" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Response%20Serialization"></a>
                  <h3 class="section-name"><span>Response Serialization</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC08responseC02on6streamACXDSo17OS_dispatch_queueC_yAC0C0Vy_10Foundation0B0Vs5NeverOGKctF"></a>
                    <a name="//apple_ref/swift/Method/responseStream(on:stream:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC08responseC02on6streamACXDSo17OS_dispatch_queueC_yAC0C0Vy_10Foundation0B0Vs5NeverOGKctF">responseStream(on:<wbr>stream:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a <code>StreamHandler</code> which performs no parsing on incoming <code>Data</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseStream</span><span class="p">(</span><span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span> <span class="nv">stream</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC7Handlera">Handler</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="p">,</span> <span class="kt">Never</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform <code>StreamHandler</code> closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>StreamHandler</code> closure called as <code>Data</code> is received. May be called multiple times.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>DataStreamRequest</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC08responseC05using2on6streamACXDx_So17OS_dispatch_queueCyAC0C0Vy_16SerializedObjectQzAA7AFErrorOGKctAA0bC10SerializerRzlF"></a>
                    <a name="//apple_ref/swift/Method/responseStream(using:on:stream:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC08responseC05using2on6streamACXDx_So17OS_dispatch_queueCyAC0C0Vy_16SerializedObjectQzAA7AFErrorOGKctAA0bC10SerializerRzlF">responseStream(using:<wbr>on:<wbr>stream:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a <code>StreamHandler</code> which uses the provided <code><a href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a></code> to process incoming <code>Data</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">responseStream</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a></span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                             <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                                             <span class="nv">stream</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC7Handlera">Handler</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="p">,</span> <span class="kt"><a href="../Enums/AFError.html">AFError</a></span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a></code> used to process incoming <code>Data</code>. Its work is done on the <code>serializationQueue</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform <code>StreamHandler</code> closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>StreamHandler</code> closure called as <code>Data</code> is received. May be called multiple times.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>DataStreamRequest</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC08responseC6String2on6streamACXDSo17OS_dispatch_queueC_yAC0C0Vy_SSs5NeverOGKctF"></a>
                    <a name="//apple_ref/swift/Method/responseStreamString(on:stream:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC08responseC6String2on6streamACXDSo17OS_dispatch_queueC_yAC0C0Vy_SSs5NeverOGKctF">responseStreamString(on:<wbr>stream:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a <code>StreamHandler</code> which parses incoming <code>Data</code> as a UTF8 <code>String</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseStreamString</span><span class="p">(</span><span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                 <span class="nv">stream</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC7Handlera">Handler</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="p">,</span> <span class="kt">Never</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform <code>StreamHandler</code> closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>StreamHandler</code> closure called as <code>Data</code> is received. May be called multiple times.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>DataStreamRequest</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC08responseC9Decodable2of2on5using12preprocessor6streamACXDxm_So17OS_dispatch_queueCAA0B7Decoder_pAA0B12Preprocessor_pyAC0C0Vy_xAA7AFErrorOGKctSeRzlF"></a>
                    <a name="//apple_ref/swift/Method/responseStreamDecodable(of:on:using:preprocessor:stream:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC08responseC9Decodable2of2on5using12preprocessor6streamACXDxm_So17OS_dispatch_queueCAA0B7Decoder_pAA0B12Preprocessor_pyAC0C0Vy_xAA7AFErrorOGKctSeRzlF">responseStreamDecodable(of:<wbr>on:<wbr>using:<wbr>preprocessor:<wbr>stream:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a <code>StreamHandler</code> which parses incoming <code>Data</code> using the provided <code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">responseStreamDecodable</span><span class="o">&lt;</span><span class="kt">T</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">of</span> <span class="nv">type</span><span class="p">:</span> <span class="kt">T</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">T</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                                  <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                                  <span class="n">using</span> <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                                  <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a></span><span class="p">(),</span>
                                                  <span class="nv">stream</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Classes/DataStreamRequest.html#/s:9Alamofire17DataStreamRequestC7Handlera">Handler</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="p">,</span> <span class="kt"><a href="../Enums/AFError.html">AFError</a></span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">T</span><span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to parse incoming <code>Data</code> into.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform <code>StreamHandler</code> closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> used to decode the incoming <code>Data</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> used to process the incoming <code>Data</code> before it&rsquo;s passed to the <code>decoder</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>StreamHandler</code> closure called as <code>Data</code> is received. May be called multiple times.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>DataStreamRequest</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataRequest%20%2F%20UploadRequest"></a>
                <a name="//apple_ref/swift/Section/DataRequest / UploadRequest" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataRequest%20%2F%20UploadRequest"></a>
                  <h3 class="section-name"><span>DataRequest / UploadRequest</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC07publishC05using2onAA0bC9PublisherVy16SerializedObjectQzGx_So17OS_dispatch_queueCtAA0bC10SerializerRzlF"></a>
                    <a name="//apple_ref/swift/Method/publishStream(using:on:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC07publishC05using2onAA0bC9PublisherVy16SerializedObjectQzGx_So17OS_dispatch_queueCtAA0bC10SerializerRzlF">publishStream(using:<wbr>on:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code> for this instance using the given <code><a href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a></code> and <code>DispatchQueue</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishStream</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a></span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                            <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a></code> used to serialize the streamed <code>Data</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code>DataRequest.Stream</code> values will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC07publishB05queueAA0bC9PublisherVy10Foundation0B0VGSo012OS_dispatch_F0C_tF"></a>
                    <a name="//apple_ref/swift/Method/publishData(queue:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC07publishB05queueAA0bC9PublisherVy10Foundation0B0VGSo012OS_dispatch_F0C_tF">publishData(queue:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code> for this instance which uses a <code><a href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a></code> to stream <code>Data</code>
unserialized.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishData</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code>DataRequest.Stream</code> values will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC13publishString5queueAA0bC9PublisherVySSGSo012OS_dispatch_G0C_tF"></a>
                    <a name="//apple_ref/swift/Method/publishString(queue:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC13publishString5queueAA0bC9PublisherVySSGSo012OS_dispatch_G0C_tF">publishString(queue:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code> for this instance which uses a <code><a href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a></code> to serialize stream
<code>Data</code> values into <code>String</code> values.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishString</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code>DataRequest.Stream</code> values will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC16publishDecodable4type5queue7decoder12preprocessorAA0bC9PublisherVyxGxm_So012OS_dispatch_H0CAA0B7Decoder_pAA0B12Preprocessor_ptSeRzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/publishDecodable(type:queue:decoder:preprocessor:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC16publishDecodable4type5queue7decoder12preprocessorAA0bC9PublisherVyxGxm_So012OS_dispatch_H0CAA0B7Decoder_pAA0B12Preprocessor_ptSeRzs8SendableRzlF">publishDecodable(type:<wbr>queue:<wbr>decoder:<wbr>preprocessor:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code> for this instance which uses a <code><a href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a></code> with the provided
parameters to serialize stream <code>Data</code> values into the provided type.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishDecodable</span><span class="o">&lt;</span><span class="kt">T</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="nv">type</span><span class="p">:</span> <span class="kt">T</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">T</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                           <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                           <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                           <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a></span><span class="p">())</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to which to decode stream <code>Data</code>. Inferred from the context by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code>DataRequest.Stream</code> values will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> instance used to decode stream <code>Data</code>. <code>JSONDecoder()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which filters incoming stream <code>Data</code> before serialization.
          <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataStreamTask"></a>
                <a name="//apple_ref/swift/Section/DataStreamTask" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataStreamTask"></a>
                  <h3 class="section-name"><span>DataStreamTask</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC13httpResponses15bufferingPolicyAA0C2OfVySo17NSHTTPURLResponseCGScS12ContinuationV09BufferingH0OyAI__G_tF"></a>
                    <a name="//apple_ref/swift/Method/httpResponses(bufferingPolicy:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC13httpResponses15bufferingPolicyAA0C2OfVySo17NSHTTPURLResponseCGScS12ContinuationV09BufferingH0OyAI__G_tF">httpResponses(bufferingPolicy:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/StreamOf.html">StreamOf&lt;HTTPURLResponse&gt;</a></code> for the instance&rsquo;s responses.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">httpResponses</span><span class="p">(</span><span class="nv">bufferingPolicy</span><span class="p">:</span> <span class="kt"><a href="../Structs/StreamOf.html">StreamOf</a></span><span class="o">&lt;</span><span class="kt">HTTPURLResponse</span><span class="o">&gt;.</span><span class="kt">BufferingPolicy</span> <span class="o">=</span> <span class="o">.</span><span class="n">unbounded</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/StreamOf.html">StreamOf</a></span><span class="o">&lt;</span><span class="kt">HTTPURLResponse</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>bufferingPolicy</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>BufferingPolicy</code> that determines the stream&rsquo;s buffering behavior.<code>.unbounded</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/StreamOf.html">StreamOf&lt;HTTPURLResponse&gt;</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC14onHTTPResponse7performACXDAA0D0C19ResponseDispositionOSo17NSHTTPURLResponseCYaYbc_tF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC14onHTTPResponse7performACXDAA0D0C19ResponseDispositionOSo17NSHTTPURLResponseCYaYbc_tF">onHTTPResponse(perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets an async closure returning a <code><a href="../Classes/Request/ResponseDisposition.html">Request.ResponseDisposition</a></code>, called whenever the <code>DataStreamRequest</code>
produces an <code>HTTPURLResponse</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>Most requests will only produce a single response for each outgoing attempt (initial + retries).
    However, some types of response may trigger multiple <code>HTTPURLResponse</code>s, such as multipart streams,
    where responses after the first will contain the part headers.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@_disfavoredOverload</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span><span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="k">async</span> <span class="o">-&gt;</span> <span class="kt">ResponseDisposition</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Async closure executed when a new <code>HTTPURLResponse</code> is received and returning a
     <code>ResponseDisposition</code> value. This value determines whether to continue the request or cancel it as
     if <code>cancel()</code> had been called on the instance. Note, this closure is called on an arbitrary thread,
     so any synchronous calls in it will execute in that context.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC14onHTTPResponse7performACXDySo17NSHTTPURLResponseCYaYbc_tF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC14onHTTPResponse7performACXDySo17NSHTTPURLResponseCYaYbc_tF">onHTTPResponse(perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets an async closure called whenever the <code>DataStreamRequest</code> produces an <code>HTTPURLResponse</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>Most requests will only produce a single response for each outgoing attempt (initial + retries).
    However, some types of response may trigger multiple <code>HTTPURLResponse</code>s, such as multipart streams,
    where responses after the first will contain the part headers.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span><span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="k">async</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Async closure executed when a new <code>HTTPURLResponse</code> is received. Note, this closure is called on an
     arbitrary thread, so any synchronous calls in it will execute in that context.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC10streamTaskAA0bcF0VyF"></a>
                    <a name="//apple_ref/swift/Method/streamTask()" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC10streamTaskAA0bcF0VyF">streamTask()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataStreamTask.html">DataStreamTask</a></code> used to <code>await</code> streams of serialized values.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">streamTask</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataStreamTask.html">DataStreamTask</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataStreamTask.html">DataStreamTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC10Validationa"></a>
                    <a name="//apple_ref/swift/Alias/Validation" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC10Validationa">Validation</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A closure used to validate a request that takes a <code>URLRequest</code> and <code>HTTPURLResponse</code> and returns whether the
request was valid.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">typealias</span> <span class="kt">Validation</span> <span class="o">=</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">request</span><span class="p">:</span> <span class="kt">URLRequest</span><span class="p">?,</span> <span class="n">_</span> <span class="nv">response</span><span class="p">:</span> <span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">ValidationResult</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF"></a>
                    <a name="//apple_ref/swift/Method/validate(statusCode:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF">validate(statusCode:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a status code in the specified sequence.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">validate</span><span class="o">&lt;</span><span class="kt">S</span><span class="o">&gt;</span><span class="p">(</span><span class="n">statusCode</span> <span class="nv">acceptableStatusCodes</span><span class="p">:</span> <span class="kt">S</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sequence</span><span class="p">,</span> <span class="kt">S</span><span class="o">.</span><span class="kt">Element</span> <span class="o">==</span> <span class="kt">Int</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>acceptableStatusCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Sequence</code> of acceptable response status codes.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF"></a>
                    <a name="//apple_ref/swift/Method/validate(contentType:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF">validate(contentType:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a content type in the specified sequence.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">validate</span><span class="o">&lt;</span><span class="kt">S</span><span class="o">&gt;</span><span class="p">(</span><span class="n">contentType</span> <span class="nv">acceptableContentTypes</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="kd">@autoclosure</span> <span class="p">()</span> <span class="o">-&gt;</span> <span class="kt">S</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sequence</span><span class="p">,</span> <span class="kt">S</span><span class="o">.</span><span class="kt">Element</span> <span class="o">==</span> <span class="kt">String</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>contentType</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The acceptable content types, which may specify wildcard types and/or subtypes.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC8validateACXDyF"></a>
                    <a name="//apple_ref/swift/Method/validate()" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC8validateACXDyF">validate()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a status code in the default acceptable range of 200&hellip;299, and that the content
type matches any specified in the Accept HTTP header field.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
