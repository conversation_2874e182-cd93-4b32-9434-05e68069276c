<!DOCTYPE html>
<html lang="en">
  <head>
    <title>DataRequest Class Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Class/DataRequest" class="dashAnchor"></a>

    <a title="DataRequest Class Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Classes.html">Classes</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      DataRequest Class Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>DataRequest</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">class</span> <span class="kt">DataRequest</span> <span class="p">:</span> <span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                </div>
              </div>
            <p><code><a href="../Classes/Request.html">Request</a></code> subclass which handles in-memory <code>Data</code> download using <code>URLSessionDataTask</code>.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC11convertibleAA21URLRequestConvertible_pvp"></a>
                    <a name="//apple_ref/swift/Property/convertible" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC11convertibleAA21URLRequestConvertible_pvp">convertible</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value used to create <code>URLRequest</code>s for this instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC4data10Foundation0B0VSgvp"></a>
                    <a name="//apple_ref/swift/Property/data" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC4data10Foundation0B0VSgvp">data</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>Data</code> read from the server so far.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCAI0B0VSgtcF"></a>
                    <a name="//apple_ref/swift/Method/validate(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCAI0B0VSgtcF">validate(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates the request, using the specified closure.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">(</span><span class="n">_</span> <span class="nv">validation</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Classes/DataRequest.html#/s:9Alamofire11DataRequestC10Validationa">Validation</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>validation</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DataRequest.html#/s:9Alamofire11DataRequestC10Validationa">Validation</a></code> closure used to validate the response.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC14onHTTPResponse0D07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseC_yAA0C0C19ResponseDispositionOctctF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(on:perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC14onHTTPResponse0D07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseC_yAA0C0C19ResponseDispositionOctctF">onHTTPResponse(on:<wbr>perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets a closure called whenever the <code>DataRequest</code> produces an <code>HTTPURLResponse</code> and providing a completion
handler to return a <code>ResponseDisposition</code> value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@_disfavoredOverload</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span>
    <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
    <span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">response</span><span class="p">:</span> <span class="kt">HTTPURLResponse</span><span class="p">,</span>
                                          <span class="n">_</span> <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">ResponseDisposition</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the closure will be called. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Closure called when the instance produces an <code>HTTPURLResponse</code>. The <code>completionHandler</code> provided
     MUST be called, otherwise the request will never complete.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC14onHTTPResponse0D07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseCctF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(on:perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC14onHTTPResponse0D07performACXDSo17OS_dispatch_queueC_ySo17NSHTTPURLResponseCctF">onHTTPResponse(on:<wbr>perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets a closure called whenever the <code>DataRequest</code> produces an <code>HTTPURLResponse</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span><span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                           <span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the closure will be called. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Closure called when the instance produces an <code>HTTPURLResponse</code>.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Response%20Serialization"></a>
                <a name="//apple_ref/swift/Section/Response Serialization" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Response%20Serialization"></a>
                  <h3 class="section-name"><span>Response Serialization</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC8response5queue17completionHandlerACXDSo012OS_dispatch_E0C_yAA0B8ResponseVy10Foundation0B0VSgAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/response(queue:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC8response5queue17completionHandlerACXDSo012OS_dispatch_E0C_yAA0B8ResponseVy10Foundation0B0VSgAA7AFErrorOGctF">response(queue:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">response</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span> <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="p">?</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The code to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGctAA0bkF8ProtocolRzlF"></a>
                    <a name="//apple_ref/swift/Method/response(queue:responseSerializer:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGctAA0bkF8ProtocolRzlF">response(queue:<wbr>responseSerializer:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">response</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a></span><span class="o">&gt;</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                                                 <span class="nv">responseSerializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                                 <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>responseSerializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The response serializer responsible for serializing the request, response, and data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The code to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGctAA0kF0RzlF"></a>
                    <a name="//apple_ref/swift/Method/response(queue:responseSerializer:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGctAA0kF0RzlF">response(queue:<wbr>responseSerializer:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">response</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></span><span class="o">&gt;</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                                     <span class="nv">responseSerializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                     <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>responseSerializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The response serializer responsible for serializing the request, response, and data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The code to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC08responseB05queue16dataPreprocessor18emptyResponseCodes0hC7Methods17completionHandlerACXDSo012OS_dispatch_E0C_AA0bG0_pShySiGShyAA10HTTPMethodVGyAA0bI0Vy10Foundation0B0VAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/responseData(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC08responseB05queue16dataPreprocessor18emptyResponseCodes0hC7Methods17completionHandlerACXDSo012OS_dispatch_E0C_AA0bG0_pShySiGShyAA10HTTPMethodVGyAA0bI0Vy10Foundation0B0VAA7AFErrorOGctF">responseData(queue:<wbr>dataPreprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></code> to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseData</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                         <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                         <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                         <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                         <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is called. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC14responseString5queue16dataPreprocessor8encoding18emptyResponseCodes0jC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA0bH0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGyAA0bK0VySSAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/responseString(queue:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC14responseString5queue16dataPreprocessor8encoding18emptyResponseCodes0jC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA0bH0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGyAA0bK0VySSAA7AFErrorOGctF">responseString(queue:<wbr>dataPreprocessor:<wbr>encoding:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></code> to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseString</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                           <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                           <span class="nv">encoding</span><span class="p">:</span> <span class="kt">String</span><span class="o">.</span><span class="kt">Encoding</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                           <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                           <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                           <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The string encoding. Defaults to <code>nil</code>, in which case the encoding will be determined
                 from the server response, falling back to the default HTTP character set, <code>ISO-8859-1</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC12responseJSON5queue16dataPreprocessor18emptyResponseCodes0iC7Methods7options17completionHandlerACXDSo012OS_dispatch_F0C_AA0bH0_pShySiGShyAA10HTTPMethodVGSo20NSJSONReadingOptionsVyAA0bJ0VyypAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/responseJSON(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:options:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC12responseJSON5queue16dataPreprocessor18emptyResponseCodes0iC7Methods7options17completionHandlerACXDSo012OS_dispatch_F0C_AA0bH0_pShySiGShyAA10HTTPMethodVGSo20NSJSONReadingOptionsVyAA0bJ0VyypAA7AFErrorOGctF">responseJSON(queue:<wbr>dataPreprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>options:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></code> to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="o">*</span><span class="p">,</span> <span class="n">deprecated</span><span class="p">,</span> <span class="nv">message</span><span class="p">:</span> <span class="s">"responseJSON deprecated and will be removed in Alamofire 6. Use responseDecodable instead."</span><span class="p">)</span>
<span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseJSON</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                         <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                         <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                         <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                         <span class="nv">options</span><span class="p">:</span> <span class="kt">JSONSerialization</span><span class="o">.</span><span class="kt">ReadingOptions</span> <span class="o">=</span> <span class="o">.</span><span class="n">allowFragments</span><span class="p">,</span>
                         <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a></span><span class="o">&lt;</span><span class="kt">Any</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>options</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>JSONSerialization.ReadingOptions</code> used when parsing the response. <code>.allowFragments</code>
                 by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC17responseDecodable2of5queue16dataPreprocessor7decoder18emptyResponseCodes0kC7Methods17completionHandlerACXDxm_So012OS_dispatch_G0CAA0bI0_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGyAA0bL0VyxAA7AFErrorOGctSeRzlF"></a>
                    <a name="//apple_ref/swift/Method/responseDecodable(of:queue:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC17responseDecodable2of5queue16dataPreprocessor7decoder18emptyResponseCodes0kC7Methods17completionHandlerACXDxm_So012OS_dispatch_G0CAA0bI0_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGyAA0bL0VyxAA7AFErrorOGctSeRzlF">responseDecodable(of:<wbr>queue:<wbr>dataPreprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></code> to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">responseDecodable</span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;</span><span class="p">(</span><span class="n">of</span> <span class="nv">type</span><span class="p">:</span> <span class="kt">Value</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">Value</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                     <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                     <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                     <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                     <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                     <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                                     <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">Value</span><span class="p">:</span> <span class="kt">Decodable</span><span class="p">,</span> <span class="kt">Value</span><span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to decode from response data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> to use to decode the response. <code>JSONDecoder()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataRequest%20%2F%20UploadRequest"></a>
                <a name="//apple_ref/swift/Section/DataRequest / UploadRequest" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataRequest%20%2F%20UploadRequest"></a>
                  <h3 class="section-name"><span>DataRequest / UploadRequest</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0E10SerializerRz16SerializedObjectQzRs_r0_lF"></a>
                    <a name="//apple_ref/swift/Method/publishResponse(using:on:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0E10SerializerRz16SerializedObjectQzRs_r0_lF">publishResponse(using:<wbr>on:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code> for this instance using the given <code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> and <code>DispatchQueue</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishResponse</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></span><span class="p">,</span> <span class="kt">T</span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span> <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span>
    <span class="k">where</span> <span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span> <span class="o">==</span> <span class="kt">T</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> used to serialize response <code>Data</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DataResponse.html">DataResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC07publishB05queue12preprocessor18emptyResponseCodes0gC7MethodsAA0bH9PublisherVy10Foundation0B0VGSo012OS_dispatch_E0C_AA0B12Preprocessor_pShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/publishData(queue:preprocessor:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC07publishB05queue12preprocessor18emptyResponseCodes0gC7MethodsAA0bH9PublisherVy10Foundation0B0VGSo012OS_dispatch_E0C_AA0B12Preprocessor_pShySiGShyAA10HTTPMethodVGtF">publishData(queue:<wbr>preprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code> for this instance and uses a <code><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></code> to serialize the
response.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishData</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                        <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                        <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                        <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DataResponse.html">DataResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which filters the <code>Data</code> before serialization. <code>PassthroughPreprocessor()</code>
                 by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;Int&gt;</code> of HTTP status codes for which empty responses are allowed. <code>[204, 205]</code> by
                 default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;HTTPMethod&gt;</code> of <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are allowed, regardless of
                 status code. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC13publishString5queue12preprocessor8encoding18emptyResponseCodes0iC7MethodsAA0bJ9PublisherVySSGSo012OS_dispatch_F0C_AA0B12Preprocessor_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/publishString(queue:preprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC13publishString5queue12preprocessor8encoding18emptyResponseCodes0iC7MethodsAA0bJ9PublisherVySSGSo012OS_dispatch_F0C_AA0B12Preprocessor_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF">publishString(queue:<wbr>preprocessor:<wbr>encoding:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code> for this instance and uses a <code><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></code> to serialize the
response.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishString</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                          <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                          <span class="nv">encoding</span><span class="p">:</span> <span class="kt">String</span><span class="o">.</span><span class="kt">Encoding</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                          <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DataResponse.html">DataResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which filters the <code>Data</code> before serialization. <code>PassthroughPreprocessor()</code>
                 by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>String.Encoding</code> to parse the response. <code>nil</code> by default, in which case the encoding
                 will be determined by the server response, falling back to the default HTTP character
                 set, <code>ISO-8859-1</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;Int&gt;</code> of HTTP status codes for which empty responses are allowed. <code>[204, 205]</code> by
                 default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;HTTPMethod&gt;</code> of <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are allowed, regardless of
                 status code. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jK7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA0B12Preprocessor_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyResponseMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jK7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA0B12Preprocessor_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF">publishDecodable(type:<wbr>queue:<wbr>preprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyResponseMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Undocumented</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@_disfavoredOverload</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishDecodable</span><span class="o">&lt;</span><span class="kt">T</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="nv">type</span><span class="p">:</span> <span class="kt">T</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">T</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                           <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                           <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                           <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                           <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                           <span class="nv">emptyResponseMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jC7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA0B12Preprocessor_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jC7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA0B12Preprocessor_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF">publishDecodable(type:<wbr>queue:<wbr>preprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code> for this instance and uses a <code><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></code> to serialize the
response.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishDecodable</span><span class="o">&lt;</span><span class="kt">T</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="nv">type</span><span class="p">:</span> <span class="kt">T</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">T</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                           <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                           <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                           <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                           <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                           <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to which to decode response <code>Data</code>. Inferred from the context by
                 default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DataResponse.html">DataResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which filters the <code>Data</code> before serialization.
                 <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> instance used to decode response <code>Data</code>. <code>JSONDecoder()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;Int&gt;</code> of HTTP status codes for which empty responses are allowed. <code>[204, 205]</code> by
                 default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;HTTPMethod&gt;</code> of <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are allowed, regardless of
                 status code. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC19publishUnserialized5queueAA0B17ResponsePublisherVy10Foundation0B0VSgGSo012OS_dispatch_F0C_tF"></a>
                    <a name="//apple_ref/swift/Method/publishUnserialized(queue:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC19publishUnserialized5queueAA0B17ResponsePublisherVy10Foundation0B0VSgGSo012OS_dispatch_F0C_tF">publishUnserialized(queue:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code> for this instance which does not serialize the response before publishing.</p>

<ul>
<li><p>queue: <code>DispatchQueue</code> on which the <code><a href="../Structs/DataResponse.html">DataResponse</a></code> will be published. <code>.main</code> by default.</p></li>
</ul>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishUnserialized</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="p">?</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataTask"></a>
                <a name="//apple_ref/swift/Section/DataTask" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataTask"></a>
                  <h3 class="section-name"><span>DataTask</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC13httpResponses15bufferingPolicyAA8StreamOfVySo17NSHTTPURLResponseCGScS12ContinuationV09BufferingG0OyAI__G_tF"></a>
                    <a name="//apple_ref/swift/Method/httpResponses(bufferingPolicy:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC13httpResponses15bufferingPolicyAA8StreamOfVySo17NSHTTPURLResponseCGScS12ContinuationV09BufferingG0OyAI__G_tF">httpResponses(bufferingPolicy:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/StreamOf.html">StreamOf&lt;HTTPURLResponse&gt;</a></code> for the instance&rsquo;s responses.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">httpResponses</span><span class="p">(</span><span class="nv">bufferingPolicy</span><span class="p">:</span> <span class="kt"><a href="../Structs/StreamOf.html">StreamOf</a></span><span class="o">&lt;</span><span class="kt">HTTPURLResponse</span><span class="o">&gt;.</span><span class="kt">BufferingPolicy</span> <span class="o">=</span> <span class="o">.</span><span class="n">unbounded</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/StreamOf.html">StreamOf</a></span><span class="o">&lt;</span><span class="kt">HTTPURLResponse</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>bufferingPolicy</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>BufferingPolicy</code> that determines the stream&rsquo;s buffering behavior.<code>.unbounded</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/StreamOf.html">StreamOf&lt;HTTPURLResponse&gt;</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC14onHTTPResponse7performACXDAA0C0C19ResponseDispositionOSo17NSHTTPURLResponseCYaYbc_tF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC14onHTTPResponse7performACXDAA0C0C19ResponseDispositionOSo17NSHTTPURLResponseCYaYbc_tF">onHTTPResponse(perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets an async closure returning a <code><a href="../Classes/Request/ResponseDisposition.html">Request.ResponseDisposition</a></code>, called whenever the <code>DataRequest</code> produces an
<code>HTTPURLResponse</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>Most requests will only produce a single response for each outgoing attempt (initial + retries).
    However, some types of response may trigger multiple <code>HTTPURLResponse</code>s, such as multipart streams,
    where responses after the first will contain the part headers.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@_disfavoredOverload</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span>
    <span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">response</span><span class="p">:</span> <span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="k">async</span> <span class="o">-&gt;</span> <span class="kt">ResponseDisposition</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Async closure executed when a new <code>HTTPURLResponse</code> is received and returning a
     <code>ResponseDisposition</code> value. This value determines whether to continue the request or cancel it as
     if <code>cancel()</code> had been called on the instance. Note, this closure is called on an arbitrary thread,
     so any synchronous calls in it will execute in that context.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC14onHTTPResponse7performACXDySo17NSHTTPURLResponseCYaYbc_tF"></a>
                    <a name="//apple_ref/swift/Method/onHTTPResponse(perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC14onHTTPResponse7performACXDySo17NSHTTPURLResponseCYaYbc_tF">onHTTPResponse(perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Sets an async closure called whenever the <code>DataRequest</code> produces an <code>HTTPURLResponse</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>Most requests will only produce a single response for each outgoing attempt (initial + retries).
    However, some types of response may trigger multiple <code>HTTPURLResponse</code>s, such as multipart streams,
    where responses after the first will contain the part headers.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">onHTTPResponse</span><span class="p">(</span><span class="n">perform</span> <span class="nv">handler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">response</span><span class="p">:</span> <span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="k">async</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>handler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Async closure executed when a new <code>HTTPURLResponse</code> is received. Note, this closure is called on an
     arbitrary thread, so any synchronous calls in it will execute in that context.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC011serializingB023automaticallyCancelling16dataPreprocessor18emptyResponseCodes0iC7MethodsAA0B4TaskVy10Foundation0B0VGSb_AA0bH0_pShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/serializingData(automaticallyCancelling:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC011serializingB023automaticallyCancelling16dataPreprocessor18emptyResponseCodes0iC7MethodsAA0B4TaskVy10Foundation0B0VGSb_AA0bH0_pShySiGShyAA10HTTPMethodVGtF">serializingData(automaticallyCancelling:<wbr>dataPreprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataTask.html">DataTask</a></code> to <code>await</code> a <code>Data</code> value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">serializingData</span><span class="p">(</span><span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
                            <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                            <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                            <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataTask.html">DataTask</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DataTask.html">DataTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before completion.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP response codes for which empty responses are allowed. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataTask.html">DataTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC20serializingDecodable_23automaticallyCancelling16dataPreprocessor7decoder18emptyResponseCodes0kC7MethodsAA0B4TaskVyxGxm_SbAA0bI0_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/serializingDecodable(_:automaticallyCancelling:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC20serializingDecodable_23automaticallyCancelling16dataPreprocessor7decoder18emptyResponseCodes0kC7MethodsAA0B4TaskVyxGxm_SbAA0bI0_pAA0B7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF">serializingDecodable(_:<wbr>automaticallyCancelling:<wbr>dataPreprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataTask.html">DataTask</a></code> to <code>await</code> serialization of a <code>Decodable</code> value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="n">serializingDecodable</span><span class="o">&lt;</span><span class="kt">Value</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">_</span> <span class="nv">type</span><span class="p">:</span> <span class="kt">Value</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">Value</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                                   <span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
                                                   <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                                   <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                                   <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                                   <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataTask.html">DataTask</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to decode from response data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DataTask.html">DataTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the serializer.
                       <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> to use to decode the response. <code>JSONDecoder()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataTask.html">DataTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC17serializingString23automaticallyCancelling16dataPreprocessor8encoding18emptyResponseCodes0kC7MethodsAA0B4TaskVySSGSb_AA0bI0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/serializingString(automaticallyCancelling:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC17serializingString23automaticallyCancelling16dataPreprocessor8encoding18emptyResponseCodes0kC7MethodsAA0B4TaskVySSGSb_AA0bI0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF">serializingString(automaticallyCancelling:<wbr>dataPreprocessor:<wbr>encoding:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataTask.html">DataTask</a></code> to <code>await</code> serialization of a <code>String</code> value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">serializingString</span><span class="p">(</span><span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
                              <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                              <span class="nv">encoding</span><span class="p">:</span> <span class="kt">String</span><span class="o">.</span><span class="kt">Encoding</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                              <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                              <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataTask.html">DataTask</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DataTask.html">DataTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the serializer.
                       <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>String.Encoding</code> to use during serialization. Defaults to <code>nil</code>, in which case
                       the encoding will be determined from the server response, falling back to the
                       default HTTP character set, <code>ISO-8859-1</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataTask.html">DataTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC19serializingResponse5using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0E10SerializerRzlF"></a>
                    <a name="//apple_ref/swift/Method/serializingResponse(using:automaticallyCancelling:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC19serializingResponse5using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0E10SerializerRzlF">serializingResponse(using:<wbr>automaticallyCancelling:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataTask.html">DataTask</a></code> to <code>await</code> serialization using the provided <code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="n">serializingResponse</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                                <span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataTask.html">DataTask</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> responsible for serializing the request, response, and data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DataTask.html">DataTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataTask.html">DataTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC19serializingResponse5using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0bE18SerializerProtocolRzlF"></a>
                    <a name="//apple_ref/swift/Method/serializingResponse(using:automaticallyCancelling:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC19serializingResponse5using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0bE18SerializerProtocolRzlF">serializingResponse(using:<wbr>automaticallyCancelling:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DataTask.html">DataTask</a></code> to <code>await</code> serialization using the provided <code><a href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a></code> instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="n">serializingResponse</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a></span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                                            <span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DataTask.html">DataTask</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a></code> responsible for serializing the request,
                       response, and data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DataTask.html">DataTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DataTask.html">DataTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC10Validationa"></a>
                    <a name="//apple_ref/swift/Alias/Validation" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC10Validationa">Validation</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A closure used to validate a request that takes a URL request, a URL response and data, and returns whether the
request was valid.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">typealias</span> <span class="kt">Validation</span> <span class="o">=</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">URLRequest</span><span class="p">?,</span> <span class="kt">HTTPURLResponse</span><span class="p">,</span> <span class="kt">Data</span><span class="p">?)</span> <span class="o">-&gt;</span> <span class="kt">ValidationResult</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF"></a>
                    <a name="//apple_ref/swift/Method/validate(statusCode:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF">validate(statusCode:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a status code in the specified sequence.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">validate</span><span class="o">&lt;</span><span class="kt">S</span><span class="o">&gt;</span><span class="p">(</span><span class="n">statusCode</span> <span class="nv">acceptableStatusCodes</span><span class="p">:</span> <span class="kt">S</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sequence</span><span class="p">,</span> <span class="kt">S</span><span class="o">.</span><span class="kt">Element</span> <span class="o">==</span> <span class="kt">Int</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>acceptableStatusCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Sequence</code> of acceptable response status codes.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF"></a>
                    <a name="//apple_ref/swift/Method/validate(contentType:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF">validate(contentType:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a content type in the specified sequence.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">validate</span><span class="o">&lt;</span><span class="kt">S</span><span class="o">&gt;</span><span class="p">(</span><span class="n">contentType</span> <span class="nv">acceptableContentTypes</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="kd">@autoclosure</span> <span class="p">()</span> <span class="o">-&gt;</span> <span class="kt">S</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sequence</span><span class="p">,</span> <span class="kt">S</span><span class="o">.</span><span class="kt">Element</span> <span class="o">==</span> <span class="kt">String</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>contentType</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The acceptable content types, which may specify wildcard types and/or subtypes.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC8validateACXDyF"></a>
                    <a name="//apple_ref/swift/Method/validate()" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC8validateACXDyF">validate()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a status code in the default acceptable range of 200&hellip;299, and that the content
type matches any specified in the Accept HTTP header field.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
