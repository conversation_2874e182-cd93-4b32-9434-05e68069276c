<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Classes  Reference</title>
    <link rel="stylesheet" type="text/css" href="css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="css/highlight.css" />
    <meta charset="utf-8">
    <script src="js/jquery.min.js" defer></script>
    <script src="js/jazzy.js" defer></script>
    
    <script src="js/lunr.min.js" defer></script>
    <script src="js/typeahead.jquery.js" defer></script>
    <script src="js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Section/Classes" class="dashAnchor"></a>

    <a title="Classes  Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="index.html">Alamofire</a>
      <img class="carat" src="img/carat.png" alt=""/>
      Classes  Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>Classes</h1>
            <p>The following classes are available globally.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11DataRequestC"></a>
                    <a name="//apple_ref/swift/Class/DataRequest" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11DataRequestC">DataRequest</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="Classes/Request.html">Request</a></code> subclass which handles in-memory <code>Data</code> download using <code>URLSessionDataTask</code>.</p>

                        <a href="Classes/DataRequest.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">class</span> <span class="kt">DataRequest</span> <span class="p">:</span> <span class="kt"><a href="Classes/Request.html">Request</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17DataStreamRequestC"></a>
                    <a name="//apple_ref/swift/Class/DataStreamRequest" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17DataStreamRequestC">DataStreamRequest</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="Classes/Request.html">Request</a></code> subclass which streams HTTP response <code>Data</code> through a <code>Handler</code> closure.</p>

                        <a href="Classes/DataStreamRequest.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DataStreamRequest</span> <span class="p">:</span> <span class="kt"><a href="Classes/Request.html">Request</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC"></a>
                    <a name="//apple_ref/swift/Class/DownloadRequest" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC">DownloadRequest</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="Classes/Request.html">Request</a></code> subclass which downloads <code>Data</code> to a file on disk using <code>URLSessionDownloadTask</code>.</p>

                        <a href="Classes/DownloadRequest.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DownloadRequest</span> <span class="p">:</span> <span class="kt"><a href="Classes/Request.html">Request</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7RequestC"></a>
                    <a name="//apple_ref/swift/Class/Request" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7RequestC">Request</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>Request</code> is the common superclass of all Alamofire request types and provides common state, delegate, and callback
handling.</p>

                        <a href="Classes/Request.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">class</span> <span class="kt">Request</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Request</span><span class="p">:</span> <span class="kt">Equatable</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Request</span><span class="p">:</span> <span class="kt">Hashable</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Request</span><span class="p">:</span> <span class="kt">CustomStringConvertible</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire0A13NotificationsC"></a>
                    <a name="//apple_ref/swift/Class/AlamofireNotifications" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire0A13NotificationsC">AlamofireNotifications</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="Protocols/EventMonitor.html">EventMonitor</a></code> that provides Alamofire&rsquo;s notifications.</p>

                        <a href="Classes/AlamofireNotifications.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">AlamofireNotifications</span> <span class="p">:</span> <span class="kt"><a href="Protocols/EventMonitor.html">EventMonitor</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire20JSONParameterEncoderC"></a>
                    <a name="//apple_ref/swift/Class/JSONParameterEncoder" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire20JSONParameterEncoderC">JSONParameterEncoder</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A <code><a href="Protocols/ParameterEncoder.html">ParameterEncoder</a></code> that encodes types as JSON body data.</p>

<p>If no <code>Content-Type</code> header is already set on the provided <code>URLRequest</code>s, it&rsquo;s set to <code>application/json</code>.</p>

                        <a href="Classes/JSONParameterEncoder.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">JSONParameterEncoder</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt"><a href="Protocols/ParameterEncoder.html">ParameterEncoder</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire30URLEncodedFormParameterEncoderC"></a>
                    <a name="//apple_ref/swift/Class/URLEncodedFormParameterEncoder" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire30URLEncodedFormParameterEncoderC">URLEncodedFormParameterEncoder</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A <code><a href="Protocols/ParameterEncoder.html">ParameterEncoder</a></code> that encodes types as URL-encoded query strings to be set on the URL or as body data, depending
on the <code>Destination</code> set.</p>

<p>If no <code>Content-Type</code> header is already set on the provided <code>URLRequest</code>s, it will be set to
<code>application/x-www-form-urlencoded; charset=utf-8</code>.</p>

<p>Encoding behavior can be customized by passing an instance of <code><a href="Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a></code> to the initializer.</p>

                        <a href="Classes/URLEncodedFormParameterEncoder.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">URLEncodedFormParameterEncoder</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt"><a href="Protocols/ParameterEncoder.html">ParameterEncoder</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC"></a>
                    <a name="//apple_ref/swift/Class/Session" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC">Session</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>Session</code> creates and manages Alamofire&rsquo;s <code><a href="Classes/Request.html">Request</a></code> types during their lifetimes. It also provides common
functionality for all <code><a href="Classes/Request.html">Request</a></code>s, including queuing, interception, trust management, redirect handling, and response
cache handling.</p>

                        <a href="Classes/Session.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">Session</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Session</span><span class="p">:</span> <span class="kt"><a href="Protocols/RequestDelegate.html">RequestDelegate</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@M@Alamofire@objc(cs)SessionDelegate"></a>
                    <a name="//apple_ref/swift/Class/SessionDelegate" class="dashAnchor"></a>
                    <a class="token" href="#/c:@M@Alamofire@objc(cs)SessionDelegate">SessionDelegate</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Class which implements the various <code>URLSessionDelegate</code> methods to connect various Alamofire features.</p>

                        <a href="Classes/SessionDelegate.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">SessionDelegate</span> <span class="p">:</span> <span class="kt">NSObject</span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SessionDelegate</span><span class="p">:</span> <span class="kt">URLSessionDelegate</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SessionDelegate</span><span class="p">:</span> <span class="kt">URLSessionTaskDelegate</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SessionDelegate</span><span class="p">:</span> <span class="kt">URLSessionDataDelegate</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SessionDelegate</span><span class="p">:</span> <span class="kt">URLSessionWebSocketDelegate</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SessionDelegate</span><span class="p">:</span> <span class="kt">URLSessionDownloadDelegate</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire13UploadRequestC"></a>
                    <a name="//apple_ref/swift/Class/UploadRequest" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire13UploadRequestC">UploadRequest</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="Classes/DataRequest.html">DataRequest</a></code> subclass which handles <code>Data</code> upload from memory, file, or stream using <code>URLSessionUploadTask</code>.</p>

                        <a href="Classes/UploadRequest.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">UploadRequest</span> <span class="p">:</span> <span class="kt"><a href="Classes/DataRequest.html">DataRequest</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire25AuthenticationInterceptorC"></a>
                    <a name="//apple_ref/swift/Class/AuthenticationInterceptor" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire25AuthenticationInterceptorC">AuthenticationInterceptor</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code>AuthenticationInterceptor</code> class manages the queuing and threading complexity of authenticating requests.
It relies on an <code><a href="Protocols/Authenticator.html">Authenticator</a></code> type to handle the actual <code>URLRequest</code> authentication and <code>Credential</code> refresh.</p>

                        <a href="Classes/AuthenticationInterceptor.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">AuthenticationInterceptor</span><span class="o">&lt;</span><span class="kt">AuthenticatorType</span><span class="o">&gt;</span> <span class="p">:</span> <span class="kt"><a href="Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">,</span> <span class="kt">Sendable</span> <span class="k">where</span> <span class="kt">AuthenticatorType</span> <span class="p">:</span> <span class="kt"><a href="Protocols/Authenticator.html">Authenticator</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21CompositeEventMonitorC"></a>
                    <a name="//apple_ref/swift/Class/CompositeEventMonitor" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21CompositeEventMonitorC">CompositeEventMonitor</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>An <code><a href="Protocols/EventMonitor.html">EventMonitor</a></code> which can contain multiple <code><a href="Protocols/EventMonitor.html">EventMonitor</a></code>s and calls their methods on their queues.</p>

                        <a href="Classes/CompositeEventMonitor.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">CompositeEventMonitor</span> <span class="p">:</span> <span class="kt"><a href="Protocols/EventMonitor.html">EventMonitor</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire19ClosureEventMonitorC"></a>
                    <a name="//apple_ref/swift/Class/ClosureEventMonitor" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire19ClosureEventMonitorC">ClosureEventMonitor</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="Protocols/EventMonitor.html">EventMonitor</a></code> that allows optional closures to be set to receive events.</p>

                        <a href="Classes/ClosureEventMonitor.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">ClosureEventMonitor</span> <span class="p">:</span> <span class="kt"><a href="Protocols/EventMonitor.html">EventMonitor</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire17MultipartFormDataC"></a>
                    <a name="//apple_ref/swift/Class/MultipartFormData" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire17MultipartFormDataC">MultipartFormData</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Constructs <code>multipart/form-data</code> for uploads within an HTTP or HTTPS body. There are currently two ways to encode
multipart form data. The first way is to encode the data directly in memory. This is very efficient, but can lead
to memory issues if the dataset is too large. The second way is designed for larger datasets and will write all the
data to a single file on disk with all the proper boundary segmentation. The second approach MUST be used for
larger datasets such as video content, otherwise your app may run out of memory when trying to encode the dataset.</p>

<p>For more information on <code>multipart/form-data</code> in general, please refer to the RFC-2388 and RFC-2045 specs as well
and the w3 form documentation.</p>

<ul>
<li><a href="https://www.ietf.org/rfc/rfc2388.txt">https://www.ietf.org/rfc/rfc2388.txt</a></li>
<li><a href="https://www.ietf.org/rfc/rfc2045.txt">https://www.ietf.org/rfc/rfc2045.txt</a></li>
<li><a href="https://www.w3.org/TR/html401/interact/forms.html#h-17.13">https://www.w3.org/TR/html401/interact/forms.html#h-17.13</a></li>
</ul>

                        <a href="Classes/MultipartFormData.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">MultipartFormData</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire26NetworkReachabilityManagerC"></a>
                    <a name="//apple_ref/swift/Class/NetworkReachabilityManager" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire26NetworkReachabilityManagerC">NetworkReachabilityManager</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code>NetworkReachabilityManager</code> class listens for reachability changes of hosts and addresses for both cellular and
WiFi network interfaces.</p>

<p>Reachability can be used to determine background information about why a network operation failed, or to retry
network requests when a connection is established. It should not be used to prevent a user from initiating a network
request, as it&rsquo;s possible that an initial request may be required to establish reachability.</p>

                        <a href="Classes/NetworkReachabilityManager.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">NetworkReachabilityManager</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7AdapterC"></a>
                    <a name="//apple_ref/swift/Class/Adapter" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7AdapterC">Adapter</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Closure-based <code><a href="Protocols/RequestAdapter.html">RequestAdapter</a></code>.</p>

                        <a href="Classes/Adapter.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">Adapter</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt"><a href="Protocols/RequestInterceptor.html">RequestInterceptor</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7RetrierC"></a>
                    <a name="//apple_ref/swift/Class/Retrier" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7RetrierC">Retrier</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Closure-based <code><a href="Protocols/RequestRetrier.html">RequestRetrier</a></code>.</p>

                        <a href="Classes/Retrier.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">Retrier</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt"><a href="Protocols/RequestInterceptor.html">RequestInterceptor</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11InterceptorC"></a>
                    <a name="//apple_ref/swift/Class/Interceptor" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11InterceptorC">Interceptor</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="Protocols/RequestInterceptor.html">RequestInterceptor</a></code> which can use multiple <code><a href="Protocols/RequestAdapter.html">RequestAdapter</a></code> and <code><a href="Protocols/RequestRetrier.html">RequestRetrier</a></code> values.</p>

                        <a href="Classes/Interceptor.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">Interceptor</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt"><a href="Protocols/RequestInterceptor.html">RequestInterceptor</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Data"></a>
                <a name="//apple_ref/swift/Section/Data" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Data"></a>
                  <h3 class="section-name"><span>Data</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire22DataResponseSerializerC"></a>
                    <a name="//apple_ref/swift/Class/DataResponseSerializer" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire22DataResponseSerializerC">DataResponseSerializer</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A <code><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></code> that performs minimal response checking and returns any response <code>Data</code> as-is. By default, a
request returning <code>nil</code> or no data is considered an error. However, if the request has an <code><a href="Structs/HTTPMethod.html">HTTPMethod</a></code> or the
response has an  HTTP status code valid for empty responses, then an empty <code>Data</code> value is returned.</p>

                        <a href="Classes/DataResponseSerializer.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DataResponseSerializer</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/String"></a>
                <a name="//apple_ref/swift/Section/String" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/String"></a>
                  <h3 class="section-name"><span>String</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire24StringResponseSerializerC"></a>
                    <a name="//apple_ref/swift/Class/StringResponseSerializer" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire24StringResponseSerializerC">StringResponseSerializer</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A <code><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></code> that decodes the response data as a <code>String</code>. By default, a request returning <code>nil</code> or no
data is considered an error. However, if the request has an <code><a href="Structs/HTTPMethod.html">HTTPMethod</a></code> or the response has an  HTTP status code
valid for empty responses, then an empty <code>String</code> is returned.</p>

                        <a href="Classes/StringResponseSerializer.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">StringResponseSerializer</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/JSON"></a>
                <a name="//apple_ref/swift/Section/JSON" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/JSON"></a>
                  <h3 class="section-name"><span>JSON</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire22JSONResponseSerializerC"></a>
                    <a name="//apple_ref/swift/Class/JSONResponseSerializer" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire22JSONResponseSerializerC">JSONResponseSerializer</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A <code><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></code> that decodes the response data using <code>JSONSerialization</code>. By default, a request returning
<code>nil</code> or no data is considered an error. However, if the request has an <code><a href="Structs/HTTPMethod.html">HTTPMethod</a></code> or the response has an
HTTP status code valid for empty responses, then an <code>NSNull</code> value is returned.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    This serializer is deprecated and should not be used. Instead, create concrete types conforming to
    <code>Decodable</code> and use a <code><a href="Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></code>.

</div>

                        <a href="Classes/JSONResponseSerializer.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="o">*</span><span class="p">,</span> <span class="n">deprecated</span><span class="p">,</span> <span class="nv">message</span><span class="p">:</span> <span class="s">"JSONResponseSerializer deprecated and will be removed in Alamofire 6. Use DecodableResponseSerializer instead."</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">JSONResponseSerializer</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Decodable"></a>
                <a name="//apple_ref/swift/Section/Decodable" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Decodable"></a>
                  <h3 class="section-name"><span>Decodable</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire27DecodableResponseSerializerC"></a>
                    <a name="//apple_ref/swift/Class/DecodableResponseSerializer" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire27DecodableResponseSerializerC">DecodableResponseSerializer</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A <code><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></code> that decodes the response data as a <code>Decodable</code> value using any decoder that conforms to
<code><a href="Protocols/DataDecoder.html">DataDecoder</a></code>. By default, this is an instance of <code>JSONDecoder</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>A request returning <code>nil</code> or no data is considered an error. However, if the request has an <code><a href="Structs/HTTPMethod.html">HTTPMethod</a></code> or
    the response has an HTTP status code valid for empty responses then an empty value will be returned. If the
    decoded type conforms to <code><a href="Protocols/EmptyResponse.html">EmptyResponse</a></code>, the type&rsquo;s <code>emptyValue()</code> will be returned. If the decoded type is
    <code><a href="Structs/Empty.html">Empty</a></code>, the <code>.value</code> instance is returned. If the decoded type <em>does not</em> conform to <code><a href="Protocols/EmptyResponse.html">EmptyResponse</a></code> and
    isn&rsquo;t <code><a href="Structs/Empty.html">Empty</a></code>, an error will be produced.</p>

</div><div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p><code>JSONDecoder</code> and <code>PropertyListDecoder</code> are not <code>Sendable</code> on Apple platforms until macOS 13+ or iOS 16+, so
    instances passed to a serializer should not be used outside of the serializer. Additionally, ensure a new
    serializer is created for each request, do not use a single, shared serializer, so as to ensure separate
    decoder instances.</p>

</div>

                        <a href="Classes/DecodableResponseSerializer.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DecodableResponseSerializer</span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ResponseSerializer.html">ResponseSerializer</a></span> <span class="k">where</span> <span class="kt">T</span> <span class="p">:</span> <span class="kt">Decodable</span><span class="p">,</span> <span class="kt">T</span> <span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire11RetryPolicyC"></a>
                    <a name="//apple_ref/swift/Class/RetryPolicy" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire11RetryPolicyC">RetryPolicy</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A retry policy that retries requests using an exponential backoff for allowed HTTP methods and HTTP status codes
as well as certain types of networking errors.</p>

                        <a href="Classes/RetryPolicy.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">RetryPolicy</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt"><a href="Protocols/RequestInterceptor.html">RequestInterceptor</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire25ConnectionLostRetryPolicyC"></a>
                    <a name="//apple_ref/swift/Class/ConnectionLostRetryPolicy" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire25ConnectionLostRetryPolicyC">ConnectionLostRetryPolicy</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A retry policy that automatically retries idempotent requests for network connection lost errors. For more
information about retrying network connection lost errors, please refer to Apple&rsquo;s
<a href="https://developer.apple.com/library/content/qa/qa1941/_index.html">technical document</a>.</p>

                        <a href="Classes/ConnectionLostRetryPolicy.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">ConnectionLostRetryPolicy</span> <span class="p">:</span> <span class="kt"><a href="Classes/RetryPolicy.html">RetryPolicy</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18ServerTrustManagerC"></a>
                    <a name="//apple_ref/swift/Class/ServerTrustManager" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18ServerTrustManagerC">ServerTrustManager</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Responsible for managing the mapping of <code><a href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a></code> values to given hosts.</p>

                        <a href="Classes/ServerTrustManager.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">ServerTrustManager</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Server%20Trust%20Evaluators"></a>
                <a name="//apple_ref/swift/Section/Server Trust Evaluators" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Server%20Trust%20Evaluators"></a>
                  <h3 class="section-name"><span>Server Trust Evaluators</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21DefaultTrustEvaluatorC"></a>
                    <a name="//apple_ref/swift/Class/DefaultTrustEvaluator" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21DefaultTrustEvaluatorC">DefaultTrustEvaluator</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>An evaluator which uses the default server trust evaluation while allowing you to control whether to validate the
host provided by the challenge. Applications are encouraged to always validate the host in production environments
to guarantee the validity of the server&rsquo;s certificate chain.</p>

                        <a href="Classes/DefaultTrustEvaluator.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DefaultTrustEvaluator</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire24RevocationTrustEvaluatorC"></a>
                    <a name="//apple_ref/swift/Class/RevocationTrustEvaluator" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire24RevocationTrustEvaluatorC">RevocationTrustEvaluator</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>An evaluator which Uses the default and revoked server trust evaluations allowing you to control whether to validate
the host provided by the challenge as well as specify the revocation flags for testing for revoked certificates.
Apple platforms did not start testing for revoked certificates automatically until iOS 10.1, macOS 10.12 and tvOS
10.1 which is demonstrated in our TLS tests. Applications are encouraged to always validate the host in production
environments to guarantee the validity of the server&rsquo;s certificate chain.</p>

                        <a href="Classes/RevocationTrustEvaluator.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">RevocationTrustEvaluator</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire32PinnedCertificatesTrustEvaluatorC"></a>
                    <a name="//apple_ref/swift/Class/PinnedCertificatesTrustEvaluator" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire32PinnedCertificatesTrustEvaluatorC">PinnedCertificatesTrustEvaluator</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Uses the pinned certificates to validate the server trust. The server trust is considered valid if one of the pinned
certificates match one of the server certificates. By validating both the certificate chain and host, certificate
pinning provides a very secure form of server trust validation mitigating most, if not all, MITM attacks.
Applications are encouraged to always validate the host and require a valid certificate chain in production
environments.</p>

                        <a href="Classes/PinnedCertificatesTrustEvaluator.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">PinnedCertificatesTrustEvaluator</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire24PublicKeysTrustEvaluatorC"></a>
                    <a name="//apple_ref/swift/Class/PublicKeysTrustEvaluator" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire24PublicKeysTrustEvaluatorC">PublicKeysTrustEvaluator</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Uses the pinned public keys to validate the server trust. The server trust is considered valid if one of the pinned
public keys match one of the server certificate public keys. By validating both the certificate chain and host,
public key pinning provides a very secure form of server trust validation mitigating most, if not all, MITM attacks.
Applications are encouraged to always validate the host and require a valid certificate chain in production
environments.</p>

                        <a href="Classes/PublicKeysTrustEvaluator.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">PublicKeysTrustEvaluator</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire23CompositeTrustEvaluatorC"></a>
                    <a name="//apple_ref/swift/Class/CompositeTrustEvaluator" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire23CompositeTrustEvaluatorC">CompositeTrustEvaluator</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Uses the provided evaluators to validate the server trust. The trust is only considered valid if all of the
evaluators consider it valid.</p>

                        <a href="Classes/CompositeTrustEvaluator.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">CompositeTrustEvaluator</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire22DisabledTrustEvaluatorC"></a>
                    <a name="//apple_ref/swift/Class/DisabledTrustEvaluator" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire22DisabledTrustEvaluatorC">DisabledTrustEvaluator</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Disables all evaluation which in turn will always consider any server trust as valid.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    Instead of disabling server trust evaluation, it&rsquo;s a better idea to configure systems to properly trust test
    certificates, as outlined in <a href="https://developer.apple.com/library/archive/qa/qa1948/_index.html">this Apple tech note</a>.

</div>

<p><strong>THIS EVALUATOR SHOULD NEVER BE USED IN PRODUCTION!</strong></p>

                        <a href="Classes/DisabledTrustEvaluator.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DisabledTrustEvaluator</span> <span class="p">:</span> <span class="kt"><a href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC"></a>
                    <a name="//apple_ref/swift/Class/URLEncodedFormEncoder" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC">URLEncodedFormEncoder</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>An object that encodes instances into URL-encoded query strings.</p>

<p><code>ArrayEncoding</code> can be used to configure how <code>Array</code> values are encoded. By default, the <code>.brackets</code> encoding is
used, encoding array values with brackets for each value. e.g <code>array[]=1&amp;array[]=2</code>.</p>

<p><code>BoolEncoding</code> can be used to configure how <code>Bool</code> values are encoded. By default, the <code>.numeric</code> encoding is used,
encoding <code>true</code> as <code>1</code> and <code>false</code> as <code>0</code>.</p>

<p><code>DataEncoding</code> can be used to configure how <code>Data</code> values are encoded. By default, the <code>.deferredToData</code> encoding is
used, which encodes <code>Data</code> values using their default <code>Encodable</code> implementation.</p>

<p><code>DateEncoding</code> can be used to configure how <code>Date</code> values are encoded. By default, the <code>.deferredToDate</code>
encoding is used, which encodes <code>Date</code>s using their default <code>Encodable</code> implementation.</p>

<p><code>KeyEncoding</code> can be used to configure how keys are encoded. By default, the <code>.useDefaultKeys</code> encoding is used,
which encodes the keys directly from the <code>Encodable</code> implementation.</p>

<p><code>KeyPathEncoding</code> can be used to configure how paths within nested objects are encoded. By default, the <code>.brackets</code>
encoding is used, which encodes each sub-key in brackets. e.g. <code>parent[child][grandchild]=value</code>.</p>

<p><code>NilEncoding</code> can be used to configure how <code>nil</code> <code>Optional</code> values are encoded. By default, the <code>.dropKey</code> encoding
is used, which drops <code>nil</code> key / value pairs from the output entirely.</p>

<p><code>SpaceEncoding</code> can be used to configure how spaces are encoded. By default, the <code>.percentEscaped</code> encoding is used,
replacing spaces with <code>%20</code>.</p>

<p>This type is largely based on Vapor&rsquo;s <a href="https://github.com/vapor/url-encoded-form"><code>url-encoded-form</code></a> project.</p>

                        <a href="Classes/URLEncodedFormEncoder.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">URLEncodedFormEncoder</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
