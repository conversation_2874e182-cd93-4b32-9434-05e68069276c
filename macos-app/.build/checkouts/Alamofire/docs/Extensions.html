<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Extensions  Reference</title>
    <link rel="stylesheet" type="text/css" href="css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="css/highlight.css" />
    <meta charset="utf-8">
    <script src="js/jquery.min.js" defer></script>
    <script src="js/jazzy.js" defer></script>
    
    <script src="js/lunr.min.js" defer></script>
    <script src="js/typeahead.jquery.js" defer></script>
    <script src="js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Section/Extensions" class="dashAnchor"></a>

    <a title="Extensions  Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="index.html">Alamofire</a>
      <img class="carat" src="img/carat.png" alt=""/>
      Extensions  Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>Extensions</h1>
            <p>The following extensions are available globally.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:s5ErrorP"></a>
                    <a name="//apple_ref/swift/Extension/Error" class="dashAnchor"></a>
                    <a class="token" href="#/s:s5ErrorP">Error</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/Error.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Error</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/System%20Type%20Extensions"></a>
                <a name="//apple_ref/swift/Section/System Type Extensions" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/System%20Type%20Extensions"></a>
                  <h3 class="section-name"><span>System Type Extensions</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:10Foundation10URLRequestV"></a>
                    <a name="//apple_ref/swift/Extension/URLRequest" class="dashAnchor"></a>
                    <a class="token" href="#/s:10Foundation10URLRequestV">URLRequest</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/URLRequest.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">URLRequest</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">URLRequest</span><span class="p">:</span> <span class="kt"><a href="Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:objc(cs)NSHTTPURLResponse"></a>
                    <a name="//apple_ref/swift/Extension/HTTPURLResponse" class="dashAnchor"></a>
                    <a class="token" href="#/c:objc(cs)NSHTTPURLResponse">HTTPURLResponse</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/HTTPURLResponse.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">HTTPURLResponse</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:objc(cs)NSURLSessionConfiguration"></a>
                    <a name="//apple_ref/swift/Extension/URLSessionConfiguration" class="dashAnchor"></a>
                    <a class="token" href="#/c:objc(cs)NSURLSessionConfiguration">URLSessionConfiguration</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/URLSessionConfiguration.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">URLSessionConfiguration</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">URLSessionConfiguration</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:10Foundation12NotificationV"></a>
                    <a name="//apple_ref/swift/Extension/Notification" class="dashAnchor"></a>
                    <a class="token" href="#/s:10Foundation12NotificationV">Notification</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/Notification.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Notification</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/"></a>
                    <a name="//apple_ref/swift/Extension/Protected" class="dashAnchor"></a>
                    <a class="token" href="#/">Protected</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:SS"></a>
                    <a name="//apple_ref/swift/Extension/String" class="dashAnchor"></a>
                    <a class="token" href="#/s:SS">String</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/String.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">String</span><span class="p">:</span> <span class="kt"><a href="Protocols/URLConvertible.html">URLConvertible</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:10Foundation3URLV"></a>
                    <a name="//apple_ref/swift/Extension/URL" class="dashAnchor"></a>
                    <a class="token" href="#/s:10Foundation3URLV">URL</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/URL.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">URL</span><span class="p">:</span> <span class="kt"><a href="Protocols/URLConvertible.html">URLConvertible</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:10Foundation13URLComponentsV"></a>
                    <a name="//apple_ref/swift/Extension/URLComponents" class="dashAnchor"></a>
                    <a class="token" href="#/s:10Foundation13URLComponentsV">URLComponents</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/URLComponents.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">URLComponents</span><span class="p">:</span> <span class="kt"><a href="Protocols/URLConvertible.html">URLConvertible</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataDecoder%20Protocol"></a>
                <a name="//apple_ref/swift/Section/DataDecoder Protocol" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataDecoder%20Protocol"></a>
                  <h3 class="section-name"><span>DataDecoder Protocol</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:10Foundation11JSONDecoderC"></a>
                    <a name="//apple_ref/swift/Extension/JSONDecoder" class="dashAnchor"></a>
                    <a class="token" href="#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>JSONDecoder</code> automatically conforms to <code><a href="Protocols/DataDecoder.html">DataDecoder</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">JSONDecoder</span><span class="p">:</span> <span class="kt"><a href="Protocols/DataDecoder.html">DataDecoder</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:10Foundation19PropertyListDecoderC"></a>
                    <a name="//apple_ref/swift/Extension/PropertyListDecoder" class="dashAnchor"></a>
                    <a class="token" href="#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>PropertyListDecoder</code> automatically conforms to <code><a href="Protocols/DataDecoder.html">DataDecoder</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">PropertyListDecoder</span><span class="p">:</span> <span class="kt"><a href="Protocols/DataDecoder.html">DataDecoder</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Extensions"></a>
                <a name="//apple_ref/swift/Section/Extensions" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Extensions"></a>
                  <h3 class="section-name"><span>Extensions</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/"></a>
                    <a name="//apple_ref/swift/Extension/[ServerTrustEvaluating]" class="dashAnchor"></a>
                    <a class="token" href="#/">[ServerTrustEvaluating]</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/%5BServerTrustEvaluating%5D.html" class="slightly-smaller">See more</a>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:objc(cs)NSBundle"></a>
                    <a name="//apple_ref/swift/Extension/Bundle" class="dashAnchor"></a>
                    <a class="token" href="#/c:objc(cs)NSBundle">Bundle</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Bundle</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@T@SecTrustRef"></a>
                    <a name="//apple_ref/swift/Extension/SecTrust" class="dashAnchor"></a>
                    <a class="token" href="#/c:@T@SecTrustRef">SecTrust</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SecTrust</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@T@SecPolicyRef"></a>
                    <a name="//apple_ref/swift/Extension/SecPolicy" class="dashAnchor"></a>
                    <a class="token" href="#/c:@T@SecPolicyRef">SecPolicy</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SecPolicy</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:Sa"></a>
                    <a name="//apple_ref/swift/Extension/Array" class="dashAnchor"></a>
                    <a class="token" href="#/s:Sa">Array</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Array</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@T@SecCertificateRef"></a>
                    <a name="//apple_ref/swift/Extension/SecCertificate" class="dashAnchor"></a>
                    <a class="token" href="#/c:@T@SecCertificateRef">SecCertificate</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SecCertificate</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@T@OSStatus"></a>
                    <a name="//apple_ref/swift/Extension/OSStatus" class="dashAnchor"></a>
                    <a class="token" href="#/c:@T@OSStatus">OSStatus</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">OSStatus</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/c:@E@SecTrustResultType"></a>
                    <a name="//apple_ref/swift/Extension/SecTrustResultType" class="dashAnchor"></a>
                    <a class="token" href="#/c:@E@SecTrustResultType">SecTrustResultType</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">SecTrustResultType</span><span class="p">:</span> <span class="kt"><a href="Protocols/AlamofireExtended.html">AlamofireExtended</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:10Foundation12CharacterSetV"></a>
                    <a name="//apple_ref/swift/Extension/CharacterSet" class="dashAnchor"></a>
                    <a class="token" href="#/s:10Foundation12CharacterSetV">CharacterSet</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                        <a href="Extensions/CharacterSet.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">CharacterSet</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
