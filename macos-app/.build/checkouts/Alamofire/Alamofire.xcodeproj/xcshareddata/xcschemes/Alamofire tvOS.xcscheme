<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1600"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "4CF626EE1BA7CB3E0011A099"
               BuildableName = "Alamofire.framework"
               BlueprintName = "Alamofire tvOS"
               ReferencedContainer = "container:Alamofire.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "4CF626F71BA7CB3E0011A099"
               BuildableName = "Alamofire tvOS Tests.xctest"
               BlueprintName = "Alamofire tvOS Tests"
               ReferencedContainer = "container:Alamofire.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "NO"
      codeCoverageEnabled = "YES"
      onlyGenerateCoverageForSpecifiedTargets = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "4CF626EE1BA7CB3E0011A099"
            BuildableName = "Alamofire.framework"
            BlueprintName = "Alamofire tvOS"
            ReferencedContainer = "container:Alamofire.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <AdditionalOptions>
         <AdditionalOption
            key = "NSZombieEnabled"
            value = "YES"
            isEnabled = "YES">
         </AdditionalOption>
      </AdditionalOptions>
      <CodeCoverageTargets>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "4CF626EE1BA7CB3E0011A099"
            BuildableName = "Alamofire.framework"
            BlueprintName = "Alamofire tvOS"
            ReferencedContainer = "container:Alamofire.xcodeproj">
         </BuildableReference>
      </CodeCoverageTargets>
      <TestPlans>
         <TestPlanReference
            reference = "container:Tests/Test Plans/tvOS.xctestplan"
            default = "YES">
         </TestPlanReference>
         <TestPlanReference
            reference = "container:Tests/Test Plans/tvOS-NoTS.xctestplan">
         </TestPlanReference>
         <TestPlanReference
            reference = "container:Tests/Test Plans/tvOS-Old.xctestplan">
         </TestPlanReference>
      </TestPlans>
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "4CF626F71BA7CB3E0011A099"
               BuildableName = "Alamofire tvOS Tests.xctest"
               BlueprintName = "Alamofire tvOS Tests"
               ReferencedContainer = "container:Alamofire.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      disableMainThreadChecker = "YES"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      enableGPUValidationMode = "1"
      allowLocationSimulation = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "4CF626EE1BA7CB3E0011A099"
            BuildableName = "Alamofire.framework"
            BlueprintName = "Alamofire tvOS"
            ReferencedContainer = "container:Alamofire.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "4CF626EE1BA7CB3E0011A099"
            BuildableName = "Alamofire.framework"
            BlueprintName = "Alamofire tvOS"
            ReferencedContainer = "container:Alamofire.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
