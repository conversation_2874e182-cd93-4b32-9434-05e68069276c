<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder.WatchKit.Storyboard" version="3.0" toolsVersion="11134" targetRuntime="watchKit" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="3mp-fW-waa">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="11106"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBWatchKitPlugin" version="11055"/>
    </dependencies>
    <scenes>
        <!--Interface Controller-->
        <scene sceneID="aou-V4-d1y">
            <objects>
                <hostingController id="3mp-fW-waa" customClass="HostingController"
                customModuleProvider="target"/>
            </objects>
        </scene>
    </scenes>
</document>
