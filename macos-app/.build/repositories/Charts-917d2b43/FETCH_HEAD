e73cb9fa546f120bde179d5f92a6b052da7330b1	not-for-merge	branch 'bugfix/xbounds-iterator' of https://github.com/danielgindi/Charts
0c194fa6cb5d65ba42c40a3bc984eef10d9126f6	not-for-merge	branch 'chanage-library-name' of https://github.com/danielgindi/Charts
c58e44145423939e2accfeac3597761699afbc7c	not-for-merge	branch 'feature/cgcontext+perform' of https://github.com/danielgindi/Charts
a8f7c499744af5818e0ff89620bb0e628a65bd1b	not-for-merge	branch 'feature/clamped' of https://github.com/danielgindi/Charts
ef2fb5beb552dcd31ff5db56625b8fff065485df	not-for-merge	branch 'feature/update-deprecated-color-scanner-implementation' of https://github.com/danielgindi/Charts
86929ffb26280d49777e8b2c2311daa419747a49	not-for-merge	branch 'master' of https://github.com/danielgindi/Charts
e065048c7e4ce51b4930772ec1857c64f16691ba	not-for-merge	branch 'objc-compatibility-library' of https://github.com/danielgindi/Charts
1935638107ef37d4048323b9488d07f6fa62307c	not-for-merge	branch 'pie_renderer_spacing_issues' of https://github.com/danielgindi/Charts
8017da48a87e606607647c1846c11d138f66ffee	not-for-merge	branch 'remove-deprecations' of https://github.com/danielgindi/Charts
3d5b031cf9bec76f835bfd4365a5ed53ef3bec79	not-for-merge	branch 'update-actions' of https://github.com/danielgindi/Charts
4d0463fb27ee7ac7d04ee35ad83a9f82036674c5	not-for-merge	'refs/pull/1/head' of https://github.com/danielgindi/Charts
5c148ae10050ae2efd7924d3b0fb977de801467a	not-for-merge	'refs/pull/1000/head' of https://github.com/danielgindi/Charts
0de8c42a3f80f502c2aa76a136796b8677853ee6	not-for-merge	'refs/pull/1006/head' of https://github.com/danielgindi/Charts
3ab5d8b5d9021834e2091cb597bd59c8ea9f961b	not-for-merge	'refs/pull/1006/merge' of https://github.com/danielgindi/Charts
dd0b7c25be827ad619dd1fd8d8875afcb32d01ed	not-for-merge	'refs/pull/1010/head' of https://github.com/danielgindi/Charts
db340af23fabae48e30241f1b228a4cd0a37f471	not-for-merge	'refs/pull/1010/merge' of https://github.com/danielgindi/Charts
0e7c26bbd9b15bca7784d2d611a5e44ee470a865	not-for-merge	'refs/pull/1012/head' of https://github.com/danielgindi/Charts
78c66e7aaccd6597e6ec3a8a8251b03896484835	not-for-merge	'refs/pull/1015/head' of https://github.com/danielgindi/Charts
8da4c9c2365a71139fb1578aaf8b4ed1951ace39	not-for-merge	'refs/pull/1035/head' of https://github.com/danielgindi/Charts
49de2326e810534451771e8d5051e3c9ee5a12ae	not-for-merge	'refs/pull/1040/head' of https://github.com/danielgindi/Charts
3aec743fdf19be21b0c8cfea83c6dc838060f975	not-for-merge	'refs/pull/1049/head' of https://github.com/danielgindi/Charts
8ff6f8e84ecec03f77cdd7d8bc1c34d9cb304e9d	not-for-merge	'refs/pull/1049/merge' of https://github.com/danielgindi/Charts
9e4c2d34f5078d7a149efa8643edd2a52da84167	not-for-merge	'refs/pull/106/head' of https://github.com/danielgindi/Charts
0b769721b946d517d91f73862d67bc8d238c9d9b	not-for-merge	'refs/pull/107/head' of https://github.com/danielgindi/Charts
8e6a2404e72e575d45fa24573a611882ba66df33	not-for-merge	'refs/pull/1082/head' of https://github.com/danielgindi/Charts
8dcc64601e7ad35b3e4d3ddf9fa6261fa2e189d1	not-for-merge	'refs/pull/1087/head' of https://github.com/danielgindi/Charts
c8ccaf9e3b15c26f4128b0989ea7c81508614d26	not-for-merge	'refs/pull/1088/head' of https://github.com/danielgindi/Charts
bec2a78459e5016e131d6a8586104c1bdd46d1c6	not-for-merge	'refs/pull/1089/head' of https://github.com/danielgindi/Charts
7eff38e728cf1f8166cb569bec992aef89874b33	not-for-merge	'refs/pull/1089/merge' of https://github.com/danielgindi/Charts
276076f1180a0676e4766fe27bb3fa48864df8dc	not-for-merge	'refs/pull/1091/head' of https://github.com/danielgindi/Charts
d64da74e863040127dcbc4d5f20d0bd2d38fb738	not-for-merge	'refs/pull/1091/merge' of https://github.com/danielgindi/Charts
276076f1180a0676e4766fe27bb3fa48864df8dc	not-for-merge	'refs/pull/1092/head' of https://github.com/danielgindi/Charts
a916854d76d03df1bbde610d8ff94e6142204e52	not-for-merge	'refs/pull/1092/merge' of https://github.com/danielgindi/Charts
4e58a896a3eeda3d87bd693a4b6763d2f8c9ad9f	not-for-merge	'refs/pull/1094/head' of https://github.com/danielgindi/Charts
8658da61f303bf4459578e48b3d1fabd1acb7a5d	not-for-merge	'refs/pull/1094/merge' of https://github.com/danielgindi/Charts
65b39128ba04a26d9cc3a5fed94217b1aa9654a6	not-for-merge	'refs/pull/1099/head' of https://github.com/danielgindi/Charts
dedacc10c3bedc4a95c6fcb64b928429990a503e	not-for-merge	'refs/pull/1099/merge' of https://github.com/danielgindi/Charts
b9ea57905f6bc86f8e8c1a2f7f85d89ca5645ee4	not-for-merge	'refs/pull/1126/head' of https://github.com/danielgindi/Charts
aa415160ccf0a9aec80511e7c903bf953043a51f	not-for-merge	'refs/pull/1126/merge' of https://github.com/danielgindi/Charts
9dedd177129afc48a1659551b646fa065075f2bc	not-for-merge	'refs/pull/1130/head' of https://github.com/danielgindi/Charts
e937837598f8aa196f8f6136e61af174a2fbd436	not-for-merge	'refs/pull/1142/head' of https://github.com/danielgindi/Charts
2958761be7b56c05e2100b29949ed0715d3558c6	not-for-merge	'refs/pull/1142/merge' of https://github.com/danielgindi/Charts
13becd37e4659da5783dfd2232c8baebef46f1d7	not-for-merge	'refs/pull/1145/head' of https://github.com/danielgindi/Charts
6e8f0497f4cb6467116bb0198f0553d358a779d0	not-for-merge	'refs/pull/1145/merge' of https://github.com/danielgindi/Charts
75931fd2f88d6cfd2570e6b78d57771a181863e1	not-for-merge	'refs/pull/1163/head' of https://github.com/danielgindi/Charts
39cdf63c95c0883ea22bb837be121c9b0333136f	not-for-merge	'refs/pull/1169/head' of https://github.com/danielgindi/Charts
7d5cc18eafa8868f7fe7569d139cb21d53c0d0ea	not-for-merge	'refs/pull/1169/merge' of https://github.com/danielgindi/Charts
39cdf63c95c0883ea22bb837be121c9b0333136f	not-for-merge	'refs/pull/1171/head' of https://github.com/danielgindi/Charts
77e0529e03fcbe99d13bbc6e4e8887b2dac74e67	not-for-merge	'refs/pull/1179/head' of https://github.com/danielgindi/Charts
7f053d4a3bac4883bc4dc89aac0ccd7b0671cb81	not-for-merge	'refs/pull/1179/merge' of https://github.com/danielgindi/Charts
b670d8cff3cadc0b497bc21cef6cb90243f0b281	not-for-merge	'refs/pull/1187/head' of https://github.com/danielgindi/Charts
950eb1748d9570b09cd345876b0a72a714762c4a	not-for-merge	'refs/pull/1187/merge' of https://github.com/danielgindi/Charts
bd1549c97853e280c9104e6931c84fdcfaa8ca5d	not-for-merge	'refs/pull/119/head' of https://github.com/danielgindi/Charts
0e50eb4a5d9b46b92e755af289f4b84744e69b87	not-for-merge	'refs/pull/1195/head' of https://github.com/danielgindi/Charts
659bdcff8d36e74b8cf3f185530643d67b87319f	not-for-merge	'refs/pull/1195/merge' of https://github.com/danielgindi/Charts
0ef42209cc7dcd4d9211c22f533b21447dd85f6d	not-for-merge	'refs/pull/1202/head' of https://github.com/danielgindi/Charts
31e9cfffd8b009a24a52f8f7fcee439d9c951cc3	not-for-merge	'refs/pull/1203/head' of https://github.com/danielgindi/Charts
3b31396971a2c9ab30f344ec979f7a22be102c74	not-for-merge	'refs/pull/1205/head' of https://github.com/danielgindi/Charts
098b961b4ce24e1bdaf800a42e0bc7242f1269ee	not-for-merge	'refs/pull/1213/head' of https://github.com/danielgindi/Charts
49730a20b365048c4d8125a7e65363982ec1dddf	not-for-merge	'refs/pull/1224/head' of https://github.com/danielgindi/Charts
c345cd3f70796745e96c5c56e621618214560b47	not-for-merge	'refs/pull/1224/merge' of https://github.com/danielgindi/Charts
4d08068fb451e7ef7baef48d9d690372a54abe84	not-for-merge	'refs/pull/1226/head' of https://github.com/danielgindi/Charts
3936663506238a18e054e5582e5b66b36a635078	not-for-merge	'refs/pull/1227/head' of https://github.com/danielgindi/Charts
3d6664328e2d27afa028b77b9090cfe901463473	not-for-merge	'refs/pull/1227/merge' of https://github.com/danielgindi/Charts
a314533a6fb7398bb40a8aaffe97c7ba05a5ee9e	not-for-merge	'refs/pull/1230/head' of https://github.com/danielgindi/Charts
605f6fd5366f497f4f4920b4103abaa8fbd9d37a	not-for-merge	'refs/pull/1230/merge' of https://github.com/danielgindi/Charts
eadefab0fafa54f3f3a1976c612e112d0e3768c5	not-for-merge	'refs/pull/1245/head' of https://github.com/danielgindi/Charts
3734cc7d4fd8b90c750d38eac0fc2ac3a149c459	not-for-merge	'refs/pull/1249/head' of https://github.com/danielgindi/Charts
0b6570591c595a019d519e695f552cf5e890be6c	not-for-merge	'refs/pull/1258/head' of https://github.com/danielgindi/Charts
fd9d8aa2b43aec36c42be0703e76051b84246012	not-for-merge	'refs/pull/1261/head' of https://github.com/danielgindi/Charts
b353268a60d7b3511f554359e6db3463b95bd114	not-for-merge	'refs/pull/1261/merge' of https://github.com/danielgindi/Charts
d432396346f402287315888094ede1b95e84bdb7	not-for-merge	'refs/pull/1264/head' of https://github.com/danielgindi/Charts
8b5dcff8ca7e30a143bdfd7e3c40d3ebc17746c8	not-for-merge	'refs/pull/1266/head' of https://github.com/danielgindi/Charts
dcf67e51e674988038d2e4a2a70a3e4f71271bca	not-for-merge	'refs/pull/1266/merge' of https://github.com/danielgindi/Charts
6d91f6149e305a631b27ef7b0ca47ee2ac8cd360	not-for-merge	'refs/pull/1268/head' of https://github.com/danielgindi/Charts
d8e60fe8dfca36056c5aa0113fb8b3c2471f5859	not-for-merge	'refs/pull/1270/head' of https://github.com/danielgindi/Charts
6b29503d8d2859201976d619c5890b4bf81c5a75	not-for-merge	'refs/pull/1271/head' of https://github.com/danielgindi/Charts
3b32f9e2e1fb8b84de3433d7bb83ba5fe4c6f375	not-for-merge	'refs/pull/1281/head' of https://github.com/danielgindi/Charts
cbaafd7e76023ba99e29fac8e44e18fdd21d38b6	not-for-merge	'refs/pull/1284/head' of https://github.com/danielgindi/Charts
1057f6d04ee85e237856210cdce1938c5c84f3a8	not-for-merge	'refs/pull/1284/merge' of https://github.com/danielgindi/Charts
5b5be17f2586732386829d276a6d19ff568b57cf	not-for-merge	'refs/pull/1287/head' of https://github.com/danielgindi/Charts
f20922970cae93bb9285f125a302918d00103196	not-for-merge	'refs/pull/1287/merge' of https://github.com/danielgindi/Charts
c4c6e85e42e6e524538a3fcee3bb43f8cfd89d9d	not-for-merge	'refs/pull/1288/head' of https://github.com/danielgindi/Charts
f24899400415013e64d3157c239d5c24f0a52c43	not-for-merge	'refs/pull/1288/merge' of https://github.com/danielgindi/Charts
c4c6e85e42e6e524538a3fcee3bb43f8cfd89d9d	not-for-merge	'refs/pull/1290/head' of https://github.com/danielgindi/Charts
8046b98e134b5fb256c5b62981600162b85eb18c	not-for-merge	'refs/pull/1294/head' of https://github.com/danielgindi/Charts
4588ac472b833157bc9b9d979eb917ac6490f492	not-for-merge	'refs/pull/1295/head' of https://github.com/danielgindi/Charts
55c79316160b028f06ebf8b18bcaa008f996f157	not-for-merge	'refs/pull/1296/head' of https://github.com/danielgindi/Charts
09bb4986fe661aa692a1fbd71c3df4491347041a	not-for-merge	'refs/pull/13/head' of https://github.com/danielgindi/Charts
cd4a47337796d786b398cc28cf5aed1e851bf9b6	not-for-merge	'refs/pull/1318/head' of https://github.com/danielgindi/Charts
2b6ac7c45ac7f36c9abc39f3403974203c791470	not-for-merge	'refs/pull/1319/head' of https://github.com/danielgindi/Charts
5e05b6de9c4b2de84aec71a421ad3c1a2f5aadc1	not-for-merge	'refs/pull/1330/head' of https://github.com/danielgindi/Charts
a6f25d040c926044e4b14d2f20829910c3b635dc	not-for-merge	'refs/pull/1330/merge' of https://github.com/danielgindi/Charts
690b307f9178acd9cb7b108be745e2598f675c43	not-for-merge	'refs/pull/1335/head' of https://github.com/danielgindi/Charts
635ca114988ce81ce0a10795cdcab97834a25e27	not-for-merge	'refs/pull/1335/merge' of https://github.com/danielgindi/Charts
d75a6d4bff296de29755d22a5a335f2b2fb7cb9e	not-for-merge	'refs/pull/1336/head' of https://github.com/danielgindi/Charts
5a9cdf3e3db3334ba5a9e416dd4371422e79ffcc	not-for-merge	'refs/pull/1336/merge' of https://github.com/danielgindi/Charts
6edd02809e7fd1d57e38b07d0f45ac07cbe45d6c	not-for-merge	'refs/pull/134/head' of https://github.com/danielgindi/Charts
062b986431142b1a8223c79389897fd2cc9023f1	not-for-merge	'refs/pull/134/merge' of https://github.com/danielgindi/Charts
bd8dd4898e5963bbedb1bb825370e8fe3cc62b19	not-for-merge	'refs/pull/1341/head' of https://github.com/danielgindi/Charts
d2dacccd786c706283d2bb78503b9c12a0b7b0b6	not-for-merge	'refs/pull/1342/head' of https://github.com/danielgindi/Charts
e7edc7550ea1b0bee7b765234640b6bf30a9d6c3	not-for-merge	'refs/pull/1353/head' of https://github.com/danielgindi/Charts
c43d3387ac7e3b8d846a7bb976091c5d86e5f239	not-for-merge	'refs/pull/1355/head' of https://github.com/danielgindi/Charts
eba6313409733aa311263c7364ccf60a9a6f8005	not-for-merge	'refs/pull/1355/merge' of https://github.com/danielgindi/Charts
2b58abed1c14a9ae6413f045adf03499ab387f0d	not-for-merge	'refs/pull/1362/head' of https://github.com/danielgindi/Charts
15aeb0fefb60d048d5a02281f0d566dbd0c19d78	not-for-merge	'refs/pull/1376/head' of https://github.com/danielgindi/Charts
389e3c843ea2997cfa55aa1deaa37e94e17c8370	not-for-merge	'refs/pull/1376/merge' of https://github.com/danielgindi/Charts
0edf903791e7a38cb87c76d5ce8bdcb60e08ee7d	not-for-merge	'refs/pull/1378/head' of https://github.com/danielgindi/Charts
3bb693168740242e9f115b93114364cb4a608cf4	not-for-merge	'refs/pull/1379/head' of https://github.com/danielgindi/Charts
cbd0db04b4b1572e6e349cd75c37072aca0909c4	not-for-merge	'refs/pull/1386/head' of https://github.com/danielgindi/Charts
4d4cae1f8d8febfc58ab4c898a9226e349856cb6	not-for-merge	'refs/pull/1386/merge' of https://github.com/danielgindi/Charts
5a1a7ce80c1c26d0d8f02298fff847dba57908bb	not-for-merge	'refs/pull/1387/head' of https://github.com/danielgindi/Charts
5a1a7ce80c1c26d0d8f02298fff847dba57908bb	not-for-merge	'refs/pull/1388/head' of https://github.com/danielgindi/Charts
5a1a7ce80c1c26d0d8f02298fff847dba57908bb	not-for-merge	'refs/pull/1389/head' of https://github.com/danielgindi/Charts
d9d73eb072cb60f2b841eb7dd0be59b0561d9f2b	not-for-merge	'refs/pull/1406/head' of https://github.com/danielgindi/Charts
1919b4f164c8c1fb2b907d8ba09531c20b443597	not-for-merge	'refs/pull/1406/merge' of https://github.com/danielgindi/Charts
12d4ccab0beb2e2ae7e03d9778b48306f468907b	not-for-merge	'refs/pull/1414/head' of https://github.com/danielgindi/Charts
394973c282bfa5e503ffef7434767c869feb3c07	not-for-merge	'refs/pull/1420/head' of https://github.com/danielgindi/Charts
13834f9ebc4bfb22d597fd727e2a038608d89167	not-for-merge	'refs/pull/1422/head' of https://github.com/danielgindi/Charts
c675f7e2b3900a6ea76dad817079678b6b27a14b	not-for-merge	'refs/pull/1437/head' of https://github.com/danielgindi/Charts
c2ab068bcc0063b7aba9ecf6ca788984792c328e	not-for-merge	'refs/pull/1450/head' of https://github.com/danielgindi/Charts
f9a270ec2af4459987b9c44895ae8be522227a12	not-for-merge	'refs/pull/1450/merge' of https://github.com/danielgindi/Charts
6e36b7036f98a4ebb54d97750505a01bfb685cc1	not-for-merge	'refs/pull/1452/head' of https://github.com/danielgindi/Charts
7dd7ddee80793dc6237c74963f17f9253ad89dc9	not-for-merge	'refs/pull/146/head' of https://github.com/danielgindi/Charts
7c1334ae08be7d27ccc0244d9037dc15947320f6	not-for-merge	'refs/pull/1466/head' of https://github.com/danielgindi/Charts
0b3ec09c1604c8238ba30de16aea1d9e28bb3457	not-for-merge	'refs/pull/1467/head' of https://github.com/danielgindi/Charts
b641ac7922d7d6515a74099bceaa17c302bc8e44	not-for-merge	'refs/pull/147/head' of https://github.com/danielgindi/Charts
a02bd3f9e43f1dd40bc73a731a29949d165d9b21	not-for-merge	'refs/pull/1484/head' of https://github.com/danielgindi/Charts
c675f7e2b3900a6ea76dad817079678b6b27a14b	not-for-merge	'refs/pull/1485/head' of https://github.com/danielgindi/Charts
ebb3e6b08d8d14afdaf95846d9ea2778e16c9052	not-for-merge	'refs/pull/1486/head' of https://github.com/danielgindi/Charts
004344dc84f6255811b389ce7d889b4a6dc9f153	not-for-merge	'refs/pull/149/head' of https://github.com/danielgindi/Charts
970845cd225b409f161411bfd6e6ee4ad8a97439	not-for-merge	'refs/pull/149/merge' of https://github.com/danielgindi/Charts
03ce567b4748cfd433d34186450c3f22d6df2ca8	not-for-merge	'refs/pull/150/head' of https://github.com/danielgindi/Charts
a2666738e95471908813ee6f567d9097f1c9fb9a	not-for-merge	'refs/pull/150/merge' of https://github.com/danielgindi/Charts
b74096f94f3f88121af74caad5c04082acd99a67	not-for-merge	'refs/pull/1505/head' of https://github.com/danielgindi/Charts
cfb79ae65967e3601d31dcba35d9dca77b441e02	not-for-merge	'refs/pull/1509/head' of https://github.com/danielgindi/Charts
09216b7d270a462565702f5320d8601c42b13b14	not-for-merge	'refs/pull/1510/head' of https://github.com/danielgindi/Charts
ab3bb619df2c2d165b41736b6bc25a4218833252	not-for-merge	'refs/pull/1512/head' of https://github.com/danielgindi/Charts
bfbfc320ffd0572ad8e693d81da266e9f113b7f4	not-for-merge	'refs/pull/152/head' of https://github.com/danielgindi/Charts
b99a56bd7bfb521a610b04610aa34bd747b531b6	not-for-merge	'refs/pull/1525/head' of https://github.com/danielgindi/Charts
986acf339e7357215346f1f0bb199abaec29ddc3	not-for-merge	'refs/pull/1541/head' of https://github.com/danielgindi/Charts
e555b1ce45d3873b050d0d6cc1e0a05787737d70	not-for-merge	'refs/pull/1541/merge' of https://github.com/danielgindi/Charts
7bc798b8712e7755bdc5bf72fe2b9c4cef4fc330	not-for-merge	'refs/pull/1558/head' of https://github.com/danielgindi/Charts
4293b973367b9a0bf24e7d0d7bb3eeddda775cdc	not-for-merge	'refs/pull/1559/head' of https://github.com/danielgindi/Charts
fbba94a0e22c0a2ef0274b06b827f0ccfd24d36a	not-for-merge	'refs/pull/1560/head' of https://github.com/danielgindi/Charts
56d2508324b9cf9920f263e12cb663c00841eb41	not-for-merge	'refs/pull/1561/head' of https://github.com/danielgindi/Charts
c4207945aa2b20f20652ca439944c28ece3d5eb3	not-for-merge	'refs/pull/1563/head' of https://github.com/danielgindi/Charts
5170d18d0073fa04c2273090dbb84a017f4ab783	not-for-merge	'refs/pull/1565/head' of https://github.com/danielgindi/Charts
40d8ea65be8fafd228e3bca43ff800734331800f	not-for-merge	'refs/pull/1571/head' of https://github.com/danielgindi/Charts
3d5d12f7da58aa068c48abf08322d2b46754c95f	not-for-merge	'refs/pull/1571/merge' of https://github.com/danielgindi/Charts
7921df43b8b25f02cb12810236f5a56e61a08a47	not-for-merge	'refs/pull/1586/head' of https://github.com/danielgindi/Charts
066de92e870afc3fd0eea6fdb4a39ea291bf5f59	not-for-merge	'refs/pull/1586/merge' of https://github.com/danielgindi/Charts
cb6f1f824e53e7d8ba1fa127f80b14a4a8b28fda	not-for-merge	'refs/pull/1621/head' of https://github.com/danielgindi/Charts
8f1ac92759fc664840fc99c5a73e40ed533a1881	not-for-merge	'refs/pull/1637/head' of https://github.com/danielgindi/Charts
b72b1818c25a0615b10e6d22bf9e2314e9bd1b0b	not-for-merge	'refs/pull/1637/merge' of https://github.com/danielgindi/Charts
68faec954272ebf27eb06e7efb07b649c85142ab	not-for-merge	'refs/pull/1638/head' of https://github.com/danielgindi/Charts
b979528b98b3b2e96bcfd487c240d7e4ea0bd782	not-for-merge	'refs/pull/1638/merge' of https://github.com/danielgindi/Charts
ac88607da23178e2a3c5ee3c06bcf2560b83ea5b	not-for-merge	'refs/pull/1648/head' of https://github.com/danielgindi/Charts
0def7bf78c08a670352608e0539426773be2621c	not-for-merge	'refs/pull/165/head' of https://github.com/danielgindi/Charts
743deb7984d0f33215fcaf705ea2d28e5c7c7880	not-for-merge	'refs/pull/1650/head' of https://github.com/danielgindi/Charts
015366f2c3624b791b7089258497cee85c097d2a	not-for-merge	'refs/pull/1662/head' of https://github.com/danielgindi/Charts
7f882b12f50d1f63f6893946aad7dc49ab002a41	not-for-merge	'refs/pull/1662/merge' of https://github.com/danielgindi/Charts
25e2d7eefe53c4eae21db66a59d132cb7b7a0fab	not-for-merge	'refs/pull/1675/head' of https://github.com/danielgindi/Charts
d01841e170598b15d13ecbc9b2718d8c71ac647e	not-for-merge	'refs/pull/1675/merge' of https://github.com/danielgindi/Charts
0a4c40bb940486792d7bfa40c49589830b8cac46	not-for-merge	'refs/pull/168/head' of https://github.com/danielgindi/Charts
c04263a203c3bf7e200b67da976b9c99c48c8ec9	not-for-merge	'refs/pull/168/merge' of https://github.com/danielgindi/Charts
28f685d82a34d47c60fb3d096c06425c8aac3801	not-for-merge	'refs/pull/1684/head' of https://github.com/danielgindi/Charts
33f63acff27aad69bde89b7d3d4fcf1d99aeb546	not-for-merge	'refs/pull/1711/head' of https://github.com/danielgindi/Charts
96fe86cd671e935305347bcd4e6c71e33c4cd1e7	not-for-merge	'refs/pull/1711/merge' of https://github.com/danielgindi/Charts
6de82cc81e277d4be9352bb727ac46ad0a5455c6	not-for-merge	'refs/pull/1712/head' of https://github.com/danielgindi/Charts
a168a2490cc1da85e60e39ce7eff47a111b0f180	not-for-merge	'refs/pull/1712/merge' of https://github.com/danielgindi/Charts
f48d76fdd0c009a0f9089a38492d01e59c9b8ccc	not-for-merge	'refs/pull/1733/head' of https://github.com/danielgindi/Charts
2bae81d4f475770f43535a2072cfe49c643f4d1b	not-for-merge	'refs/pull/1738/head' of https://github.com/danielgindi/Charts
66c40e26a1b6e3fd702cdba535aaf68029566b54	not-for-merge	'refs/pull/1738/merge' of https://github.com/danielgindi/Charts
0a56492109e95a40ab9dd67e6eb91536df22e793	not-for-merge	'refs/pull/1741/head' of https://github.com/danielgindi/Charts
e6c46534a5b224b57c99f3b7fdd6e8b708c2abab	not-for-merge	'refs/pull/1741/merge' of https://github.com/danielgindi/Charts
d32dc46319ca7463bdb6f51eb8a2870daca46f6a	not-for-merge	'refs/pull/1744/head' of https://github.com/danielgindi/Charts
cf86fa41763b7a229a401b4c97523b0a4692c362	not-for-merge	'refs/pull/1744/merge' of https://github.com/danielgindi/Charts
d45fff17773065acd0d23139ac8383923cb6e34a	not-for-merge	'refs/pull/1745/head' of https://github.com/danielgindi/Charts
88f13bbc75c34687b67adab39d8bf7ab092fa1c8	not-for-merge	'refs/pull/1745/merge' of https://github.com/danielgindi/Charts
b91b70adc2a7a1615a27012a0353db0ee20a2447	not-for-merge	'refs/pull/1771/head' of https://github.com/danielgindi/Charts
48cbde4b2c3802aaac651dd344d7dca6f826d7fe	not-for-merge	'refs/pull/178/head' of https://github.com/danielgindi/Charts
ea10029517a03f4543fbb569b9a018f63b17bfd9	not-for-merge	'refs/pull/178/merge' of https://github.com/danielgindi/Charts
2735f5586a6e325ada0a0a85e2058d08df987d33	not-for-merge	'refs/pull/1780/head' of https://github.com/danielgindi/Charts
2e0e9a0b4eecdab8c80bac628ae42d092be3ee74	not-for-merge	'refs/pull/1780/merge' of https://github.com/danielgindi/Charts
7d7abd361fe564318e88d54b78f4b8cd3c4dbd2e	not-for-merge	'refs/pull/1783/head' of https://github.com/danielgindi/Charts
a2a5d047cc8bc659215617309b833b582f7f7598	not-for-merge	'refs/pull/1785/head' of https://github.com/danielgindi/Charts
d7a4e187a8cb8402cf66c270664e50c7341142d3	not-for-merge	'refs/pull/1785/merge' of https://github.com/danielgindi/Charts
ef91bd589500729a5ba3f18c60f340ab176dbe8a	not-for-merge	'refs/pull/179/head' of https://github.com/danielgindi/Charts
ccaa966458495fe718172cb4e46556f33942ea16	not-for-merge	'refs/pull/1793/head' of https://github.com/danielgindi/Charts
84409e451ce72972b464b118854a924ea2f79f79	not-for-merge	'refs/pull/1796/head' of https://github.com/danielgindi/Charts
d0fdb1a8de306d3d4456d23e95857126a3e86c5a	not-for-merge	'refs/pull/1796/merge' of https://github.com/danielgindi/Charts
b0d18a0af216c600b269b59916b1454970feb9c5	not-for-merge	'refs/pull/1805/head' of https://github.com/danielgindi/Charts
1298073207baa9fa799cad031d0075dd4851da18	not-for-merge	'refs/pull/1805/merge' of https://github.com/danielgindi/Charts
a88178d8a3f4ba12761fc722b03a829aeed3d57f	not-for-merge	'refs/pull/1814/head' of https://github.com/danielgindi/Charts
c7e804a02818bf1edc9c2a2731d82cc8090a606c	not-for-merge	'refs/pull/1814/merge' of https://github.com/danielgindi/Charts
fe03a4268e84d7dfe09278e3dd4591ee468e576c	not-for-merge	'refs/pull/183/head' of https://github.com/danielgindi/Charts
6346c17c4a11dfb328f9a8fb8ba73c7e4c25adda	not-for-merge	'refs/pull/183/merge' of https://github.com/danielgindi/Charts
c952851069df3eb789d1f54d979ecaca1be4294a	not-for-merge	'refs/pull/184/head' of https://github.com/danielgindi/Charts
9914438a3c82ccc95b61980f07bce5d5d0d9e698	not-for-merge	'refs/pull/185/head' of https://github.com/danielgindi/Charts
1d76c6c76f9886ae1671f659ba5774e061d389ee	not-for-merge	'refs/pull/1867/head' of https://github.com/danielgindi/Charts
f40dab8e83ad1a0042883c5e42252d3b103a408c	not-for-merge	'refs/pull/188/head' of https://github.com/danielgindi/Charts
e782620abbc7acfb6ea952accf8c3107563b59df	not-for-merge	'refs/pull/188/merge' of https://github.com/danielgindi/Charts
7092de9caa2db2535ebdd95af2b024713579c221	not-for-merge	'refs/pull/1891/head' of https://github.com/danielgindi/Charts
ff585ba39f847ecfb8b9e463416b779f49b71c05	not-for-merge	'refs/pull/191/head' of https://github.com/danielgindi/Charts
2922a34bfe0c453bf37f929153a28fc51e8b52b8	not-for-merge	'refs/pull/1917/head' of https://github.com/danielgindi/Charts
3865f54ed4745697e0dfce3a776bf5114dc10871	not-for-merge	'refs/pull/192/head' of https://github.com/danielgindi/Charts
ee24561f7c3140ba44ac3513e9d5b68e50c6103b	not-for-merge	'refs/pull/1948/head' of https://github.com/danielgindi/Charts
c37be322ca5159ed98165390a12ace77227f7cc8	not-for-merge	'refs/pull/1948/merge' of https://github.com/danielgindi/Charts
68c23045aa4bc4f949669e6cd3eb68d64ea5717b	not-for-merge	'refs/pull/198/head' of https://github.com/danielgindi/Charts
a9698f68824b91a2770fc78594641f9baa46bbdc	not-for-merge	'refs/pull/1983/head' of https://github.com/danielgindi/Charts
2c79bebe131d48aaab158dc46032467266d7e16a	not-for-merge	'refs/pull/1983/merge' of https://github.com/danielgindi/Charts
05d452f8d12ebb6aefaa5a770894e40e355b2b06	not-for-merge	'refs/pull/1984/head' of https://github.com/danielgindi/Charts
bf38cc758267c0651ff4dc670f5011f1a9ff3686	not-for-merge	'refs/pull/20/head' of https://github.com/danielgindi/Charts
61c0c2e6303e4cdc16348297ce597588735a75b6	not-for-merge	'refs/pull/200/head' of https://github.com/danielgindi/Charts
bd864e8ffa24284814d474f3d1fa66f6e5ed4b33	not-for-merge	'refs/pull/2026/head' of https://github.com/danielgindi/Charts
921172c098b5dea981ac7ca7ec4ae6d7f5ea54fc	not-for-merge	'refs/pull/2026/merge' of https://github.com/danielgindi/Charts
8d929a1056ce44d8919dc40b329a84b78ecf2e2d	not-for-merge	'refs/pull/2032/head' of https://github.com/danielgindi/Charts
5c08747405c68435c93c46c082921e060f814cbf	not-for-merge	'refs/pull/2048/head' of https://github.com/danielgindi/Charts
cc5e96debb1fabaa413f57793bf821cc567a2d3c	not-for-merge	'refs/pull/2049/head' of https://github.com/danielgindi/Charts
803b6dc1b9d839517199e0e7e28c63a4ef486d8e	not-for-merge	'refs/pull/2049/merge' of https://github.com/danielgindi/Charts
e71b3f1c389d859b8ebf2fb632823b91a999762c	not-for-merge	'refs/pull/2052/head' of https://github.com/danielgindi/Charts
e24fe7019d51cd66af752ee8f6165e7149ee1e0b	not-for-merge	'refs/pull/2052/merge' of https://github.com/danielgindi/Charts
fc0e121e32b60bb31138f8ee978abcbc712b93db	not-for-merge	'refs/pull/2054/head' of https://github.com/danielgindi/Charts
1e4cf006a2552c40f9996f234bbfd0ae5fcde84e	not-for-merge	'refs/pull/2054/merge' of https://github.com/danielgindi/Charts
71e9f8788af7d09d40e5f6626fa20da27cff15a6	not-for-merge	'refs/pull/2056/head' of https://github.com/danielgindi/Charts
455af4233f85e2d121a5e39e4b7526b6b3960035	not-for-merge	'refs/pull/2062/head' of https://github.com/danielgindi/Charts
f875b31c6e9aba44e24df1d9254cd0d0e670d426	not-for-merge	'refs/pull/2062/merge' of https://github.com/danielgindi/Charts
1f26720eabe89e5ef97b55542da4c452952844d4	not-for-merge	'refs/pull/2064/head' of https://github.com/danielgindi/Charts
f76aae9810d7d0337b1b293a7ddf06d91ffd8d21	not-for-merge	'refs/pull/207/head' of https://github.com/danielgindi/Charts
8b860b2b48306725570a051cecb2593e477971bf	not-for-merge	'refs/pull/2078/head' of https://github.com/danielgindi/Charts
09af10c209c6765b48c3eeb605af5a8fa5f1880f	not-for-merge	'refs/pull/208/head' of https://github.com/danielgindi/Charts
8264de057b42af8c27bccbe26828cd2b779ba974	not-for-merge	'refs/pull/208/merge' of https://github.com/danielgindi/Charts
7083149d797dce56e4771b57144d4ff77209aef6	not-for-merge	'refs/pull/2083/head' of https://github.com/danielgindi/Charts
9d32bd757f8b4c29d899a1263b21ba0247b34b19	not-for-merge	'refs/pull/2083/merge' of https://github.com/danielgindi/Charts
acc15ab3d436b1656584c34505b8a1b967ff1c0b	not-for-merge	'refs/pull/2084/head' of https://github.com/danielgindi/Charts
cb9dc0286960c8abf9ce56062d9c8e8d49c75329	not-for-merge	'refs/pull/209/head' of https://github.com/danielgindi/Charts
8f8a3a499b67ad23b534d9288fe54721959cf139	not-for-merge	'refs/pull/209/merge' of https://github.com/danielgindi/Charts
fa3a5414fe0aa0a422e044915dbc882206112757	not-for-merge	'refs/pull/2093/head' of https://github.com/danielgindi/Charts
caf9eda0b8374b043b9c89dfec3a3048a4cfe7f1	not-for-merge	'refs/pull/2093/merge' of https://github.com/danielgindi/Charts
7e8683ec8d1f63b993781522f71daae3047a6b31	not-for-merge	'refs/pull/2096/head' of https://github.com/danielgindi/Charts
03772852b8bd215bc776a77b216610b8ea22c70a	not-for-merge	'refs/pull/2096/merge' of https://github.com/danielgindi/Charts
a8f4917e172a7574442419dcdb6305b410cd19ae	not-for-merge	'refs/pull/2101/head' of https://github.com/danielgindi/Charts
b833737824c45d97fb4e35e2fb6112a447cc9271	not-for-merge	'refs/pull/2108/head' of https://github.com/danielgindi/Charts
3291aa7a31e2caf9a9d65a8ee50d133edc165e46	not-for-merge	'refs/pull/2108/merge' of https://github.com/danielgindi/Charts
17938bc0452128992e29779c1a9688a753109a4f	not-for-merge	'refs/pull/2114/head' of https://github.com/danielgindi/Charts
72c061d40bf8cd73ee0ca095fd4358b92fee300f	not-for-merge	'refs/pull/2116/head' of https://github.com/danielgindi/Charts
6446d29a321cf71dbe003fbe8e3304842c30525e	not-for-merge	'refs/pull/2156/head' of https://github.com/danielgindi/Charts
1581bb6818fa66a67e73d6c1e0f8110ff3e3353e	not-for-merge	'refs/pull/2162/head' of https://github.com/danielgindi/Charts
6ec1209213ea3149a4436aad984dad63f3e5da9b	not-for-merge	'refs/pull/2167/head' of https://github.com/danielgindi/Charts
cbead46243757f503cbba80bf9ec8d202941fddf	not-for-merge	'refs/pull/2177/head' of https://github.com/danielgindi/Charts
126527e647911e32a62f01a507b7fbb019a76aae	not-for-merge	'refs/pull/2189/head' of https://github.com/danielgindi/Charts
b3fbb0f8f94e150f011fbea199ed5607eac5e24f	not-for-merge	'refs/pull/2191/head' of https://github.com/danielgindi/Charts
4470aad0309c57b83509de7965ef2b913de5c76b	not-for-merge	'refs/pull/2191/merge' of https://github.com/danielgindi/Charts
2407d5f9459a2878f2e5d9603f6a1b7f18bce844	not-for-merge	'refs/pull/2196/head' of https://github.com/danielgindi/Charts
039a47893b95edbba013e198fbaa41bb3afc5b9a	not-for-merge	'refs/pull/2196/merge' of https://github.com/danielgindi/Charts
a069ab57d881ae4737a5668a10c1cd64dfa0067d	not-for-merge	'refs/pull/2199/head' of https://github.com/danielgindi/Charts
4e509dfc14978edb5518c85d236c5a74a9189460	not-for-merge	'refs/pull/22/head' of https://github.com/danielgindi/Charts
aadc3a8428bdf544bddc813568632e3a6d3c19dd	not-for-merge	'refs/pull/2200/head' of https://github.com/danielgindi/Charts
f50477681489983d580800e7c20388fd3d84402e	not-for-merge	'refs/pull/2206/head' of https://github.com/danielgindi/Charts
333f1d224a4e741ee9e6b9e64fee4541f698315c	not-for-merge	'refs/pull/2206/merge' of https://github.com/danielgindi/Charts
2ed242744a50fe3961cc894f0cb9ca081de10100	not-for-merge	'refs/pull/221/head' of https://github.com/danielgindi/Charts
1e45469e6bb9ed799b4bcfb394552f0b4fac9102	not-for-merge	'refs/pull/221/merge' of https://github.com/danielgindi/Charts
cdf3a7726d694c912796f1b7bc83bd3bcdc8e2b2	not-for-merge	'refs/pull/2214/head' of https://github.com/danielgindi/Charts
f2fc9dd089af8f9e965ae879f3a71ff719858e0a	not-for-merge	'refs/pull/2228/head' of https://github.com/danielgindi/Charts
30518c6df34ca0fe9f4ae39c8276a7e929cd67e7	not-for-merge	'refs/pull/2229/head' of https://github.com/danielgindi/Charts
63e17cab486f11a092d82eb90af1f17462fa6ebf	not-for-merge	'refs/pull/2231/head' of https://github.com/danielgindi/Charts
7478e9c49ef819e9a311c06931afd7c94fbf145b	not-for-merge	'refs/pull/2247/head' of https://github.com/danielgindi/Charts
3cfb4442d3f59b01768675746a992e221cdb1156	not-for-merge	'refs/pull/2247/merge' of https://github.com/danielgindi/Charts
8502d3dbbd7b542a419025264cf2e6e8712cb85d	not-for-merge	'refs/pull/225/head' of https://github.com/danielgindi/Charts
0ddf3cd1bd481b41dcb0245a18d10686f41c1702	not-for-merge	'refs/pull/225/merge' of https://github.com/danielgindi/Charts
4101b101771a856c218d76f2b8fa0923b0ce60b6	not-for-merge	'refs/pull/2264/head' of https://github.com/danielgindi/Charts
e39e48715720afbf30ac266f6e78055b30019eda	not-for-merge	'refs/pull/2264/merge' of https://github.com/danielgindi/Charts
4101b101771a856c218d76f2b8fa0923b0ce60b6	not-for-merge	'refs/pull/2265/head' of https://github.com/danielgindi/Charts
cbb18ee28126e490544c242cdc95d0fab7faf269	not-for-merge	'refs/pull/2265/merge' of https://github.com/danielgindi/Charts
bcc993f8557c8468315bff5d9a79d2d9937d7ce3	not-for-merge	'refs/pull/2275/head' of https://github.com/danielgindi/Charts
512941fc040dd3097460cd0a866a02313a177d5a	not-for-merge	'refs/pull/2275/merge' of https://github.com/danielgindi/Charts
e72fe90b0619c581a01013a0e23048442de875c2	not-for-merge	'refs/pull/2279/head' of https://github.com/danielgindi/Charts
3269d53fea25f2b400ddca92b9ef39cddddbb4a6	not-for-merge	'refs/pull/2289/head' of https://github.com/danielgindi/Charts
65f2067761976761e26572fbac80270bed959851	not-for-merge	'refs/pull/2289/merge' of https://github.com/danielgindi/Charts
ebce590a6471ea47524a829363b00ded037f1c0b	not-for-merge	'refs/pull/229/head' of https://github.com/danielgindi/Charts
05f46c950e1694ede886d1f5163bf0ad23741efc	not-for-merge	'refs/pull/229/merge' of https://github.com/danielgindi/Charts
fb9427f1bf00bf25ec600575990426476aff0bc9	not-for-merge	'refs/pull/2299/head' of https://github.com/danielgindi/Charts
cbef02f04158213efb237585185721bd5b8c7598	not-for-merge	'refs/pull/2299/merge' of https://github.com/danielgindi/Charts
3f285e862bffc98837114e5e5dbc17ae427912ff	not-for-merge	'refs/pull/2301/head' of https://github.com/danielgindi/Charts
********************73b3597fdc28fb47faf1	not-for-merge	'refs/pull/2306/head' of https://github.com/danielgindi/Charts
38d838a21c25ffab43027a2b12bd58daecb0600c	not-for-merge	'refs/pull/2306/merge' of https://github.com/danielgindi/Charts
dd09e90bb5a4222da6c4ffff3e6a36a2ad4b93b1	not-for-merge	'refs/pull/2315/head' of https://github.com/danielgindi/Charts
814e623e8226e2f0632147434137e044e8b871f7	not-for-merge	'refs/pull/2315/merge' of https://github.com/danielgindi/Charts
0f0bdf6635a5d31a21074baf834722e71d46d255	not-for-merge	'refs/pull/232/head' of https://github.com/danielgindi/Charts
f5296890a5b301e9f373f2b5e46ea54f3fdf551b	not-for-merge	'refs/pull/2328/head' of https://github.com/danielgindi/Charts
cabd745e4f906081a92d8e3ab1925ecc3e8ec9a2	not-for-merge	'refs/pull/2328/merge' of https://github.com/danielgindi/Charts
409cebfb8bde357617a122829acf218c5b4e7ed9	not-for-merge	'refs/pull/2335/head' of https://github.com/danielgindi/Charts
03939328cd995043877f97daa1c75c0d5a3ab60c	not-for-merge	'refs/pull/2350/head' of https://github.com/danielgindi/Charts
22d63ad1c531651d3716d38891fd611ad3389753	not-for-merge	'refs/pull/2353/head' of https://github.com/danielgindi/Charts
6d7c0496678ebd95375099154f0ef22f4aea7f03	not-for-merge	'refs/pull/2353/merge' of https://github.com/danielgindi/Charts
3b9e7f425f53a95a9b41a2e9ad1702990d85da4a	not-for-merge	'refs/pull/2355/head' of https://github.com/danielgindi/Charts
f04b48957a9fac8c911dfa19d95328ab273c1b55	not-for-merge	'refs/pull/2357/head' of https://github.com/danielgindi/Charts
69971c80a18d6be447fd48209c628df46e091831	not-for-merge	'refs/pull/2357/merge' of https://github.com/danielgindi/Charts
59ed74157fa488de7e974e7856eece2861d2b091	not-for-merge	'refs/pull/2358/head' of https://github.com/danielgindi/Charts
63da156b8b3638cc66f0b89f91d502a4e76eb8f9	not-for-merge	'refs/pull/2358/merge' of https://github.com/danielgindi/Charts
2188fc9d97dd5b5a53d419d84611a338f3a263d1	not-for-merge	'refs/pull/2359/head' of https://github.com/danielgindi/Charts
345fe531c902ca23dac12e49ec10f806bd521347	not-for-merge	'refs/pull/2359/merge' of https://github.com/danielgindi/Charts
373682f6eaa63c676924a19f4ebd7b8ecad5fc92	not-for-merge	'refs/pull/2360/head' of https://github.com/danielgindi/Charts
51011b3bb42ea4f261c3aa6be9355e066d181445	not-for-merge	'refs/pull/2360/merge' of https://github.com/danielgindi/Charts
07a2ff1944a21fedbb02e5889cbaa942aeb37d2f	not-for-merge	'refs/pull/2361/head' of https://github.com/danielgindi/Charts
052fcdc68338e858b07561d1d910ade7d415ae95	not-for-merge	'refs/pull/2361/merge' of https://github.com/danielgindi/Charts
8cbdf6d0c8dc1980dbc79a22c31051e58ded4bbf	not-for-merge	'refs/pull/2364/head' of https://github.com/danielgindi/Charts
f562d496ef1b0ea48871885d32743c5d38055d59	not-for-merge	'refs/pull/2365/head' of https://github.com/danielgindi/Charts
509251222e32831bc704933a3e24fb78bcdb3687	not-for-merge	'refs/pull/2377/head' of https://github.com/danielgindi/Charts
830281770e4b0d351c6c1b6f1e9d1ab86990833d	not-for-merge	'refs/pull/2378/head' of https://github.com/danielgindi/Charts
7f515a27fd87b47785f2005fa8b564f92a0282da	not-for-merge	'refs/pull/2387/head' of https://github.com/danielgindi/Charts
1deb97b573056c774e6f58ce674aba2dd178746e	not-for-merge	'refs/pull/2387/merge' of https://github.com/danielgindi/Charts
41da6c38ae6cea6a70c318c7810d85ace603ba6f	not-for-merge	'refs/pull/2388/head' of https://github.com/danielgindi/Charts
24275ae99056d904bc3937f4675c48ca037cebb0	not-for-merge	'refs/pull/2390/head' of https://github.com/danielgindi/Charts
06e8609600950dc54ceb48b77d575d744d2abe0d	not-for-merge	'refs/pull/2390/merge' of https://github.com/danielgindi/Charts
05702339eee62e4ab5ca4e4c254816bdb054bed4	not-for-merge	'refs/pull/2393/head' of https://github.com/danielgindi/Charts
b03f555644b325324d8d5882955de8bb6c6fc9a5	not-for-merge	'refs/pull/2396/head' of https://github.com/danielgindi/Charts
ce7bf0ebb7dba4200292b438750e207e445570ba	not-for-merge	'refs/pull/2399/head' of https://github.com/danielgindi/Charts
8326fa0b77e5809c84f9d594c82bace599e7e74a	not-for-merge	'refs/pull/2399/merge' of https://github.com/danielgindi/Charts
f43bc570a3222ad8c1cc5c1c5c0a7e637e9fc3a0	not-for-merge	'refs/pull/2413/head' of https://github.com/danielgindi/Charts
e998e3526b0ab442c5760252a75e5ca096412820	not-for-merge	'refs/pull/2414/head' of https://github.com/danielgindi/Charts
78908f82a705b4b518c7cfce7d198839d24d860d	not-for-merge	'refs/pull/2414/merge' of https://github.com/danielgindi/Charts
ba97abda0107fc03d5c539a16077fcb8fb527154	not-for-merge	'refs/pull/2429/head' of https://github.com/danielgindi/Charts
b365a70190d2029e84fa9e25bd7a1d813ed0984d	not-for-merge	'refs/pull/2429/merge' of https://github.com/danielgindi/Charts
bbfa8d212536029f8cc4947df6910e317440b6ed	not-for-merge	'refs/pull/243/head' of https://github.com/danielgindi/Charts
d104bd61185bb47e75ea8188221b3d43e578d6a6	not-for-merge	'refs/pull/2430/head' of https://github.com/danielgindi/Charts
095a76248659498cabb490b2da508a9c485117a5	not-for-merge	'refs/pull/2433/head' of https://github.com/danielgindi/Charts
480154aa298e512a850fde2213ef7e5febcc6113	not-for-merge	'refs/pull/2433/merge' of https://github.com/danielgindi/Charts
8f2783166c2036c45b9369ae11338c2f62eff802	not-for-merge	'refs/pull/2439/head' of https://github.com/danielgindi/Charts
927f2fc977820372d0705119347d66365819bf3c	not-for-merge	'refs/pull/2439/merge' of https://github.com/danielgindi/Charts
6d866db45e55e57c7b15e0b1cf413dfe585a1ac5	not-for-merge	'refs/pull/2449/head' of https://github.com/danielgindi/Charts
9e9c1655de3c8156c3e14ed6585654005ddac3ca	not-for-merge	'refs/pull/2449/merge' of https://github.com/danielgindi/Charts
a7c686901f16d870ecf3ce437d7dd9d6849cdc0c	not-for-merge	'refs/pull/2452/head' of https://github.com/danielgindi/Charts
6e9999e68708c79f72b7d6d9698c1ddba7022118	not-for-merge	'refs/pull/2452/merge' of https://github.com/danielgindi/Charts
948141f0c4e2c92b003ddc10b79b714af2fe0502	not-for-merge	'refs/pull/247/head' of https://github.com/danielgindi/Charts
ef749a34298e7b36522c48d84a09c42bd54f25bb	not-for-merge	'refs/pull/2470/head' of https://github.com/danielgindi/Charts
358d477567d1056b2c56d4b07921ac593999aeed	not-for-merge	'refs/pull/2477/head' of https://github.com/danielgindi/Charts
884f5891ca7a07be92fe8c12f439ff63f3dc0060	not-for-merge	'refs/pull/2477/merge' of https://github.com/danielgindi/Charts
9686ac720dacfab2634039188df59304fd556104	not-for-merge	'refs/pull/248/head' of https://github.com/danielgindi/Charts
cce36f0706f257c7499fc95db08c0fb29944fda8	not-for-merge	'refs/pull/2483/head' of https://github.com/danielgindi/Charts
e69ceb34c961db8276799e3ba6235612904df7ff	not-for-merge	'refs/pull/2483/merge' of https://github.com/danielgindi/Charts
cf0aa2de4f3ca9ce8fe71d48e919a7de7708e5fd	not-for-merge	'refs/pull/2484/head' of https://github.com/danielgindi/Charts
cdfa6c232318fb783aeaaa2e228e2355c651164b	not-for-merge	'refs/pull/25/head' of https://github.com/danielgindi/Charts
4a65b41e651a6797314e522efcf9590d4a64aa91	not-for-merge	'refs/pull/2500/head' of https://github.com/danielgindi/Charts
5e3ac750248c309cf763c121dc2adc458e099dfa	not-for-merge	'refs/pull/2507/head' of https://github.com/danielgindi/Charts
509251222e32831bc704933a3e24fb78bcdb3687	not-for-merge	'refs/pull/2509/head' of https://github.com/danielgindi/Charts
865b7de64c8b827e0ae4f4bbd882a7593c995f29	not-for-merge	'refs/pull/2509/merge' of https://github.com/danielgindi/Charts
a5d6e81000c2e3fcf2ba579e3c8ccf8deac9db65	not-for-merge	'refs/pull/2522/head' of https://github.com/danielgindi/Charts
cea3f16e5a9ebf6e27298f04124648436e693a08	not-for-merge	'refs/pull/2522/merge' of https://github.com/danielgindi/Charts
33c622ff6ac2d5fe457732d9f3bb5e8e7983885c	not-for-merge	'refs/pull/2533/head' of https://github.com/danielgindi/Charts
262e688a65feba9bfbb0a100bf4752bd59a83a4a	not-for-merge	'refs/pull/2533/merge' of https://github.com/danielgindi/Charts
2e1296622a3365d14cb6495fd0a89a45fd8689d8	not-for-merge	'refs/pull/254/head' of https://github.com/danielgindi/Charts
3aa542a6a3bf33dbae6628ed2ae4658b462aeb55	not-for-merge	'refs/pull/2568/head' of https://github.com/danielgindi/Charts
e8dd9fa73ce3a3c4e1999b9c36d757758dbd502a	not-for-merge	'refs/pull/2604/head' of https://github.com/danielgindi/Charts
f4f3563c49624768aeaab1c736bd875db0e07981	not-for-merge	'refs/pull/262/head' of https://github.com/danielgindi/Charts
b4f84b6d62053a1f546f7a769f6b2e9ca0fd9c56	not-for-merge	'refs/pull/2628/head' of https://github.com/danielgindi/Charts
26965e141a11290f0dfc119be44f9f67519d5be8	not-for-merge	'refs/pull/2629/head' of https://github.com/danielgindi/Charts
0c516b46f1edffc74edf32ee8fede9a301514905	not-for-merge	'refs/pull/2640/head' of https://github.com/danielgindi/Charts
0933269f5f28b8d9f8ccd0b2558328098ec6567b	not-for-merge	'refs/pull/2640/merge' of https://github.com/danielgindi/Charts
b0094db1ded9cf743e2c5a6b6b3cf9aab4db7fbe	not-for-merge	'refs/pull/2650/head' of https://github.com/danielgindi/Charts
a339988e3eec1b4998201e975c1e996fc17b5a32	not-for-merge	'refs/pull/2668/head' of https://github.com/danielgindi/Charts
c6f59c9c04d87b9d837cb4f4a356b5a3235b43c4	not-for-merge	'refs/pull/2668/merge' of https://github.com/danielgindi/Charts
80a45c01db77ef2752ea689cc19c117abeb2da69	not-for-merge	'refs/pull/2679/head' of https://github.com/danielgindi/Charts
93413f96b83fc3f97cc0ddd586d0abbe95d1bfbf	not-for-merge	'refs/pull/268/head' of https://github.com/danielgindi/Charts
27e76e406d15e569caf820e45781fe0ce352df3c	not-for-merge	'refs/pull/2687/head' of https://github.com/danielgindi/Charts
d2e08b6f66399d11c876ef7365aa4adff2909eba	not-for-merge	'refs/pull/2687/merge' of https://github.com/danielgindi/Charts
d5b866f6fcccfdff785730ab4db98244c0f0a646	not-for-merge	'refs/pull/269/head' of https://github.com/danielgindi/Charts
d9ad8fab63d8537a2b460fef23f04ce8b7bf5e2f	not-for-merge	'refs/pull/269/merge' of https://github.com/danielgindi/Charts
83ec4165a6992b82a51dcbb442a87232c50cee93	not-for-merge	'refs/pull/2698/head' of https://github.com/danielgindi/Charts
59218f04a79fd66a20e8ff147458aea352bd55f4	not-for-merge	'refs/pull/2702/head' of https://github.com/danielgindi/Charts
4425eb391a56f568a5b66f911c7dc3e0342f0f5e	not-for-merge	'refs/pull/2711/head' of https://github.com/danielgindi/Charts
b81b92ab3358aa861c8735d85ac32e48d900f64d	not-for-merge	'refs/pull/2711/merge' of https://github.com/danielgindi/Charts
7635f5f34c3e7927fea743659d5679e9e145ff21	not-for-merge	'refs/pull/2717/head' of https://github.com/danielgindi/Charts
35e08b0fb29e4823170d391b45bbdd061f3f4a64	not-for-merge	'refs/pull/273/head' of https://github.com/danielgindi/Charts
c83e3ea662b959b8b2cef56485a6e94f9bf9fe19	not-for-merge	'refs/pull/2730/head' of https://github.com/danielgindi/Charts
476c86c9914d4f810d04d8f43e7cb90c52700473	not-for-merge	'refs/pull/2730/merge' of https://github.com/danielgindi/Charts
56b4530f13dd3702a7cf1c3f0ed623439e07c943	not-for-merge	'refs/pull/2737/head' of https://github.com/danielgindi/Charts
25f7ef7e2f94e86702c4e759c09e009a2264a2ba	not-for-merge	'refs/pull/2737/merge' of https://github.com/danielgindi/Charts
35e08b0fb29e4823170d391b45bbdd061f3f4a64	not-for-merge	'refs/pull/274/head' of https://github.com/danielgindi/Charts
d7766a110aa1a9375ae7bc7792630c8661a0132d	not-for-merge	'refs/pull/274/merge' of https://github.com/danielgindi/Charts
e23d186192663e03ea76c96f35057ac2617335e9	not-for-merge	'refs/pull/2754/head' of https://github.com/danielgindi/Charts
2db9b4a1e487fe07b9d96806235c3fd9f4975dd4	not-for-merge	'refs/pull/2760/head' of https://github.com/danielgindi/Charts
e20c34e96ca6a4ab14cd934096e6e6044066e6eb	not-for-merge	'refs/pull/2760/merge' of https://github.com/danielgindi/Charts
d300d6d332afa2e790fd616e5a8326e815b0e1f0	not-for-merge	'refs/pull/2767/head' of https://github.com/danielgindi/Charts
37ff8c038ada87d8557dc51ca07cd9388ee434b9	not-for-merge	'refs/pull/2790/head' of https://github.com/danielgindi/Charts
3bb5a082910b4eee5e684bdcf077f77c4e09adf1	not-for-merge	'refs/pull/2790/merge' of https://github.com/danielgindi/Charts
03939328cd995043877f97daa1c75c0d5a3ab60c	not-for-merge	'refs/pull/2793/head' of https://github.com/danielgindi/Charts
aa9eec212e403aaf15f663f38155f8f91723a858	not-for-merge	'refs/pull/2795/head' of https://github.com/danielgindi/Charts
5e2ad39a3190a039424d825f59c96a549672e56a	not-for-merge	'refs/pull/2795/merge' of https://github.com/danielgindi/Charts
287d349cc8a9c9c5dd6e4995137c8a373110c817	not-for-merge	'refs/pull/2796/head' of https://github.com/danielgindi/Charts
985080542a880bd18f17401fd145b51e357797da	not-for-merge	'refs/pull/2796/merge' of https://github.com/danielgindi/Charts
fdab2168e8e5831ef6f490873f76a3c12d3407d2	not-for-merge	'refs/pull/2800/head' of https://github.com/danielgindi/Charts
29f56f3db8a5e569931856b803447391cdd69394	not-for-merge	'refs/pull/2800/merge' of https://github.com/danielgindi/Charts
58c33f3c2b41cdefed7a4b260f9369236d218dd9	not-for-merge	'refs/pull/2804/head' of https://github.com/danielgindi/Charts
df180608a796d53a51225a57a942ce0ac54aa1f1	not-for-merge	'refs/pull/2804/merge' of https://github.com/danielgindi/Charts
a891d97cfe7cd7ba1cfbe4d88e5c2208edb5ff01	not-for-merge	'refs/pull/2806/head' of https://github.com/danielgindi/Charts
c662fe1f12c5f1258e91c3947611014624f0ff06	not-for-merge	'refs/pull/2848/head' of https://github.com/danielgindi/Charts
a2f1b1efd0cea45eb709ad4262de3b99332d8939	not-for-merge	'refs/pull/285/head' of https://github.com/danielgindi/Charts
ef03b647219ac2ec82b91c77d1b21c7424c5b33b	not-for-merge	'refs/pull/285/merge' of https://github.com/danielgindi/Charts
62aa8b940d5494d0fe410b1f3389baf0af586954	not-for-merge	'refs/pull/2852/head' of https://github.com/danielgindi/Charts
ce665df7f25eaaeadaaa9e0f8521d15a76e11370	not-for-merge	'refs/pull/286/head' of https://github.com/danielgindi/Charts
6f9a2310bbbcc3ee065b94377db880ac7c1ede57	not-for-merge	'refs/pull/286/merge' of https://github.com/danielgindi/Charts
b9a4e053fa7e036f01bac3ecf72240bb96e8a7af	not-for-merge	'refs/pull/2870/head' of https://github.com/danielgindi/Charts
6f1055e9584c6979151c8fc586c93a9ab5a0c676	not-for-merge	'refs/pull/2870/merge' of https://github.com/danielgindi/Charts
ad4d942626294f26b969c773dcfccebc13af05d2	not-for-merge	'refs/pull/2872/head' of https://github.com/danielgindi/Charts
0912e7eacf4f7394e4ebe353b23154bee90109f7	not-for-merge	'refs/pull/2874/head' of https://github.com/danielgindi/Charts
d7cd7b940376c9b56c9674bfe84bf218456879ff	not-for-merge	'refs/pull/2887/head' of https://github.com/danielgindi/Charts
d738fc10b142da9ecabcc1d4f0a350ef4786c611	not-for-merge	'refs/pull/2889/head' of https://github.com/danielgindi/Charts
af12428b39e5fd9dfd96abd7b6fe75293dd14c83	not-for-merge	'refs/pull/2889/merge' of https://github.com/danielgindi/Charts
655d0be54fbc0408036c60409d0777fecdfee890	not-for-merge	'refs/pull/2891/head' of https://github.com/danielgindi/Charts
d26acd056a09bcb9775648386a5eda15f5776c4c	not-for-merge	'refs/pull/2894/head' of https://github.com/danielgindi/Charts
208d82a69056482450623ced445cd3374757097e	not-for-merge	'refs/pull/290/head' of https://github.com/danielgindi/Charts
e1c9faba2e875e9a393dab3328c6671855cb2345	not-for-merge	'refs/pull/291/head' of https://github.com/danielgindi/Charts
52775955c963e016b3b0dd83c7520883a617020a	not-for-merge	'refs/pull/2913/head' of https://github.com/danielgindi/Charts
72d0d73519e32fc7a1f27b648fc2c7a05cea62b5	not-for-merge	'refs/pull/2913/merge' of https://github.com/danielgindi/Charts
aa6cec36f03efb1f570094f9c1026919db5c3ed6	not-for-merge	'refs/pull/2914/head' of https://github.com/danielgindi/Charts
e147d890e6a5dddf3e4fe311b17c09ed9ada1d1b	not-for-merge	'refs/pull/2914/merge' of https://github.com/danielgindi/Charts
bdbdaa62b5a6e98f76b7ebada17f0029570c4c3f	not-for-merge	'refs/pull/2939/head' of https://github.com/danielgindi/Charts
f5b9ec7dc4f89fe3caa7badbb5feda4baaf69fea	not-for-merge	'refs/pull/2941/head' of https://github.com/danielgindi/Charts
f7de35a2539f23a513dfa621a9c9f38bb99d9f31	not-for-merge	'refs/pull/2941/merge' of https://github.com/danielgindi/Charts
021d5fb48df3fc6ab71b4f8090e0b54e3ea5d9f6	not-for-merge	'refs/pull/2942/head' of https://github.com/danielgindi/Charts
87b53a17b9cb4c68a4ab9ae079d01801365ad490	not-for-merge	'refs/pull/2943/head' of https://github.com/danielgindi/Charts
87b53a17b9cb4c68a4ab9ae079d01801365ad490	not-for-merge	'refs/pull/2945/head' of https://github.com/danielgindi/Charts
790cb61b196fe63641c279f3508bf42815ec2b9a	not-for-merge	'refs/pull/2946/head' of https://github.com/danielgindi/Charts
0595a136bffd30633fb37857d9b7d5fc3130af27	not-for-merge	'refs/pull/2946/merge' of https://github.com/danielgindi/Charts
37c8a77de43d56cf44a61192ad52349a7514c5d0	not-for-merge	'refs/pull/2949/head' of https://github.com/danielgindi/Charts
9b9dd683470d60a7ae2bce81d0754dc9e72f7441	not-for-merge	'refs/pull/2950/head' of https://github.com/danielgindi/Charts
d75fb52c4830e73ddfc5d3956016605acbacfe81	not-for-merge	'refs/pull/2955/head' of https://github.com/danielgindi/Charts
57c2e114c7d59a404836c57f4749dc1db34dc146	not-for-merge	'refs/pull/2961/head' of https://github.com/danielgindi/Charts
f9d9863a87a3fd8370bdbc91d382e1e441be57b5	not-for-merge	'refs/pull/2965/head' of https://github.com/danielgindi/Charts
bbfc9697ed7d608b864de124284d1bdfc78dde32	not-for-merge	'refs/pull/2965/merge' of https://github.com/danielgindi/Charts
f9d9863a87a3fd8370bdbc91d382e1e441be57b5	not-for-merge	'refs/pull/2966/head' of https://github.com/danielgindi/Charts
c32532de083015389b722d6a55f564374529f198	not-for-merge	'refs/pull/2966/merge' of https://github.com/danielgindi/Charts
f00f9de2782f6653720d55db585a70e0d4080fd0	not-for-merge	'refs/pull/2967/head' of https://github.com/danielgindi/Charts
26aa4881adeeb0199f129f869f67383fc6e6f8f9	not-for-merge	'refs/pull/297/head' of https://github.com/danielgindi/Charts
b565773d24df12b218454fb053e90a33d8bf354b	not-for-merge	'refs/pull/2980/head' of https://github.com/danielgindi/Charts
326caac1ee96eeb94f75177ec523c754c25efb8c	not-for-merge	'refs/pull/2981/head' of https://github.com/danielgindi/Charts
e458bd97507da0b3acbe2d031c6593dbafa09a72	not-for-merge	'refs/pull/2982/head' of https://github.com/danielgindi/Charts
f8d982b09045904d097316530d6df8fa03e70a64	not-for-merge	'refs/pull/2983/head' of https://github.com/danielgindi/Charts
61984480ee3c8b15ddadbf3a969adea60ba76c78	not-for-merge	'refs/pull/2983/merge' of https://github.com/danielgindi/Charts
d6df20d175476ffb0f26c3bd81b156f96982bce5	not-for-merge	'refs/pull/2986/head' of https://github.com/danielgindi/Charts
af4674d52d26e9597a5b74bae2dea40003d1f75e	not-for-merge	'refs/pull/2986/merge' of https://github.com/danielgindi/Charts
41d5f1070bdb1e3c9ab03d0f048a58e859f68ff5	not-for-merge	'refs/pull/2990/head' of https://github.com/danielgindi/Charts
7a5dd2b7f4f0b3b493601cbf2268b7693cc51d26	not-for-merge	'refs/pull/2990/merge' of https://github.com/danielgindi/Charts
76dc5b96b251aa4a3713c6333774ceb665cbb2b2	not-for-merge	'refs/pull/2991/head' of https://github.com/danielgindi/Charts
3072f9c38bc9dea8a319c35de8875cdcb7f39d37	not-for-merge	'refs/pull/2991/merge' of https://github.com/danielgindi/Charts
d1c6d372b72c5429374ccd0189b9a56017dbb1a5	not-for-merge	'refs/pull/2992/head' of https://github.com/danielgindi/Charts
3aba32db5bd87f5a250e72b85d3fbf34e5f7c9fe	not-for-merge	'refs/pull/2993/head' of https://github.com/danielgindi/Charts
672f25e4a02461788fc478ef945b4e8834bc013b	not-for-merge	'refs/pull/2994/head' of https://github.com/danielgindi/Charts
01160fddff6d6239841201fdf497ebd9817dc6d0	not-for-merge	'refs/pull/2995/head' of https://github.com/danielgindi/Charts
a33f9529ba91770790cdcd84760a78935c0f1b85	not-for-merge	'refs/pull/2996/head' of https://github.com/danielgindi/Charts
81a427cc7aa81b0cf998638b8fc8d4f16716bd6c	not-for-merge	'refs/pull/2997/head' of https://github.com/danielgindi/Charts
3ab6706382c29210372c5df579b5ef73f3bc81e0	not-for-merge	'refs/pull/2998/head' of https://github.com/danielgindi/Charts
e5478b89f13b6bc187d0f9c3dad7ec3df2dcc4a6	not-for-merge	'refs/pull/3000/head' of https://github.com/danielgindi/Charts
8f74fe02462f729f7104fc94009d39ab0e027cb6	not-for-merge	'refs/pull/3001/head' of https://github.com/danielgindi/Charts
0ef8aa17869f57d307c92ce781f1ee7be76db959	not-for-merge	'refs/pull/3002/head' of https://github.com/danielgindi/Charts
fa1297a46f065650e0f08a41543ce0a8a4f357cc	not-for-merge	'refs/pull/3003/head' of https://github.com/danielgindi/Charts
50fdd2dcd4adf631e932d02a603fc6e80acb56f9	not-for-merge	'refs/pull/3005/head' of https://github.com/danielgindi/Charts
ffa89ded2b8a6c7ab84a9e3ffc9e331e4392c1b6	not-for-merge	'refs/pull/3007/head' of https://github.com/danielgindi/Charts
be479ada319004593be7f19d6cade080ab940fc2	not-for-merge	'refs/pull/3007/merge' of https://github.com/danielgindi/Charts
c59b73b87af8bda3e686c8e31d375acf40838131	not-for-merge	'refs/pull/3008/head' of https://github.com/danielgindi/Charts
9ebcae081a3ce1e7ee3cde24236468275d905fb5	not-for-merge	'refs/pull/3010/head' of https://github.com/danielgindi/Charts
a86efd40998e78026f32eddf5b2232e6531cdab4	not-for-merge	'refs/pull/3018/head' of https://github.com/danielgindi/Charts
6ff1faf71233e7983c6e69fe0746ec00a18ca061	not-for-merge	'refs/pull/3018/merge' of https://github.com/danielgindi/Charts
a71f87cfb39c22bb32636ac61c71a89867589af2	not-for-merge	'refs/pull/3023/head' of https://github.com/danielgindi/Charts
eb8e031b86fc3555a84ce920affb0b4cadd15838	not-for-merge	'refs/pull/3024/head' of https://github.com/danielgindi/Charts
c944497f41a962092ff23176a84bd8d00b3b6d33	not-for-merge	'refs/pull/3032/head' of https://github.com/danielgindi/Charts
54d4dd7c2ad7df7aac19158cb8b6cbc288b2cc6a	not-for-merge	'refs/pull/3032/merge' of https://github.com/danielgindi/Charts
8e67c9e5a9bfedc027ac761794319676de3a4262	not-for-merge	'refs/pull/3034/head' of https://github.com/danielgindi/Charts
f56fa656e7ea74d31ed248144b4451993d108ae2	not-for-merge	'refs/pull/3034/merge' of https://github.com/danielgindi/Charts
ea00ed0476a50d6ded2ff7a032fda23c56e7e98c	not-for-merge	'refs/pull/3035/head' of https://github.com/danielgindi/Charts
6869d8546778886a8a34e4198fa181d9d8d0a1eb	not-for-merge	'refs/pull/3038/head' of https://github.com/danielgindi/Charts
5ee225d06b3ea3cc916250ee5ceb632409e49066	not-for-merge	'refs/pull/3039/head' of https://github.com/danielgindi/Charts
e7c85dcf4977e677a4b83c55eaea9acedb693555	not-for-merge	'refs/pull/3041/head' of https://github.com/danielgindi/Charts
0176e15574e34f67a77ac680a0f1eadcd40c378d	not-for-merge	'refs/pull/3042/head' of https://github.com/danielgindi/Charts
55626bb950dcfff2104e352616b3894adf6438fb	not-for-merge	'refs/pull/3043/head' of https://github.com/danielgindi/Charts
c7a1c045d487c1db3b326e7cb5147d6fd88b6af3	not-for-merge	'refs/pull/3044/head' of https://github.com/danielgindi/Charts
310211b6a4ee5def5ac082915076b24b75e2c63e	not-for-merge	'refs/pull/3045/head' of https://github.com/danielgindi/Charts
a5ce127eefce8a8167d03dd597ea7df4fcfb0ad1	not-for-merge	'refs/pull/3046/head' of https://github.com/danielgindi/Charts
d072eebf85655267819fd0a9cb51ee7da89b7e94	not-for-merge	'refs/pull/3051/head' of https://github.com/danielgindi/Charts
e4b177b263c4296185820e76241fac6c56a9a120	not-for-merge	'refs/pull/3054/head' of https://github.com/danielgindi/Charts
b2168dbf96e5589d692a952f85c858362048782b	not-for-merge	'refs/pull/306/head' of https://github.com/danielgindi/Charts
3bba7734c5da0ad23984399765a5d9ffcff60735	not-for-merge	'refs/pull/306/merge' of https://github.com/danielgindi/Charts
c7b4fb58f23cfcc6da0869e1e64e85dc7950133d	not-for-merge	'refs/pull/3071/head' of https://github.com/danielgindi/Charts
8c5158457a6ea30a76ac6e5bea6e72b7b11a19bb	not-for-merge	'refs/pull/3071/merge' of https://github.com/danielgindi/Charts
757d444e7faf15df66e68c90aab526f682490279	not-for-merge	'refs/pull/3073/head' of https://github.com/danielgindi/Charts
c6faf5340319f64d49a881c2da7e97d72cb57901	not-for-merge	'refs/pull/3078/head' of https://github.com/danielgindi/Charts
be29cabb346cf1cd4c2062a151a8098767d91453	not-for-merge	'refs/pull/3079/head' of https://github.com/danielgindi/Charts
15490888e27bed025c5b57cc3c2087094bc45947	not-for-merge	'refs/pull/3079/merge' of https://github.com/danielgindi/Charts
e4c65bbdadc8d47ce6557fd154b2769075ebc9eb	not-for-merge	'refs/pull/3084/head' of https://github.com/danielgindi/Charts
5c03f3186e4be626d62d1c5a091a9dd4a670d2d4	not-for-merge	'refs/pull/3086/head' of https://github.com/danielgindi/Charts
3b4e1394e2917e7d51b3a814cd3b1f778933e6ae	not-for-merge	'refs/pull/3087/head' of https://github.com/danielgindi/Charts
e1628de8d97d6695f208ef037853bf4032b028dd	not-for-merge	'refs/pull/3088/head' of https://github.com/danielgindi/Charts
f9a14566aeb52536e58e38d0295f01cf62a67051	not-for-merge	'refs/pull/3098/head' of https://github.com/danielgindi/Charts
982153d2b368568709f559f5cd30496afbc351df	not-for-merge	'refs/pull/3099/head' of https://github.com/danielgindi/Charts
a9cff6e5938e67f0a996bcc0d758fb4088963399	not-for-merge	'refs/pull/3099/merge' of https://github.com/danielgindi/Charts
7fcdfeb7a7eb8290828a6d21ab0717e7a190cd79	not-for-merge	'refs/pull/3104/head' of https://github.com/danielgindi/Charts
b31c6c39f0d63a597fd2c6b67178172879da4061	not-for-merge	'refs/pull/3104/merge' of https://github.com/danielgindi/Charts
ccbe085bafb8f7fc5d412a2613adb3e66392912d	not-for-merge	'refs/pull/3106/head' of https://github.com/danielgindi/Charts
88f1811cc9b574f9d22ac259b1d9455f2ffa3674	not-for-merge	'refs/pull/3114/head' of https://github.com/danielgindi/Charts
0fa1287a4b6997396c628d5eb98a8cfcd260875a	not-for-merge	'refs/pull/3117/head' of https://github.com/danielgindi/Charts
b01883b02a87da23e9b0d5687e8ab2bc9aae0c11	not-for-merge	'refs/pull/3121/head' of https://github.com/danielgindi/Charts
6601129c2bc592b4096c44beaf0a3b9f59413e14	not-for-merge	'refs/pull/313/head' of https://github.com/danielgindi/Charts
428843f9054b6f78c6d4460949a8968fc93eb294	not-for-merge	'refs/pull/3130/head' of https://github.com/danielgindi/Charts
6f768750b3f36dd652f8df62e0ec23f73b1a8c53	not-for-merge	'refs/pull/3132/head' of https://github.com/danielgindi/Charts
de6524a6d1162c3fadc925dff30ab5918fc292f3	not-for-merge	'refs/pull/3135/head' of https://github.com/danielgindi/Charts
68bb24023b75c114ff614dedaa7c4325ed13bae9	not-for-merge	'refs/pull/3136/head' of https://github.com/danielgindi/Charts
c49d469153cf82f1cb73bbfca5e70c700d0f3388	not-for-merge	'refs/pull/3141/head' of https://github.com/danielgindi/Charts
7de4894f729be7fb46e0540fe9dbc9f8bafcc3f4	not-for-merge	'refs/pull/3141/merge' of https://github.com/danielgindi/Charts
c49d469153cf82f1cb73bbfca5e70c700d0f3388	not-for-merge	'refs/pull/3142/head' of https://github.com/danielgindi/Charts
eca3994941a0a6c3407635df507942f06af8a74b	not-for-merge	'refs/pull/3142/merge' of https://github.com/danielgindi/Charts
972eac5f95fc2b6f2dd0b13857fb2e73eeef8c78	not-for-merge	'refs/pull/3143/head' of https://github.com/danielgindi/Charts
fd7f46ff5c1b3acf65765f47023d5a23247812d1	not-for-merge	'refs/pull/3149/head' of https://github.com/danielgindi/Charts
4f824618a94eec12b2a2f426872475bc4627322a	not-for-merge	'refs/pull/3159/head' of https://github.com/danielgindi/Charts
a37e45089241227fe7aaf758c53bf3fd52cf28e3	not-for-merge	'refs/pull/3160/head' of https://github.com/danielgindi/Charts
52480dbf6be0aa524d37491eaa305657ef260739	not-for-merge	'refs/pull/3164/head' of https://github.com/danielgindi/Charts
8412abaded97ba4b77aa777967760a0f225b4959	not-for-merge	'refs/pull/3167/head' of https://github.com/danielgindi/Charts
fe0af58671728274eb43375396e92ee4203a4a8a	not-for-merge	'refs/pull/3169/head' of https://github.com/danielgindi/Charts
3bc87fb761bda80164915799925d24436babb367	not-for-merge	'refs/pull/3172/head' of https://github.com/danielgindi/Charts
13e3ede83166cb566563141774a0d838a1db7504	not-for-merge	'refs/pull/3173/head' of https://github.com/danielgindi/Charts
68b2ad79f8f231215ee3091d8783f1f655034ee2	not-for-merge	'refs/pull/3173/merge' of https://github.com/danielgindi/Charts
3b145a40fc64f7e466da9546f6223a690b176ab4	not-for-merge	'refs/pull/3174/head' of https://github.com/danielgindi/Charts
44318e3925f2ccd5c340ccfdcdcc506df4e4edee	not-for-merge	'refs/pull/3176/head' of https://github.com/danielgindi/Charts
6e01775b79a1b65d6a1cbcc082e60b6a5850a768	not-for-merge	'refs/pull/3176/merge' of https://github.com/danielgindi/Charts
12f3978a31bcf6e754731155a9c6af7bf1afb1b0	not-for-merge	'refs/pull/3178/head' of https://github.com/danielgindi/Charts
1108ca6674fe8fc928ee0d965d3591ec2e8fbd18	not-for-merge	'refs/pull/3178/merge' of https://github.com/danielgindi/Charts
ef0ac253279450755084401af1ee4ca27eff3d20	not-for-merge	'refs/pull/3179/head' of https://github.com/danielgindi/Charts
fe7c08b4201b754f0ccd100a929926797ca7d4f2	not-for-merge	'refs/pull/3181/head' of https://github.com/danielgindi/Charts
7c10085884093099a0573358c5307110e13433d9	not-for-merge	'refs/pull/3182/head' of https://github.com/danielgindi/Charts
186c99342cffc331944bce2bb2f70f08c2415fbf	not-for-merge	'refs/pull/3182/merge' of https://github.com/danielgindi/Charts
a25665afdc80fd2e300be07fafa7a7ba6682309f	not-for-merge	'refs/pull/3183/head' of https://github.com/danielgindi/Charts
1f1958e8f3ed789cdce7935d4ddb1018bfb40553	not-for-merge	'refs/pull/3187/head' of https://github.com/danielgindi/Charts
30e7ba0b3c147c65cbf48de56139269c7aab716b	not-for-merge	'refs/pull/319/head' of https://github.com/danielgindi/Charts
5d9e5f8a3929eb9619333f6534d4a4743994c823	not-for-merge	'refs/pull/319/merge' of https://github.com/danielgindi/Charts
af101b646ebf74bb735d7df4551fec8a846a48b6	not-for-merge	'refs/pull/3191/head' of https://github.com/danielgindi/Charts
69fd502d0f97fd27e6bcbc7e9c251b1c7d6605b2	not-for-merge	'refs/pull/3198/head' of https://github.com/danielgindi/Charts
c42ffe34cf764e9cf69c309464bd9fb8edaac586	not-for-merge	'refs/pull/3199/head' of https://github.com/danielgindi/Charts
cc322967305d332add5d0dd743e24fa1defbd2a9	not-for-merge	'refs/pull/32/head' of https://github.com/danielgindi/Charts
45c8ecb9e291d18f6d4c1e3dc780ea7756e0249a	not-for-merge	'refs/pull/3201/head' of https://github.com/danielgindi/Charts
e942f99f7d095d05f42e73196f34f01569e812f5	not-for-merge	'refs/pull/3201/merge' of https://github.com/danielgindi/Charts
d45049e58c92795fd266e2da05a9601b7568b1b9	not-for-merge	'refs/pull/3202/head' of https://github.com/danielgindi/Charts
544490a404796cca62a2b1561a3144750ac36d7d	not-for-merge	'refs/pull/3206/head' of https://github.com/danielgindi/Charts
c3416c1626ad4c1d3d87df147f14d76ff0e9aefa	not-for-merge	'refs/pull/3206/merge' of https://github.com/danielgindi/Charts
7a52d792e89bd48d2d4fc0350d7fb76702de7bbd	not-for-merge	'refs/pull/3207/head' of https://github.com/danielgindi/Charts
30e7ba0b3c147c65cbf48de56139269c7aab716b	not-for-merge	'refs/pull/321/head' of https://github.com/danielgindi/Charts
3d26481e9246990afd4f0ac0c1a0d188ce73ce73	not-for-merge	'refs/pull/321/merge' of https://github.com/danielgindi/Charts
38656d186467df4ab3f66e1347c966140def4bb8	not-for-merge	'refs/pull/3214/head' of https://github.com/danielgindi/Charts
deec2d83d6f9dadf52d364955855157e3352c163	not-for-merge	'refs/pull/3214/merge' of https://github.com/danielgindi/Charts
4d85f37a95ea290ad1944c8e1ce22b6d3f61970c	not-for-merge	'refs/pull/3215/head' of https://github.com/danielgindi/Charts
937e8c2180ab7dbf09cdb1ef98df7cbc108a47ff	not-for-merge	'refs/pull/3216/head' of https://github.com/danielgindi/Charts
ffa9455c3442f8de4949820b30bfba2e1924d43d	not-for-merge	'refs/pull/3217/head' of https://github.com/danielgindi/Charts
17adc69b77560edfefc2320eb9c44248a2328ccd	not-for-merge	'refs/pull/3218/head' of https://github.com/danielgindi/Charts
e71320b054d7add785d79295f1c64c3259ce3c17	not-for-merge	'refs/pull/3218/merge' of https://github.com/danielgindi/Charts
30e7ba0b3c147c65cbf48de56139269c7aab716b	not-for-merge	'refs/pull/322/head' of https://github.com/danielgindi/Charts
03885a50fa6b90f260f120bfd2bac14538153cff	not-for-merge	'refs/pull/322/merge' of https://github.com/danielgindi/Charts
891fb8ebcb209515c5f1257c6a85b9a9ad97d6a9	not-for-merge	'refs/pull/3223/head' of https://github.com/danielgindi/Charts
a65a3e7e0207d32657996562a9cec370b9e7b850	not-for-merge	'refs/pull/3225/head' of https://github.com/danielgindi/Charts
ccc4b2ab1bc5cd60cbc14ccdb941b2eb96772d67	not-for-merge	'refs/pull/3226/head' of https://github.com/danielgindi/Charts
86ba08bbb9cb0086eb5511129c09b6bdc02659a4	not-for-merge	'refs/pull/3226/merge' of https://github.com/danielgindi/Charts
fe166419630b97abbdc6d9caf7b3ae78337713b5	not-for-merge	'refs/pull/3229/head' of https://github.com/danielgindi/Charts
243911f51574d3b6478e9928e7093af2ccca3a9c	not-for-merge	'refs/pull/324/head' of https://github.com/danielgindi/Charts
eaa23a3fe43e8d25c6ef2196e7caf6208a387302	not-for-merge	'refs/pull/3252/head' of https://github.com/danielgindi/Charts
52f58b7752e2cf11dbade19e4402c003a66af9fe	not-for-merge	'refs/pull/3252/merge' of https://github.com/danielgindi/Charts
85a3784a1fc86f00aa217df45c9471ae720f4d61	not-for-merge	'refs/pull/3256/head' of https://github.com/danielgindi/Charts
9b1735b67c28c8ae46d5a9a45d9b39d5f10ebc73	not-for-merge	'refs/pull/3256/merge' of https://github.com/danielgindi/Charts
eb2518ae1bd0c26530e3f05c09a89af2bd0b7874	not-for-merge	'refs/pull/3265/head' of https://github.com/danielgindi/Charts
16963877a2e138f5a6b79f4e3d47442478f3822f	not-for-merge	'refs/pull/3278/head' of https://github.com/danielgindi/Charts
ed8cf207bf91f8ca9242577f8010b2052f1929d4	not-for-merge	'refs/pull/3286/head' of https://github.com/danielgindi/Charts
f59aa7664d43cb8f198c2c1c3e979ed3fd320f3a	not-for-merge	'refs/pull/3290/head' of https://github.com/danielgindi/Charts
a5f60085f684e175280c0a5cffecd31d63e20317	not-for-merge	'refs/pull/3291/head' of https://github.com/danielgindi/Charts
b88560affca8a0c3245b7cd2e4890ef3b6d0c339	not-for-merge	'refs/pull/3297/head' of https://github.com/danielgindi/Charts
0730dac6299eedc68fa34e991ed252e959c04480	not-for-merge	'refs/pull/3302/head' of https://github.com/danielgindi/Charts
4cbf1ff0fae39e064b0b4c7d8afb6d68fc9888f0	not-for-merge	'refs/pull/3302/merge' of https://github.com/danielgindi/Charts
c49d469153cf82f1cb73bbfca5e70c700d0f3388	not-for-merge	'refs/pull/3305/head' of https://github.com/danielgindi/Charts
21fc8aa96110f4978b883347ece4c68c9dde826e	not-for-merge	'refs/pull/3309/head' of https://github.com/danielgindi/Charts
6d655b24d4c4066a93eed5922fb0fdf7a4337905	not-for-merge	'refs/pull/3309/merge' of https://github.com/danielgindi/Charts
bf108f2c1390ef5e3ea2ef3586224e616755eed1	not-for-merge	'refs/pull/331/head' of https://github.com/danielgindi/Charts
1bc0822ef03ce5314d54b2cb9ef2bdb8a98356ce	not-for-merge	'refs/pull/331/merge' of https://github.com/danielgindi/Charts
df018a4f390ccd78c136ce9203682dce29a3a9e6	not-for-merge	'refs/pull/3312/head' of https://github.com/danielgindi/Charts
2b7db9dda448f539de54585c994e920beac79be3	not-for-merge	'refs/pull/3316/head' of https://github.com/danielgindi/Charts
2faa8c036b18216eba8bba7c2a3148cff035c706	not-for-merge	'refs/pull/3316/merge' of https://github.com/danielgindi/Charts
284324a0ef54b96612afafc5442f840d725365ba	not-for-merge	'refs/pull/3326/head' of https://github.com/danielgindi/Charts
64e41b5241baa4e5e2a9302be1cb0f98382f413f	not-for-merge	'refs/pull/3329/head' of https://github.com/danielgindi/Charts
e39a40ef6e23edc78b21dfb6398431a7b8bf3bdb	not-for-merge	'refs/pull/3329/merge' of https://github.com/danielgindi/Charts
12ac52395b9e4316e9be322e3a6fd17db3587d20	not-for-merge	'refs/pull/3330/head' of https://github.com/danielgindi/Charts
3da53ecb797790ec07e826652fe90f77fc8211e3	not-for-merge	'refs/pull/3330/merge' of https://github.com/danielgindi/Charts
47d63eab8c1f6f17e2fc082f28a546c38671c13b	not-for-merge	'refs/pull/335/head' of https://github.com/danielgindi/Charts
36148e4710f80dd4586de0537674e4ad4324301c	not-for-merge	'refs/pull/335/merge' of https://github.com/danielgindi/Charts
e1a063f83c05180485928dfba8d274dca9579a38	not-for-merge	'refs/pull/3351/head' of https://github.com/danielgindi/Charts
1cda45689143133606c3be081917a2cca05d568c	not-for-merge	'refs/pull/3355/head' of https://github.com/danielgindi/Charts
eee429419738110eb0a142a2c2226b754dced4a9	not-for-merge	'refs/pull/3355/merge' of https://github.com/danielgindi/Charts
2ffea4bff2110956dfca87754279f198f23970c0	not-for-merge	'refs/pull/3357/head' of https://github.com/danielgindi/Charts
af5ac586c87a1cb33861e421689acffb24a98af0	not-for-merge	'refs/pull/3360/head' of https://github.com/danielgindi/Charts
2872190c82935842b0c6c8510c59c610c24f0047	not-for-merge	'refs/pull/3366/head' of https://github.com/danielgindi/Charts
272eb086bfc80814b6f34fa138b22b13d6ac8f7a	not-for-merge	'refs/pull/3370/head' of https://github.com/danielgindi/Charts
c68570985c51f80e6286398098e921df7d273ac6	not-for-merge	'refs/pull/3403/head' of https://github.com/danielgindi/Charts
4b00c8ff842e36044221c7629874f4c72b190903	not-for-merge	'refs/pull/3406/head' of https://github.com/danielgindi/Charts
aee20f004cc4bfcbda6d1b5431564074cd2e3302	not-for-merge	'refs/pull/3415/head' of https://github.com/danielgindi/Charts
43cab6c30c1a81e70bfc7a65c6a5ed52cf9c6bb6	not-for-merge	'refs/pull/3421/head' of https://github.com/danielgindi/Charts
11f553340e7207ee535c83454fe84f00cc59c516	not-for-merge	'refs/pull/3424/head' of https://github.com/danielgindi/Charts
fd00317d77b77c3241f61569045bcd55f68f20cb	not-for-merge	'refs/pull/3429/head' of https://github.com/danielgindi/Charts
f56b4b5c0f20eae25daa425a1f8f18015d0b1b6b	not-for-merge	'refs/pull/343/head' of https://github.com/danielgindi/Charts
8cf4c385182356462dd0af2d2b1c3520fa031bb5	not-for-merge	'refs/pull/3432/head' of https://github.com/danielgindi/Charts
1b111e08dbb704e8a3c40898fabccd1627893163	not-for-merge	'refs/pull/3435/head' of https://github.com/danielgindi/Charts
6a5ab718f23d6ee9b82ce2bca8b3071758e13a7c	not-for-merge	'refs/pull/3440/head' of https://github.com/danielgindi/Charts
4d2e335ead8de57ec72041ab5b7352a2157a139b	not-for-merge	'refs/pull/3445/head' of https://github.com/danielgindi/Charts
699f1c5e213b33674ceabddfb91e8def29c3904c	not-for-merge	'refs/pull/3459/head' of https://github.com/danielgindi/Charts
6b560fdf6acd8d30ca4bb9bb9c9f00804e88bd5e	not-for-merge	'refs/pull/3459/merge' of https://github.com/danielgindi/Charts
1a442d9bb9de17a925b5115e3f28968dbf930f9d	not-for-merge	'refs/pull/3479/head' of https://github.com/danielgindi/Charts
89706c0b9c4be20d7d73673d7b85c79432f838eb	not-for-merge	'refs/pull/3500/head' of https://github.com/danielgindi/Charts
6e32e3e34a53c7856c44a9af2e87756d67ddd5da	not-for-merge	'refs/pull/3504/head' of https://github.com/danielgindi/Charts
4f2609c459cf5238278bd3f09750b092c4f7c53c	not-for-merge	'refs/pull/3504/merge' of https://github.com/danielgindi/Charts
50805d1f6053b8299c8e70e899d1165f8baee9b8	not-for-merge	'refs/pull/3512/head' of https://github.com/danielgindi/Charts
a109b13885778305eab799b0c2b6a9ef0fd1eb85	not-for-merge	'refs/pull/3512/merge' of https://github.com/danielgindi/Charts
e1f3b713bdd2601b0169ed0db913b823d65bdd72	not-for-merge	'refs/pull/3518/head' of https://github.com/danielgindi/Charts
71f5c3405e6df05f25206210f30555b2d2a93c1f	not-for-merge	'refs/pull/3520/head' of https://github.com/danielgindi/Charts
ed67b785524581ebacca09389263a72676149a26	not-for-merge	'refs/pull/3522/head' of https://github.com/danielgindi/Charts
5d9860f43a3a20e63b7b522bd2b3226d8581db11	not-for-merge	'refs/pull/3528/head' of https://github.com/danielgindi/Charts
33d8e489c98353671f9ab58ce642e78dc6757a17	not-for-merge	'refs/pull/3533/head' of https://github.com/danielgindi/Charts
8d9580377d2af1703710309739fb26abcbd3add2	not-for-merge	'refs/pull/3548/head' of https://github.com/danielgindi/Charts
cff71f1fe1a1cd6dded6cbbf07258af14568fe9c	not-for-merge	'refs/pull/3556/head' of https://github.com/danielgindi/Charts
9f4679bfaa32b173c71728e01bf4e415763b77b8	not-for-merge	'refs/pull/3556/merge' of https://github.com/danielgindi/Charts
7690b2e6d7f014b1f9fa64dffeb556afdb33da6b	not-for-merge	'refs/pull/3558/head' of https://github.com/danielgindi/Charts
45efb61784d68f6d2e961d57482916b10d263b63	not-for-merge	'refs/pull/3563/head' of https://github.com/danielgindi/Charts
6c8872e9dd56dc6d99af6702be0763b43e32b9f1	not-for-merge	'refs/pull/3563/merge' of https://github.com/danielgindi/Charts
b54e9ed130c1f93f3e154ae570305ff171e1d52d	not-for-merge	'refs/pull/3577/head' of https://github.com/danielgindi/Charts
881b72e33ff5b03d281faabdc4f554bcea4eb307	not-for-merge	'refs/pull/3587/head' of https://github.com/danielgindi/Charts
a2923a0f084a688ee6991982d3c68e1b37837d4c	not-for-merge	'refs/pull/359/head' of https://github.com/danielgindi/Charts
5a5121de35051ce0ec54a2cf0905748a4c7cda27	not-for-merge	'refs/pull/3591/head' of https://github.com/danielgindi/Charts
09ad4268e14ada50f336d4f582e63c1d82a08c76	not-for-merge	'refs/pull/3593/head' of https://github.com/danielgindi/Charts
bcab58c6ad5e1d731dbf4287ff3ee48e08beec6d	not-for-merge	'refs/pull/3599/head' of https://github.com/danielgindi/Charts
c427557ee24e65822e252cd964a561ac729b7db1	not-for-merge	'refs/pull/360/head' of https://github.com/danielgindi/Charts
707d627ff05a0353f296aed073f850ff4a82f757	not-for-merge	'refs/pull/3608/head' of https://github.com/danielgindi/Charts
33e8eb1d81624a07789d5cceaaf63de29888cf14	not-for-merge	'refs/pull/3612/head' of https://github.com/danielgindi/Charts
0ca724dff4c5bef37c9f552a1de356d31bf89ee7	not-for-merge	'refs/pull/362/head' of https://github.com/danielgindi/Charts
a3b4138385fbe8728668fb8c2d619290bd194497	not-for-merge	'refs/pull/362/merge' of https://github.com/danielgindi/Charts
a4356691aa6bae6ec32697877d5bbeeedc29a8e0	not-for-merge	'refs/pull/3621/head' of https://github.com/danielgindi/Charts
3bc4742fa36294c83dae14e077b965c240276fb6	not-for-merge	'refs/pull/3636/head' of https://github.com/danielgindi/Charts
55ca2e659b4efe028283673e8aba61e50fb6ae52	not-for-merge	'refs/pull/3636/merge' of https://github.com/danielgindi/Charts
c510e7496d5cae3136d27ceef22a68edef5b0440	not-for-merge	'refs/pull/3637/head' of https://github.com/danielgindi/Charts
93b647955861c4ec7116353e6c096052013de05a	not-for-merge	'refs/pull/3638/head' of https://github.com/danielgindi/Charts
8ae48b98b783ff8eaf8f394cd25a2b5152e44200	not-for-merge	'refs/pull/3650/head' of https://github.com/danielgindi/Charts
3d40b000deb529be869241cdc44baab45d2a4f89	not-for-merge	'refs/pull/3654/head' of https://github.com/danielgindi/Charts
26dfe038f14289125e10bca214f345eb6cf17569	not-for-merge	'refs/pull/3664/head' of https://github.com/danielgindi/Charts
0d3f54b08d9a98cc5a1812ea5d333b86928b81fa	not-for-merge	'refs/pull/367/head' of https://github.com/danielgindi/Charts
a27d1ef6113fcf858f765e87a3d060a8a2b7072d	not-for-merge	'refs/pull/367/merge' of https://github.com/danielgindi/Charts
c1e54339a44bc7e2b5e74b2ebf3693983de78b79	not-for-merge	'refs/pull/3676/head' of https://github.com/danielgindi/Charts
0b8ce2ee917831f894638a33f797d0bd64e949bd	not-for-merge	'refs/pull/3676/merge' of https://github.com/danielgindi/Charts
216f5d88430bd8eedac68ca633cde67bbbbed1c5	not-for-merge	'refs/pull/368/head' of https://github.com/danielgindi/Charts
cf38e7a17901e156189f5378f7010f96df53749e	not-for-merge	'refs/pull/368/merge' of https://github.com/danielgindi/Charts
1fb45fba27e5bb7845a52a9b534361eba7399dd3	not-for-merge	'refs/pull/3680/head' of https://github.com/danielgindi/Charts
238dbacc4523c19abfb9df11c1dc0492531d73d6	not-for-merge	'refs/pull/3681/head' of https://github.com/danielgindi/Charts
dd1cfa7389dce27b14a9df8eec37f3c6947b1fea	not-for-merge	'refs/pull/369/head' of https://github.com/danielgindi/Charts
853a1d246c188adb4456f66fc33a9231f0242bf9	not-for-merge	'refs/pull/369/merge' of https://github.com/danielgindi/Charts
a35f20cacd6765a52bab0d01ce90b8e69adff438	not-for-merge	'refs/pull/3691/head' of https://github.com/danielgindi/Charts
f1670f1cdfbabab465af260eafe5f60a4acc90a6	not-for-merge	'refs/pull/3695/head' of https://github.com/danielgindi/Charts
a0d4d0b220dfd0c75f1ec33e65ade77d7b2db9de	not-for-merge	'refs/pull/37/head' of https://github.com/danielgindi/Charts
dd1cfa7389dce27b14a9df8eec37f3c6947b1fea	not-for-merge	'refs/pull/370/head' of https://github.com/danielgindi/Charts
e0f4fa6f79c495e0793d9549154c0008d836c2c7	not-for-merge	'refs/pull/3709/head' of https://github.com/danielgindi/Charts
a00d57f1ee1aa57f85f0604d492f0b8bc4a5773f	not-for-merge	'refs/pull/371/head' of https://github.com/danielgindi/Charts
fae3be74fafdd1e711919bbedc5b059eedad86c7	not-for-merge	'refs/pull/3715/head' of https://github.com/danielgindi/Charts
d4aa956452d47e5c467e0647200a41ccd9525282	not-for-merge	'refs/pull/372/head' of https://github.com/danielgindi/Charts
d59b2d8a78263a30c59a1bc1004c6d0b252b9cb9	not-for-merge	'refs/pull/3721/head' of https://github.com/danielgindi/Charts
c0d29424499e3c9ee77cb4444c4773e7445e3711	not-for-merge	'refs/pull/373/head' of https://github.com/danielgindi/Charts
ee7a2181fa95735fbcaab4b1c37eadafacf59350	not-for-merge	'refs/pull/3736/head' of https://github.com/danielgindi/Charts
5900abca516a5d54f5881e5826a09fb26bc3f9f9	not-for-merge	'refs/pull/3737/head' of https://github.com/danielgindi/Charts
707433491e31dd5c1f50b0a7d13a16eeeb4c3a14	not-for-merge	'refs/pull/374/head' of https://github.com/danielgindi/Charts
d38b0cdd8493fa5fc1aa2a0c7780cfd08ff46047	not-for-merge	'refs/pull/374/merge' of https://github.com/danielgindi/Charts
44ba57d97832035237ec832cb6b7582686b6e6c1	not-for-merge	'refs/pull/3740/head' of https://github.com/danielgindi/Charts
116556d77492d358ac457b43db447b16bf199c6f	not-for-merge	'refs/pull/3741/head' of https://github.com/danielgindi/Charts
2544f9902089bffec0bb796a742fb39d45f0b483	not-for-merge	'refs/pull/3741/merge' of https://github.com/danielgindi/Charts
eb194dc42d45d5689e25f8e587b3822175149115	not-for-merge	'refs/pull/3747/head' of https://github.com/danielgindi/Charts
22e83fcff482e17d36f576e8dfa3d7e90794b104	not-for-merge	'refs/pull/375/head' of https://github.com/danielgindi/Charts
f53604abf7ae151ddf805e2ad366b992fe542c14	not-for-merge	'refs/pull/3754/head' of https://github.com/danielgindi/Charts
7aa9e541e795f92f55466ce6c8a5662dd9b7bc26	not-for-merge	'refs/pull/3758/head' of https://github.com/danielgindi/Charts
a54496bae213c0b78f93017c94e4f9b4791df751	not-for-merge	'refs/pull/376/head' of https://github.com/danielgindi/Charts
55a9cc84a7dfe901eb3f59065cad98a6e0de21e2	not-for-merge	'refs/pull/3764/head' of https://github.com/danielgindi/Charts
2a8a01f8b2d54780cf46389a78de388f46f955e6	not-for-merge	'refs/pull/3766/head' of https://github.com/danielgindi/Charts
eaab970594dd35cbc6abd327605b2c45c9bd29d1	not-for-merge	'refs/pull/3766/merge' of https://github.com/danielgindi/Charts
b68481e024045c70dc759145d6471fd35f918d5e	not-for-merge	'refs/pull/377/head' of https://github.com/danielgindi/Charts
4eedf03300a53ea32429e2baefa3a28f5bd8808b	not-for-merge	'refs/pull/377/merge' of https://github.com/danielgindi/Charts
4ce1f23d07f8cb219710818be2d3b81741253a17	not-for-merge	'refs/pull/3770/head' of https://github.com/danielgindi/Charts
18a0ff7ae57d102028f15aaf8990126cde090fc4	not-for-merge	'refs/pull/3775/head' of https://github.com/danielgindi/Charts
2927f76c02b0c88304ffc563817e24d65b96ac84	not-for-merge	'refs/pull/3778/head' of https://github.com/danielgindi/Charts
9a7b9226ffd473e8ccf6abb6f87de15dabc0e9c3	not-for-merge	'refs/pull/3779/head' of https://github.com/danielgindi/Charts
092007edcdc85b797399b52077c58f7359bfddeb	not-for-merge	'refs/pull/3784/head' of https://github.com/danielgindi/Charts
19cde51a3f2dae91f28f7fc343d43817670639a8	not-for-merge	'refs/pull/3792/head' of https://github.com/danielgindi/Charts
ab3730642f99874dbb620c970c29a0c10f38f1a6	not-for-merge	'refs/pull/3797/head' of https://github.com/danielgindi/Charts
8f4556b4f7322ab2d40c768cd336e8d5b0084e82	not-for-merge	'refs/pull/3799/head' of https://github.com/danielgindi/Charts
7d2e2dbfcac479996d0bf3ab2ee8596e7a084483	not-for-merge	'refs/pull/3804/head' of https://github.com/danielgindi/Charts
2bec864cad9b8a15c2cd92ed831568efbbfe6845	not-for-merge	'refs/pull/3807/head' of https://github.com/danielgindi/Charts
1644038a8688ecbf4cfe66e4b61a417523f0c26b	not-for-merge	'refs/pull/3807/merge' of https://github.com/danielgindi/Charts
24897dbcf01705b4f8bcc86eb0f973afe2a1e6d4	not-for-merge	'refs/pull/3814/head' of https://github.com/danielgindi/Charts
5e8c9b1193766109afef33728ecf990720b3dd03	not-for-merge	'refs/pull/3815/head' of https://github.com/danielgindi/Charts
8729aadfd281b56d52ba3ddf974b04e99fa02e36	not-for-merge	'refs/pull/382/head' of https://github.com/danielgindi/Charts
421ac8a7efc737c05b54bcf198bf52a363da7511	not-for-merge	'refs/pull/3830/head' of https://github.com/danielgindi/Charts
7b7d78ef89c2526a0e843505c8c19f75f672490c	not-for-merge	'refs/pull/3846/head' of https://github.com/danielgindi/Charts
636f6237266d6cad976a442bdbc71c8f369a7d00	not-for-merge	'refs/pull/3847/head' of https://github.com/danielgindi/Charts
e1f28b00b5f5ff90a4865e433123e6a9a11e06e6	not-for-merge	'refs/pull/3852/head' of https://github.com/danielgindi/Charts
d096474b61963ba1e8c26831ea74ea7551e062ec	not-for-merge	'refs/pull/3854/head' of https://github.com/danielgindi/Charts
326597ede645af3bd4a972459fb6814d50d9e35b	not-for-merge	'refs/pull/386/head' of https://github.com/danielgindi/Charts
a155ab3e0129da7c2b1533860dcd3a0a83f45842	not-for-merge	'refs/pull/3862/head' of https://github.com/danielgindi/Charts
022826ea562ec06b9efc5585d9fc5f61463d48a5	not-for-merge	'refs/pull/3863/head' of https://github.com/danielgindi/Charts
46a301882974ff601a3c6c015ba68bddc9c7a640	not-for-merge	'refs/pull/3864/head' of https://github.com/danielgindi/Charts
2e808efa2b16da229fa7f1aec4394d92b9194dd3	not-for-merge	'refs/pull/3865/head' of https://github.com/danielgindi/Charts
30d997a9a3c2a3e0812cf50a2608d74f916a39a4	not-for-merge	'refs/pull/3874/head' of https://github.com/danielgindi/Charts
e67bec929edcd52a6169b519e1f56ff1a76a8463	not-for-merge	'refs/pull/3879/head' of https://github.com/danielgindi/Charts
20066718d04167438d18804279f27ba958c3e551	not-for-merge	'refs/pull/388/head' of https://github.com/danielgindi/Charts
5b125b2cdaa4882781ae8c4838d8a66804859c74	not-for-merge	'refs/pull/3883/head' of https://github.com/danielgindi/Charts
4f5cbf71d2ee19e17451afb5ad6b1d99f0fbc233	not-for-merge	'refs/pull/3884/head' of https://github.com/danielgindi/Charts
8b471b9492948ec2c4abb1f68b8e6c64bfd51a60	not-for-merge	'refs/pull/3889/head' of https://github.com/danielgindi/Charts
bf31d12fb62de04bf39ac1c5b670b22ba3e7c9bc	not-for-merge	'refs/pull/3891/head' of https://github.com/danielgindi/Charts
b053b03ccb37604175a9990ee8a9edf5c65c9cb2	not-for-merge	'refs/pull/3892/head' of https://github.com/danielgindi/Charts
afd3cd695af6b635da6d5dce9c70be9247dbd135	not-for-merge	'refs/pull/3897/head' of https://github.com/danielgindi/Charts
05327c8a8f39cf581082299b4e18b0181fa108c4	not-for-merge	'refs/pull/3899/head' of https://github.com/danielgindi/Charts
6fb075f7f3296ada71c18532b4f53d0c181d9b58	not-for-merge	'refs/pull/3900/head' of https://github.com/danielgindi/Charts
0995387a7ad18d65da4e8dad926e63a10e59bfde	not-for-merge	'refs/pull/3906/head' of https://github.com/danielgindi/Charts
4f36494cd14f1b6341c51e26984f9bfc1896d6ad	not-for-merge	'refs/pull/392/head' of https://github.com/danielgindi/Charts
c332010c9a6f58d20cca697f7305eaf572cd81af	not-for-merge	'refs/pull/393/head' of https://github.com/danielgindi/Charts
db5333254a676544a7afa95291d72abaf04dce44	not-for-merge	'refs/pull/3935/head' of https://github.com/danielgindi/Charts
e60011f8bc810aa1a657f2539ea704e88f4b90ec	not-for-merge	'refs/pull/394/head' of https://github.com/danielgindi/Charts
cd61d6f1e0fa04a738a4ff772fd57f8a246b1e27	not-for-merge	'refs/pull/394/merge' of https://github.com/danielgindi/Charts
3bc223f6d18639ae3f427d069285492e4792be3b	not-for-merge	'refs/pull/3944/head' of https://github.com/danielgindi/Charts
26b03da2a5163b44ddbca60aa4dba18bf9464c46	not-for-merge	'refs/pull/395/head' of https://github.com/danielgindi/Charts
56c4f55e9435c0e8c50609868a3a0a9bdb022a54	not-for-merge	'refs/pull/3965/head' of https://github.com/danielgindi/Charts
2aec279099804684cd22efc2b898aa3f9f11f717	not-for-merge	'refs/pull/3973/head' of https://github.com/danielgindi/Charts
f278e081e9626f0bebf38cb6f8478f95f602138d	not-for-merge	'refs/pull/3982/head' of https://github.com/danielgindi/Charts
efe7c37a7fefa03ddc376f84b6dcf1aa1ce6b813	not-for-merge	'refs/pull/3982/merge' of https://github.com/danielgindi/Charts
84318d395324e3981e496f7c5ff2b4b4c7ddff0e	not-for-merge	'refs/pull/3983/head' of https://github.com/danielgindi/Charts
adcc3711cad737ddfd4c6fce84669efd28ac65bb	not-for-merge	'refs/pull/3983/merge' of https://github.com/danielgindi/Charts
c2db7b9ad54e3398166ffd72f24b2a96d18647d3	not-for-merge	'refs/pull/3994/head' of https://github.com/danielgindi/Charts
5b59440f4ad0318747eb0af5882b9d746fdb0206	not-for-merge	'refs/pull/3996/head' of https://github.com/danielgindi/Charts
a351d15b9ba78af11b56f9dd04716082376813cf	not-for-merge	'refs/pull/4002/head' of https://github.com/danielgindi/Charts
14088fa71e6367470fe75f310896747b8a9916ff	not-for-merge	'refs/pull/4004/head' of https://github.com/danielgindi/Charts
e6b01f77f3d6b0ef160bad6ebfaba4cfd4e042c6	not-for-merge	'refs/pull/4006/head' of https://github.com/danielgindi/Charts
7d1296d102d2683bc6d3014626e07916cb715c80	not-for-merge	'refs/pull/4017/head' of https://github.com/danielgindi/Charts
68a330cc7d9ecb9520933ba261e27012d531ca1e	not-for-merge	'refs/pull/4027/head' of https://github.com/danielgindi/Charts
cbffda6f7d857d37d4dd7702ae28ed67aaaefad1	not-for-merge	'refs/pull/4027/merge' of https://github.com/danielgindi/Charts
40f0dd884357bb7ae2d01c196ef7a628f38ff082	not-for-merge	'refs/pull/4029/head' of https://github.com/danielgindi/Charts
259721ac6f0841eb8156da65f642358cec0ba0ed	not-for-merge	'refs/pull/4042/head' of https://github.com/danielgindi/Charts
a513e6fcfb3a90d47b493595d2e7b4924a7045d8	not-for-merge	'refs/pull/4044/head' of https://github.com/danielgindi/Charts
ca4fd2639354dda59598f5ac659a8ef145a0b63e	not-for-merge	'refs/pull/4044/merge' of https://github.com/danielgindi/Charts
9c4f738575574817cfc8c6e8b81e7bae3e7cf184	not-for-merge	'refs/pull/4045/head' of https://github.com/danielgindi/Charts
23ea152d3d1c24da231a29375243bf9d36bfafc9	not-for-merge	'refs/pull/4048/head' of https://github.com/danielgindi/Charts
28e066197896205dee2270ac43a5c5ce2890d4b8	not-for-merge	'refs/pull/4050/head' of https://github.com/danielgindi/Charts
acb436a1b7f9972a66af0e52082f10c6cd85e7aa	not-for-merge	'refs/pull/4055/head' of https://github.com/danielgindi/Charts
42f7539454a48cb8ad1e22bb2e31ab1df6b774c1	not-for-merge	'refs/pull/4064/head' of https://github.com/danielgindi/Charts
bbf72511d24e766b1712ec79ac1f041e5280d97c	not-for-merge	'refs/pull/408/head' of https://github.com/danielgindi/Charts
b99742b64cb6f2aae2f0a4a4720c4536a8412d59	not-for-merge	'refs/pull/408/merge' of https://github.com/danielgindi/Charts
d3bed279e5e5b63462098d60a3acbd74a2b3d433	not-for-merge	'refs/pull/4084/head' of https://github.com/danielgindi/Charts
45212333303a823984f25e445cf9e4b87d317e58	not-for-merge	'refs/pull/4084/merge' of https://github.com/danielgindi/Charts
f43f9e11543d49507a07f2c6496c045f770005e6	not-for-merge	'refs/pull/4089/head' of https://github.com/danielgindi/Charts
186164097466521537c5d51c2710965a630d66df	not-for-merge	'refs/pull/4091/head' of https://github.com/danielgindi/Charts
952b5c0785a35a330b0a3e3bac9b0b383a6d3c35	not-for-merge	'refs/pull/4094/head' of https://github.com/danielgindi/Charts
a84b379feecf9040ff3abd309cfef912028a9cea	not-for-merge	'refs/pull/4100/head' of https://github.com/danielgindi/Charts
9f7e95ca35f20b728be43c5f9001c8db747d1a7a	not-for-merge	'refs/pull/4105/head' of https://github.com/danielgindi/Charts
e9a39a239ba0c35e11b5fa9aa6df0ddabc2181d7	not-for-merge	'refs/pull/4105/merge' of https://github.com/danielgindi/Charts
cdf8c5c0d6d5958b82e17a9fac1f233f4eff18e3	not-for-merge	'refs/pull/4118/head' of https://github.com/danielgindi/Charts
944a5f629fc1d74b686b53ac78c45149090383e4	not-for-merge	'refs/pull/412/head' of https://github.com/danielgindi/Charts
57d03f341860b8e964f99bc195cd29a804996614	not-for-merge	'refs/pull/412/merge' of https://github.com/danielgindi/Charts
f0adde5fe425e59c822448435eaa4a3b2e5eb677	not-for-merge	'refs/pull/4123/head' of https://github.com/danielgindi/Charts
eb385496d16fe4d3db3aac197e2abf62fcbe96ae	not-for-merge	'refs/pull/413/head' of https://github.com/danielgindi/Charts
52d0ddd58cadac6b81bd542b89727c8283cbbeda	not-for-merge	'refs/pull/413/merge' of https://github.com/danielgindi/Charts
781974dce253bc55cf3eed3a28d84014c3a04768	not-for-merge	'refs/pull/4136/head' of https://github.com/danielgindi/Charts
58b95207c41d9a133c204ba58731c52f593fdc1c	not-for-merge	'refs/pull/4136/merge' of https://github.com/danielgindi/Charts
f38ab98f073fc6491e5b2048e238f510694d8dc0	not-for-merge	'refs/pull/4141/head' of https://github.com/danielgindi/Charts
6dbae2044b6cffcadfb337c1af4019dbf24cad0c	not-for-merge	'refs/pull/4149/head' of https://github.com/danielgindi/Charts
7af588b4ff79191aa9cfb6258a62b1bbb8acc131	not-for-merge	'refs/pull/415/head' of https://github.com/danielgindi/Charts
66d3ab493697f8d2c907218b828e0117401faf59	not-for-merge	'refs/pull/415/merge' of https://github.com/danielgindi/Charts
7589b094c3fd5af6e17e8b9f3a43b4e1eb8f9141	not-for-merge	'refs/pull/4153/head' of https://github.com/danielgindi/Charts
5aa9d8575dadd10028b9bf9f0983792050947c12	not-for-merge	'refs/pull/416/head' of https://github.com/danielgindi/Charts
afaad9d2889539a4a3b880f40f889af934bea46d	not-for-merge	'refs/pull/416/merge' of https://github.com/danielgindi/Charts
7ba56d22ab81f3024f52add83c638bdfb86ea5ee	not-for-merge	'refs/pull/4162/head' of https://github.com/danielgindi/Charts
2b96c3c35e973c19347fa12856399b73ac8dad6c	not-for-merge	'refs/pull/4163/head' of https://github.com/danielgindi/Charts
03bc12e219d96a67e1f7371115cd7bc8af30c30a	not-for-merge	'refs/pull/4168/head' of https://github.com/danielgindi/Charts
fb9f8a11b84ecc00459a96ca99ab17f42b4eda50	not-for-merge	'refs/pull/4168/merge' of https://github.com/danielgindi/Charts
d5f7ba188f680bb4e50925761cd08ff0b142d158	not-for-merge	'refs/pull/4170/head' of https://github.com/danielgindi/Charts
30bcc23a8e7bcbb2f1cfcb4be20224063c70b2e7	not-for-merge	'refs/pull/4170/merge' of https://github.com/danielgindi/Charts
a90cdffe17cd8bca0a9e17a024b4007163be5e0c	not-for-merge	'refs/pull/4171/head' of https://github.com/danielgindi/Charts
dbf90e3df7bae16547eba1d4338492ce42e4ed0d	not-for-merge	'refs/pull/4175/head' of https://github.com/danielgindi/Charts
489240969dfeb92c46e237f2e4463f8800e99cc1	not-for-merge	'refs/pull/4178/head' of https://github.com/danielgindi/Charts
e89e100ee9c0ac4bda3b950f10f5173c641b5c0f	not-for-merge	'refs/pull/4179/head' of https://github.com/danielgindi/Charts
f1005f04ba327e168a33e20f11bb3e18ab73022b	not-for-merge	'refs/pull/418/head' of https://github.com/danielgindi/Charts
6be94470736bac9b490ce193497627f456327c3e	not-for-merge	'refs/pull/418/merge' of https://github.com/danielgindi/Charts
9996d489ef8569c33ad01d710109bf341b800638	not-for-merge	'refs/pull/4180/head' of https://github.com/danielgindi/Charts
e50bc31de36ce81da36e74c11f2eef0d711279a3	not-for-merge	'refs/pull/4186/head' of https://github.com/danielgindi/Charts
6cc21c30ae6d0965d5c89bcbad31971ade17dfa5	not-for-merge	'refs/pull/4186/merge' of https://github.com/danielgindi/Charts
ab9d57b12548fadeb8e83852d364afa98bc49099	not-for-merge	'refs/pull/4197/head' of https://github.com/danielgindi/Charts
49f08227bc7a61cb81543548ebac475b008815c9	not-for-merge	'refs/pull/4199/head' of https://github.com/danielgindi/Charts
7ed9441add00b738e651bec59ef50f818a414e40	not-for-merge	'refs/pull/420/head' of https://github.com/danielgindi/Charts
832d3979572b3958ee1bf93e1c1a4ec3252dd1fc	not-for-merge	'refs/pull/4211/head' of https://github.com/danielgindi/Charts
357178a7b8ca5b4eb9e0f43ecbd3ebac1185aecc	not-for-merge	'refs/pull/4211/merge' of https://github.com/danielgindi/Charts
b8032d52d966afffc98c502ca2956af39340ac52	not-for-merge	'refs/pull/4213/head' of https://github.com/danielgindi/Charts
f44e4e1fd93cb679f39bcc0d535ee0fcee22f142	not-for-merge	'refs/pull/422/head' of https://github.com/danielgindi/Charts
57732b400a08478ab1c97df8553c239322289cbf	not-for-merge	'refs/pull/422/merge' of https://github.com/danielgindi/Charts
5067c0424499ca00b9cfaee07026d5d0aa9fadd6	not-for-merge	'refs/pull/4238/head' of https://github.com/danielgindi/Charts
c6d4cbaed530b7246d7f5d62a3b70faa1f6d3a46	not-for-merge	'refs/pull/4238/merge' of https://github.com/danielgindi/Charts
13d86ce53c91571541028ba30dd5b15c333ef002	not-for-merge	'refs/pull/425/head' of https://github.com/danielgindi/Charts
3ea06631a1255e3d9d095d6131e98206f395bbdb	not-for-merge	'refs/pull/425/merge' of https://github.com/danielgindi/Charts
66256889f27db37123e4989d4d33ae0ee67db685	not-for-merge	'refs/pull/4254/head' of https://github.com/danielgindi/Charts
147cf02de4d7b92ccb6f30024200f993049c6d52	not-for-merge	'refs/pull/4256/head' of https://github.com/danielgindi/Charts
827f59bfc66acc7271522e9655ef1c556b91bb79	not-for-merge	'refs/pull/4257/head' of https://github.com/danielgindi/Charts
8f6306c7a40e6f01437898fca080786dee08432b	not-for-merge	'refs/pull/4258/head' of https://github.com/danielgindi/Charts
ca4ab8d8f4f0b10abb58183c7bb161a4a225c7ea	not-for-merge	'refs/pull/4259/head' of https://github.com/danielgindi/Charts
a1219454815e9bcdc7cbd6c6fca0b5785d35eaaf	not-for-merge	'refs/pull/4259/merge' of https://github.com/danielgindi/Charts
b69b6046cf87619c2c4893bda0a5b3634c93c801	not-for-merge	'refs/pull/4265/head' of https://github.com/danielgindi/Charts
4e15fd4c9ba98545817c0904cf186ffa01263e53	not-for-merge	'refs/pull/4265/merge' of https://github.com/danielgindi/Charts
04555a74ae75995a4b5e18441ae4edf8cbdd7b38	not-for-merge	'refs/pull/4266/head' of https://github.com/danielgindi/Charts
db95c0f43f3122e273e5de09d800be82ce7d08b1	not-for-merge	'refs/pull/4268/head' of https://github.com/danielgindi/Charts
f48e20c2048bf4f3bf713167454bc1f72defe5de	not-for-merge	'refs/pull/4269/head' of https://github.com/danielgindi/Charts
53f176335bb1d8df211b30bc374a3a72a2759dab	not-for-merge	'refs/pull/4270/head' of https://github.com/danielgindi/Charts
a28ccdc6e67f2f279e1550a939191307c40ccc17	not-for-merge	'refs/pull/4271/head' of https://github.com/danielgindi/Charts
ae6fd3e526fc4b98c636ca22beb08d714c29ba99	not-for-merge	'refs/pull/4272/head' of https://github.com/danielgindi/Charts
9a9b04c2ec8bbdbd14d1de3036c3b72411b917d8	not-for-merge	'refs/pull/4273/head' of https://github.com/danielgindi/Charts
66d50a87bbc39ebf3190158c54b23befc1716f1d	not-for-merge	'refs/pull/4275/head' of https://github.com/danielgindi/Charts
209357725b4b027b6303ca8118a7836790c056c5	not-for-merge	'refs/pull/4277/head' of https://github.com/danielgindi/Charts
ebcf4a1a19ba513ec40fcca1e68effa7e9344efb	not-for-merge	'refs/pull/4283/head' of https://github.com/danielgindi/Charts
fce71106e2277e8dc760beac373ea32bf306a685	not-for-merge	'refs/pull/4286/head' of https://github.com/danielgindi/Charts
f83591be4372d610750fc0bb954b42c40b6966a4	not-for-merge	'refs/pull/4297/head' of https://github.com/danielgindi/Charts
ae9cc93208c9a19619934d9e23578eae5a20dbb4	not-for-merge	'refs/pull/4298/head' of https://github.com/danielgindi/Charts
8f02d1cad92f998e5decfa8dc0607af226da878a	not-for-merge	'refs/pull/4298/merge' of https://github.com/danielgindi/Charts
d6687bcad7dac949a0d3fc2e0f4f1655a0895364	not-for-merge	'refs/pull/432/head' of https://github.com/danielgindi/Charts
62e063cdaf3aaef6810f073586a3490dcdf1cd83	not-for-merge	'refs/pull/4321/head' of https://github.com/danielgindi/Charts
906de5266962ceac5a0605485504f24b60664e16	not-for-merge	'refs/pull/4325/head' of https://github.com/danielgindi/Charts
2cde1379c2bb856457418cc8ef53640de24b27bf	not-for-merge	'refs/pull/4327/head' of https://github.com/danielgindi/Charts
a53bfc8986cd861c1ab437a5a1508cbe0c207aed	not-for-merge	'refs/pull/4339/head' of https://github.com/danielgindi/Charts
804008c137502ef5299806b7f2f0c7afa10df8aa	not-for-merge	'refs/pull/4345/head' of https://github.com/danielgindi/Charts
c022f162d7117bab17c9776a1465ca81a4257323	not-for-merge	'refs/pull/4345/merge' of https://github.com/danielgindi/Charts
4b2332765e61c6ce78ac7892266784532962a39b	not-for-merge	'refs/pull/4363/head' of https://github.com/danielgindi/Charts
ff7a2fc0d191a8d63c82ddea2b3d09146fa78c32	not-for-merge	'refs/pull/4366/head' of https://github.com/danielgindi/Charts
9f257a852a7eb82a635fc315fd33306bce5fe489	not-for-merge	'refs/pull/4373/head' of https://github.com/danielgindi/Charts
32306652f6807b282130b37e25b16108ec3b59b2	not-for-merge	'refs/pull/4373/merge' of https://github.com/danielgindi/Charts
dd7439a5c825b411300a831dada775f3b574615d	not-for-merge	'refs/pull/4375/head' of https://github.com/danielgindi/Charts
7a7cd9043f4aadd6b01c3075b5cbbf870ed28fe9	not-for-merge	'refs/pull/4375/merge' of https://github.com/danielgindi/Charts
e1b49827e732c0598d2319f5b5b10acbebe79df9	not-for-merge	'refs/pull/438/head' of https://github.com/danielgindi/Charts
77ce03753d9bfac97bdef28c1bb0154e524ab75f	not-for-merge	'refs/pull/4382/head' of https://github.com/danielgindi/Charts
9dc04e1a015fda9d13d8020a97f8c6aaa1621455	not-for-merge	'refs/pull/4382/merge' of https://github.com/danielgindi/Charts
8317226c30dafbdc536689b7e76218d28654203e	not-for-merge	'refs/pull/4395/head' of https://github.com/danielgindi/Charts
0f1f03312523dcca873469fd4377b259e23df0f0	not-for-merge	'refs/pull/4399/head' of https://github.com/danielgindi/Charts
f46d66cab23bde7a0bd298d62ea362d522e1fdd5	not-for-merge	'refs/pull/4400/head' of https://github.com/danielgindi/Charts
e59ade133c3976ec19e3ebba7bd3c986bee4003e	not-for-merge	'refs/pull/4411/head' of https://github.com/danielgindi/Charts
d1ad7b6c2c3c737689b913381f9b7ac4ea0b918e	not-for-merge	'refs/pull/4411/merge' of https://github.com/danielgindi/Charts
5bbb27f04457d7c297136984d6573c21fb1afc3f	not-for-merge	'refs/pull/4422/head' of https://github.com/danielgindi/Charts
6ddc737ee38a039672daa4747210751cc6b010a9	not-for-merge	'refs/pull/4424/head' of https://github.com/danielgindi/Charts
ec081614c412f406fe4d6f7a0e09b9c96b0ab917	not-for-merge	'refs/pull/4431/head' of https://github.com/danielgindi/Charts
dbd0d94c6762aff8a9df7a2745cc35501582178d	not-for-merge	'refs/pull/4431/merge' of https://github.com/danielgindi/Charts
86d8e626b65b54fa029fcbcb24ca8a305c31348e	not-for-merge	'refs/pull/4432/head' of https://github.com/danielgindi/Charts
92e4cf39997451c721c835b5bb1c20d7252d8699	not-for-merge	'refs/pull/4432/merge' of https://github.com/danielgindi/Charts
f92092cce747500e825727957de02fde2252f7fa	not-for-merge	'refs/pull/4435/head' of https://github.com/danielgindi/Charts
afe02e5a911b87f0a16faf4d54d7a60273646406	not-for-merge	'refs/pull/4452/head' of https://github.com/danielgindi/Charts
75239a3c9f36b6a0f4278306bfdb743b61f54f84	not-for-merge	'refs/pull/4454/head' of https://github.com/danielgindi/Charts
05a59f7c858cb423e28c62fdbe0bef13e0a6cf57	not-for-merge	'refs/pull/4456/head' of https://github.com/danielgindi/Charts
927e3dd500b2b1eac5a3320a11fe00e2f36c6ea5	not-for-merge	'refs/pull/4459/head' of https://github.com/danielgindi/Charts
cc2e852e696ba7fb197bd188cad2118d05dfd407	not-for-merge	'refs/pull/4461/head' of https://github.com/danielgindi/Charts
bf93617b7e84d69221ad392e38de10c520e27d92	not-for-merge	'refs/pull/4464/head' of https://github.com/danielgindi/Charts
9d73463c1507216729ecf30b4fc749cff1700bf9	not-for-merge	'refs/pull/4476/head' of https://github.com/danielgindi/Charts
8f82705f1359a2f477fddcd45ee6043dc3449015	not-for-merge	'refs/pull/4478/head' of https://github.com/danielgindi/Charts
b03db8acc634e1e0d855595ea3f8b82f735b4fce	not-for-merge	'refs/pull/4493/head' of https://github.com/danielgindi/Charts
1829ab3f71739d7318e6767097759a583219d656	not-for-merge	'refs/pull/4494/head' of https://github.com/danielgindi/Charts
2cae4a38d0c5561857b54818187891b3fe508cbb	not-for-merge	'refs/pull/4494/merge' of https://github.com/danielgindi/Charts
47725b3ecf9f35ffb0d9ce0af72bf3cc44ba2c58	not-for-merge	'refs/pull/4497/head' of https://github.com/danielgindi/Charts
58bebab64ac9e2e62eeed6b752213a6da6cb9cce	not-for-merge	'refs/pull/45/head' of https://github.com/danielgindi/Charts
b5f2367bb355da53eaf00f6c57f79bd208651285	not-for-merge	'refs/pull/4516/head' of https://github.com/danielgindi/Charts
f783e894a45c7b32c4ea28d072319e230ec4d47d	not-for-merge	'refs/pull/4531/head' of https://github.com/danielgindi/Charts
f1dced83811b3727b53c8a3f9902181f3493e010	not-for-merge	'refs/pull/4532/head' of https://github.com/danielgindi/Charts
cbbcbdb89423490a870e1242c121867a401b63fe	not-for-merge	'refs/pull/4537/head' of https://github.com/danielgindi/Charts
34bb8457dc54e134deaddabcd1aea822da9efef5	not-for-merge	'refs/pull/4538/head' of https://github.com/danielgindi/Charts
848b61240a16bafba9f747d8a9de6f7a58dcd0a5	not-for-merge	'refs/pull/4539/head' of https://github.com/danielgindi/Charts
662bc35d5ef6a2b744fafdb55ddc6a0c24486b28	not-for-merge	'refs/pull/4550/head' of https://github.com/danielgindi/Charts
ddd8af9ed442d7cf8a1c86015d1dad856a7e9c79	not-for-merge	'refs/pull/4553/head' of https://github.com/danielgindi/Charts
fa0aa58ee7534ec5676f567efbdfbd8f49d3089e	not-for-merge	'refs/pull/4560/head' of https://github.com/danielgindi/Charts
4cdbe25ea3bb0bf852c2e164a418b9e2bb681aad	not-for-merge	'refs/pull/4563/head' of https://github.com/danielgindi/Charts
a8f7c499744af5818e0ff89620bb0e628a65bd1b	not-for-merge	'refs/pull/4564/head' of https://github.com/danielgindi/Charts
ef2fb5beb552dcd31ff5db56625b8fff065485df	not-for-merge	'refs/pull/4565/head' of https://github.com/danielgindi/Charts
1eb4cc7041ed42d346b9a8b5f4d46ced920eeae2	not-for-merge	'refs/pull/4565/merge' of https://github.com/danielgindi/Charts
c58e44145423939e2accfeac3597761699afbc7c	not-for-merge	'refs/pull/4566/head' of https://github.com/danielgindi/Charts
984c8db2fd6cfa73b74246f2a5af71832ad249a4	not-for-merge	'refs/pull/4566/merge' of https://github.com/danielgindi/Charts
d755877b962bb98b66e5b3d4d317e60cfaf81ff4	not-for-merge	'refs/pull/457/head' of https://github.com/danielgindi/Charts
b78a014be5e9a8922ad60229cf4d3c4e6c821212	not-for-merge	'refs/pull/4573/head' of https://github.com/danielgindi/Charts
c4387f23e56fc79d5c79c6efc7a5e823e71e2b6b	not-for-merge	'refs/pull/4574/head' of https://github.com/danielgindi/Charts
1de35ceff8d5c770ad130e9e306aa5e1506a7071	not-for-merge	'refs/pull/4576/head' of https://github.com/danielgindi/Charts
2e9adcc8086fe88c077d3a54690ae506eb86b5fc	not-for-merge	'refs/pull/4577/head' of https://github.com/danielgindi/Charts
33349fe91917b2c798320c9b1d0b748504d26304	not-for-merge	'refs/pull/4580/head' of https://github.com/danielgindi/Charts
32a11bb82465757dab8798f51c718a682b2dd6c5	not-for-merge	'refs/pull/4583/head' of https://github.com/danielgindi/Charts
cd26f02b966cec05e1061404944615772df424fc	not-for-merge	'refs/pull/4583/merge' of https://github.com/danielgindi/Charts
f8da416e277090bfe30fec49701e62c030e5bb1e	not-for-merge	'refs/pull/4585/head' of https://github.com/danielgindi/Charts
074f821b01a3d69b648bc2a9f3ca7bd8ef3aaa12	not-for-merge	'refs/pull/4585/merge' of https://github.com/danielgindi/Charts
7331eede62e200d8e8246302ed1428a5c5dfdcd6	not-for-merge	'refs/pull/4588/head' of https://github.com/danielgindi/Charts
6f26c63a51b340c762b7290053a8be6d1028537b	not-for-merge	'refs/pull/4588/merge' of https://github.com/danielgindi/Charts
320a852b2f53b6c2b11a77a1aed8da806dd161b1	not-for-merge	'refs/pull/4589/head' of https://github.com/danielgindi/Charts
7c052534d5c0d18eddaeecbb45d40bd2ce5b7dd3	not-for-merge	'refs/pull/4589/merge' of https://github.com/danielgindi/Charts
b19635789d5609217ec3f70998558bc06b2c50e1	not-for-merge	'refs/pull/4592/head' of https://github.com/danielgindi/Charts
e73cb9fa546f120bde179d5f92a6b052da7330b1	not-for-merge	'refs/pull/4596/head' of https://github.com/danielgindi/Charts
579263037d59496f35fed5aa33b7cd3205adee4c	not-for-merge	'refs/pull/4596/merge' of https://github.com/danielgindi/Charts
2f34b09522369d40b07d01c9db0eae4a52c323e9	not-for-merge	'refs/pull/4598/head' of https://github.com/danielgindi/Charts
3178a9e4c37b756f856dada5ade9b2c63b153e8d	not-for-merge	'refs/pull/4598/merge' of https://github.com/danielgindi/Charts
5320f30b8a68a1f15163a090bdf1fd0aeed476e5	not-for-merge	'refs/pull/460/head' of https://github.com/danielgindi/Charts
70592d0c18281ff70e28a299b402f7e1cce78551	not-for-merge	'refs/pull/460/merge' of https://github.com/danielgindi/Charts
a90a873a3bb2801482b0f3b8374757f109107d9f	not-for-merge	'refs/pull/4601/head' of https://github.com/danielgindi/Charts
547d59bfc0ceac165217b3df3a11d5466df692e3	not-for-merge	'refs/pull/4601/merge' of https://github.com/danielgindi/Charts
5c48104d73724a37eddd019092b936aad9bf0309	not-for-merge	'refs/pull/4608/head' of https://github.com/danielgindi/Charts
f8475700413134781b41416db826f3fdfb1d3939	not-for-merge	'refs/pull/461/head' of https://github.com/danielgindi/Charts
407faecd1ec3ea6f0ff1fae1c192c010cdd9e3fc	not-for-merge	'refs/pull/461/merge' of https://github.com/danielgindi/Charts
044f0f622e4ed5b8ff8d849c8721466998dd45da	not-for-merge	'refs/pull/4610/head' of https://github.com/danielgindi/Charts
9ea6c62b9642682917c7fe1abbd3b56da7e498b0	not-for-merge	'refs/pull/4613/head' of https://github.com/danielgindi/Charts
ca8c6b9e092dc2f182be3c1273ed90b7b23cb8f4	not-for-merge	'refs/pull/4615/head' of https://github.com/danielgindi/Charts
e4f83c06a2ca6233f34d5e67b6358ea9a38be3bd	not-for-merge	'refs/pull/4615/merge' of https://github.com/danielgindi/Charts
48b9dc76ed7d91061e7d1b879b7eef5b39779b41	not-for-merge	'refs/pull/462/head' of https://github.com/danielgindi/Charts
c1e78e28228e228c50f0a99f77bc8b2a3af43bea	not-for-merge	'refs/pull/4625/head' of https://github.com/danielgindi/Charts
af8e971585e8a14a2f408a2ad44bf6613a5121ac	not-for-merge	'refs/pull/4625/merge' of https://github.com/danielgindi/Charts
31e7bb6652ec02d07faa6334964fb14ce6656fe6	not-for-merge	'refs/pull/4634/head' of https://github.com/danielgindi/Charts
3ccc6e77f4a3c4432da02dd29653d474c1f471ca	not-for-merge	'refs/pull/4637/head' of https://github.com/danielgindi/Charts
93ddd0f5cf6a06a5f8b64365bd501b3ecf7cdcb4	not-for-merge	'refs/pull/4637/merge' of https://github.com/danielgindi/Charts
f026302a072bc8db36af0f18760516ab63f0c933	not-for-merge	'refs/pull/464/head' of https://github.com/danielgindi/Charts
8aaf4588879234ac4abd51eae9ef7c7d1c11669c	not-for-merge	'refs/pull/4647/head' of https://github.com/danielgindi/Charts
fa2920ed76332e9feb902c8bc03fa43633b6473d	not-for-merge	'refs/pull/4647/merge' of https://github.com/danielgindi/Charts
d3c7f295af588c4ccbfb738153d8212a6addba03	not-for-merge	'refs/pull/466/head' of https://github.com/danielgindi/Charts
25ca8b7ab53f0e00e786d9c19c760a494401d7b7	not-for-merge	'refs/pull/466/merge' of https://github.com/danielgindi/Charts
40a588f16204ac3da0bb72a89c6e567cedba1075	not-for-merge	'refs/pull/4661/head' of https://github.com/danielgindi/Charts
9cab909e4e410c16564f8fe08527183077408c47	not-for-merge	'refs/pull/4666/head' of https://github.com/danielgindi/Charts
c729d8c3bda474a23f81a8fb8c02bea62f06916b	not-for-merge	'refs/pull/4669/head' of https://github.com/danielgindi/Charts
5520917dc122ecb4389ef27dae725cf55ee9c763	not-for-merge	'refs/pull/4670/head' of https://github.com/danielgindi/Charts
8aa21ec6a04eec7d519b11aad9a25ea520966bbc	not-for-merge	'refs/pull/4670/merge' of https://github.com/danielgindi/Charts
f01bda30639bbad02d6534a87e45addffd8b96e2	not-for-merge	'refs/pull/4675/head' of https://github.com/danielgindi/Charts
dcca000f635155f755003c415e12d492a6b3ac06	not-for-merge	'refs/pull/4675/merge' of https://github.com/danielgindi/Charts
c5b01e2e0388e6f39f758dcc30cb1b07b71bb18c	not-for-merge	'refs/pull/4676/head' of https://github.com/danielgindi/Charts
7923eef4cd90d217b5d7b4be69370338ed691718	not-for-merge	'refs/pull/4676/merge' of https://github.com/danielgindi/Charts
525151855dcc46fe02f100e5b16a1ac411c48b20	not-for-merge	'refs/pull/4682/head' of https://github.com/danielgindi/Charts
7e7799fb8553eeba88eb74465643f2c2223c19c5	not-for-merge	'refs/pull/4682/merge' of https://github.com/danielgindi/Charts
b86075e31aea12b735801f6410a1326dc4e674ee	not-for-merge	'refs/pull/4684/head' of https://github.com/danielgindi/Charts
7b3526eb66d7403ae6d5a316639681d608a50e24	not-for-merge	'refs/pull/4684/merge' of https://github.com/danielgindi/Charts
eefd6053284a1c5b9209fc14c6330e460b78bf56	not-for-merge	'refs/pull/4687/head' of https://github.com/danielgindi/Charts
82b596ccd780b08ed5300198770f5e35b844e7cb	not-for-merge	'refs/pull/4687/merge' of https://github.com/danielgindi/Charts
22e8eca5af55e11c41644a498bc941dd859c2680	not-for-merge	'refs/pull/4700/head' of https://github.com/danielgindi/Charts
7d8e125c8252c319c715c8aa0f880ce07b4def67	not-for-merge	'refs/pull/4704/head' of https://github.com/danielgindi/Charts
c754577ae1339300648c99fd7174199019ee0b71	not-for-merge	'refs/pull/4708/head' of https://github.com/danielgindi/Charts
c754577ae1339300648c99fd7174199019ee0b71	not-for-merge	'refs/pull/4709/head' of https://github.com/danielgindi/Charts
b54f0f168ccef9f9d4a2ee6add1db8ab2c0f300c	not-for-merge	'refs/pull/4712/head' of https://github.com/danielgindi/Charts
6ab282655f75ecf015b5c3ca295b1a357b9df1c4	not-for-merge	'refs/pull/4712/merge' of https://github.com/danielgindi/Charts
30538b364f48d62ea525efda7b5ed14bc08570fc	not-for-merge	'refs/pull/4721/head' of https://github.com/danielgindi/Charts
8434082e4674a79604358619f8ad1b35756cd5fb	not-for-merge	'refs/pull/4728/head' of https://github.com/danielgindi/Charts
30d513bc6e8d4694b875b0a80cbb71b3e4ab46ef	not-for-merge	'refs/pull/4728/merge' of https://github.com/danielgindi/Charts
10f4b5b8ead16bb30e2235eaa4725c069c836e0b	not-for-merge	'refs/pull/4729/head' of https://github.com/danielgindi/Charts
e6cc017baeb494723c84e821cff18e89f33caa58	not-for-merge	'refs/pull/4729/merge' of https://github.com/danielgindi/Charts
359159acc79593d3c0c3451f5b432e1530cedae3	not-for-merge	'refs/pull/4730/head' of https://github.com/danielgindi/Charts
6ab64f19339ef9876906245756286b27a0af6a15	not-for-merge	'refs/pull/4730/merge' of https://github.com/danielgindi/Charts
b22e54feea6e0661c1eb7fdbf6750d3ac1b435ed	not-for-merge	'refs/pull/4731/head' of https://github.com/danielgindi/Charts
ecc61fc628a98009f5cd336c4020150883ab583f	not-for-merge	'refs/pull/4738/head' of https://github.com/danielgindi/Charts
2d6a5a0b38a9835a824ab5460c8b8090b2258a26	not-for-merge	'refs/pull/4738/merge' of https://github.com/danielgindi/Charts
07922212e18cbb4c3f12ffee76b24533e46710ba	not-for-merge	'refs/pull/474/head' of https://github.com/danielgindi/Charts
8d601ab52547c97b108f4037aea61ace1d937d29	not-for-merge	'refs/pull/474/merge' of https://github.com/danielgindi/Charts
fc68b127dd84a491d1b180b25207e7b512ac69f4	not-for-merge	'refs/pull/4743/head' of https://github.com/danielgindi/Charts
a5c11fb2295ed8ca7b1ba0fe4007cf30d2aa63af	not-for-merge	'refs/pull/4747/head' of https://github.com/danielgindi/Charts
203700608a6e956e591e7387d77423fa3fea6947	not-for-merge	'refs/pull/4751/head' of https://github.com/danielgindi/Charts
24a47d549500b6971659a6884f45e0ef792c0fc6	not-for-merge	'refs/pull/4755/head' of https://github.com/danielgindi/Charts
e22f0cbbe2097f2a22afd5694ce0ddeb9b5492e5	not-for-merge	'refs/pull/4755/merge' of https://github.com/danielgindi/Charts
a78a7118357bac12c571ee0164ad9b8ada1f3785	not-for-merge	'refs/pull/4763/head' of https://github.com/danielgindi/Charts
8de17731bfcc460df325247030c8122d62c2d81b	not-for-merge	'refs/pull/4763/merge' of https://github.com/danielgindi/Charts
409642f06351e2fc4edcb2423c522430ff15d3dc	not-for-merge	'refs/pull/4770/head' of https://github.com/danielgindi/Charts
c088043bbbefd1a94eb59d92823ad8ab75632f48	not-for-merge	'refs/pull/4770/merge' of https://github.com/danielgindi/Charts
89778948bf863fd87c9aa6af03b23546432d59f2	not-for-merge	'refs/pull/4773/head' of https://github.com/danielgindi/Charts
94d7ffc28b474c62887bfc54df29c2c2aa8513cf	not-for-merge	'refs/pull/4778/head' of https://github.com/danielgindi/Charts
92829742e1d5fc8cc6f4a31d77171d41296b5060	not-for-merge	'refs/pull/4778/merge' of https://github.com/danielgindi/Charts
1f21df6bbd448889cfd32979ca407f9ff30ce0f1	not-for-merge	'refs/pull/478/head' of https://github.com/danielgindi/Charts
a19483edc5a7fc71c543d1020669e28f45d57478	not-for-merge	'refs/pull/4781/head' of https://github.com/danielgindi/Charts
cc8e4b4c8d7b63a24f73df53dc90e4f1aca06880	not-for-merge	'refs/pull/4781/merge' of https://github.com/danielgindi/Charts
084a441e7e99536335a0197c62fbc1f062671567	not-for-merge	'refs/pull/4784/head' of https://github.com/danielgindi/Charts
80a4816e309a6d774b7a2d9ee6caba758af854e9	not-for-merge	'refs/pull/4784/merge' of https://github.com/danielgindi/Charts
f9df20d4a62cd6fe516d64d31fb801f79ce87cef	not-for-merge	'refs/pull/4785/head' of https://github.com/danielgindi/Charts
f4c7b3c6225a84b87ac9d9359e460bd321c3fba6	not-for-merge	'refs/pull/4789/head' of https://github.com/danielgindi/Charts
4180877c32f6f387352134b6197c90d6576e8ae3	not-for-merge	'refs/pull/4791/head' of https://github.com/danielgindi/Charts
6b841cecdcf5b19d7cedf392daf8f9a944066d6d	not-for-merge	'refs/pull/4797/head' of https://github.com/danielgindi/Charts
9b8433f7f1dee3c1b54be74aa21db152789e9a2b	not-for-merge	'refs/pull/4801/head' of https://github.com/danielgindi/Charts
a8e0c25be85ebba8a9b543251af5cb6b10f52746	not-for-merge	'refs/pull/4808/head' of https://github.com/danielgindi/Charts
ee95d6ab37f8aca20606c01e81f65aa3ff14c392	not-for-merge	'refs/pull/4808/merge' of https://github.com/danielgindi/Charts
829b8219a85e3f387072f984e929b92f5433bd27	not-for-merge	'refs/pull/4810/head' of https://github.com/danielgindi/Charts
08aff41c188cf0f7a6747e07e991d6154965a164	not-for-merge	'refs/pull/4812/head' of https://github.com/danielgindi/Charts
0420642103f600d466e3c713e6806c54726574f5	not-for-merge	'refs/pull/4817/head' of https://github.com/danielgindi/Charts
b44cc744ad2b49478f1bd6d8a6876a9c0d9f852e	not-for-merge	'refs/pull/4822/head' of https://github.com/danielgindi/Charts
4720a296ecce96aa5e0f95fa38762250d132c86f	not-for-merge	'refs/pull/4823/head' of https://github.com/danielgindi/Charts
20080f8e3138e8af38d3ecb615f5c3f3473e1faf	not-for-merge	'refs/pull/4827/head' of https://github.com/danielgindi/Charts
91e39f2164096f8abba47fca03bdb74265f5285d	not-for-merge	'refs/pull/4827/merge' of https://github.com/danielgindi/Charts
209511536bcc308d4d33ebbe71c275e4dae85a5f	not-for-merge	'refs/pull/4829/head' of https://github.com/danielgindi/Charts
a9193a791601c07d8b4f34bb43b351e97d1b20a7	not-for-merge	'refs/pull/4836/head' of https://github.com/danielgindi/Charts
cfd7e8e208457ee4cbcb84be0b4873fd91152674	not-for-merge	'refs/pull/4839/head' of https://github.com/danielgindi/Charts
ec6321cd5571eb1db6b4f6011060384f564bc162	not-for-merge	'refs/pull/4839/merge' of https://github.com/danielgindi/Charts
f48597f27d61f74e34132d9373a34ae66543b9ba	not-for-merge	'refs/pull/4841/head' of https://github.com/danielgindi/Charts
0a4bcfc0c2a788284b33c13c2142ff0d8613f2cd	not-for-merge	'refs/pull/4844/head' of https://github.com/danielgindi/Charts
5fc5e81862556bb6d849a49bec008f6d878c934b	not-for-merge	'refs/pull/4844/merge' of https://github.com/danielgindi/Charts
27c6fc80da11f6789d0b2d016fbace6729dd145a	not-for-merge	'refs/pull/4848/head' of https://github.com/danielgindi/Charts
9e4c77cafc69a478d92d60309e21318ab355e7b3	not-for-merge	'refs/pull/4861/head' of https://github.com/danielgindi/Charts
715d2103fa6f7fbddccbb62f067d3bace8f68ec7	not-for-merge	'refs/pull/4869/head' of https://github.com/danielgindi/Charts
696c6e5e893cd550dbbaeb4b4466d8793dcc0cae	not-for-merge	'refs/pull/4874/head' of https://github.com/danielgindi/Charts
65c93d4c91e709459e30ae52dc65efdf68058762	not-for-merge	'refs/pull/4875/head' of https://github.com/danielgindi/Charts
46fd83942da8f572989dbee9d88eb8152baeb1d0	not-for-merge	'refs/pull/488/head' of https://github.com/danielgindi/Charts
aeee443f5a5cc24b4670ab5a5e3aa8724a0f4e07	not-for-merge	'refs/pull/4881/head' of https://github.com/danielgindi/Charts
959f5acc2918f8bbf13f1dffc4c99b65923aad53	not-for-merge	'refs/pull/4882/head' of https://github.com/danielgindi/Charts
fdfe160f165b5cb9463308c74def9931a9b06c26	not-for-merge	'refs/pull/4885/head' of https://github.com/danielgindi/Charts
e338920f2aa986a1d6d1af33866a0b33eeae0dbc	not-for-merge	'refs/pull/4885/merge' of https://github.com/danielgindi/Charts
d92559322956541cbe6302e725a8068e8dfa399b	not-for-merge	'refs/pull/4886/head' of https://github.com/danielgindi/Charts
0cc20181d2e709032495091d8879ca4a95587e3d	not-for-merge	'refs/pull/4889/head' of https://github.com/danielgindi/Charts
e52f92c5565f9e41c6ead425f9ef068089ebe216	not-for-merge	'refs/pull/489/head' of https://github.com/danielgindi/Charts
9e40f17e717f3e180cb025d465a6b5ee2a4b3572	not-for-merge	'refs/pull/4898/head' of https://github.com/danielgindi/Charts
158e8b87671fbb0e29c486063c957831d602edb4	not-for-merge	'refs/pull/4899/head' of https://github.com/danielgindi/Charts
b1e184d606ed0b9281abf28f1cac1a969d31bfd2	not-for-merge	'refs/pull/4900/head' of https://github.com/danielgindi/Charts
9ba904061134fd7a967e9bdeb7b6a32226c02678	not-for-merge	'refs/pull/4901/head' of https://github.com/danielgindi/Charts
5d283078cf8ac4d94a63d784d42b70331d4afb5d	not-for-merge	'refs/pull/4902/head' of https://github.com/danielgindi/Charts
93604646a4f9a22baa83b9f945599b732a67911c	not-for-merge	'refs/pull/4904/head' of https://github.com/danielgindi/Charts
135febe37fcae83090ce80ebf2173000bff74c04	not-for-merge	'refs/pull/4909/head' of https://github.com/danielgindi/Charts
b60603689b332dae8d2aef2856b1870910ef555c	not-for-merge	'refs/pull/4909/merge' of https://github.com/danielgindi/Charts
5be8cdea42161934f943c6fc7d704d279b7473b0	not-for-merge	'refs/pull/4912/head' of https://github.com/danielgindi/Charts
7bbbc36b733463f155ed1bbd927bd5bcefadbb6a	not-for-merge	'refs/pull/4914/head' of https://github.com/danielgindi/Charts
0600d0a68c46aa5409b6358524637bbdc8add0be	not-for-merge	'refs/pull/4915/head' of https://github.com/danielgindi/Charts
a0ad6169533d8bec4ded61b7f48d65cdd2215b2a	not-for-merge	'refs/pull/4915/merge' of https://github.com/danielgindi/Charts
8e1bcbd8ed33e153b90b30dc20446b5965fc6727	not-for-merge	'refs/pull/493/head' of https://github.com/danielgindi/Charts
a4aec43eb5680db526733c06ac4ad0ca43377e07	not-for-merge	'refs/pull/4931/head' of https://github.com/danielgindi/Charts
5329451c398582f9ebce8fab44ff501a7d898d3d	not-for-merge	'refs/pull/4931/merge' of https://github.com/danielgindi/Charts
a2ff58d886885b189c8097253e28a9d6eb7a7706	not-for-merge	'refs/pull/4936/head' of https://github.com/danielgindi/Charts
fe7eb445fd2ebf78bb7f8e1ec26d0cbdf3cac3eb	not-for-merge	'refs/pull/494/head' of https://github.com/danielgindi/Charts
86c1027cb65b295327f07b675bf2e490c26492af	not-for-merge	'refs/pull/4942/head' of https://github.com/danielgindi/Charts
98c4bbffe198422c86263629f4bee07d6cb9f09e	not-for-merge	'refs/pull/4944/head' of https://github.com/danielgindi/Charts
39e0ebc9469f8d3bd30f81901d651a3b0015d6fb	not-for-merge	'refs/pull/4948/head' of https://github.com/danielgindi/Charts
229c478aeef780b7d254da71834f0082e43acabd	not-for-merge	'refs/pull/4948/merge' of https://github.com/danielgindi/Charts
671122a7c092fce9a9c55ef16d6f853a79feed8b	not-for-merge	'refs/pull/4956/head' of https://github.com/danielgindi/Charts
01305ed6f0595351e055585969104a0a8560dda3	not-for-merge	'refs/pull/4969/head' of https://github.com/danielgindi/Charts
efb2cb718a1424e04cdf3dce419f08a98977a30b	not-for-merge	'refs/pull/4969/merge' of https://github.com/danielgindi/Charts
bd49f9be483f4a99e9936318761a9bfe4219f818	not-for-merge	'refs/pull/497/head' of https://github.com/danielgindi/Charts
6dd7687b98771e0b063dba457ec51ee2ee6cb149	not-for-merge	'refs/pull/4971/head' of https://github.com/danielgindi/Charts
af24585da6db77de3855cf4050dc6e4bdb876e47	not-for-merge	'refs/pull/4974/head' of https://github.com/danielgindi/Charts
e19370c27e9d683510f582962d13e0e926d35288	not-for-merge	'refs/pull/4975/head' of https://github.com/danielgindi/Charts
06b1fc3b88a505f1fa5ad8d7d0a4a5686ee95e7f	not-for-merge	'refs/pull/4979/head' of https://github.com/danielgindi/Charts
0de62e811f1f6e82e03508696b69768a229c524f	not-for-merge	'refs/pull/498/head' of https://github.com/danielgindi/Charts
1bc32ba3892fbb49fa07540474fce084dabc32f0	not-for-merge	'refs/pull/4981/head' of https://github.com/danielgindi/Charts
626784d04027296bbd8097dcb5bd809f5e4da674	not-for-merge	'refs/pull/4981/merge' of https://github.com/danielgindi/Charts
1f63f1cfdc8d56a087404e305300302a45ded093	not-for-merge	'refs/pull/4988/head' of https://github.com/danielgindi/Charts
d5fb4673bd68fe33d2d04710d7634cd48480b3cb	not-for-merge	'refs/pull/4988/merge' of https://github.com/danielgindi/Charts
1b4fcd80ae76eb6411258dfa5a82ada971e12fdf	not-for-merge	'refs/pull/499/head' of https://github.com/danielgindi/Charts
027e79902096af983447c9431c91c46852923c28	not-for-merge	'refs/pull/499/merge' of https://github.com/danielgindi/Charts
747a49f87d3b4800458b86ad893986d1b1a8fb8a	not-for-merge	'refs/pull/4997/head' of https://github.com/danielgindi/Charts
0b1c3c5b9ad987e6e82c7dc4fb0ff98341317f98	not-for-merge	'refs/pull/4997/merge' of https://github.com/danielgindi/Charts
6ebcd6c175eb0ad797a10927e51829926c3374d8	not-for-merge	'refs/pull/4999/head' of https://github.com/danielgindi/Charts
fada3ae9327ac393ad410391f3dfed978f3bc9c9	not-for-merge	'refs/pull/5/head' of https://github.com/danielgindi/Charts
d6455f16be47c7860850ce2d0ff103f2947dc674	not-for-merge	'refs/pull/500/head' of https://github.com/danielgindi/Charts
58530f029442eb0a5f294d633e346b248928fe2b	not-for-merge	'refs/pull/5000/head' of https://github.com/danielgindi/Charts
d29f12ec5d30182cd1fdc86cbd5c16e376bea484	not-for-merge	'refs/pull/5000/merge' of https://github.com/danielgindi/Charts
342bbe178fd26e5f7811f3c2425b40877c82d6a1	not-for-merge	'refs/pull/5005/head' of https://github.com/danielgindi/Charts
6ef5ab7119c20c6f1c353564eca707918f2750c7	not-for-merge	'refs/pull/5007/head' of https://github.com/danielgindi/Charts
8279a99b230d6c7ce32fc16bd699343ab33b4b7e	not-for-merge	'refs/pull/5009/head' of https://github.com/danielgindi/Charts
91bb0cb1ffce07b66723d460f3a8c73f42e75ef6	not-for-merge	'refs/pull/501/head' of https://github.com/danielgindi/Charts
7bf42ca910e7b26e0a7616ae6a8fb96cbdd432bc	not-for-merge	'refs/pull/5012/head' of https://github.com/danielgindi/Charts
52ef5b9d23ea8763a96081fe3867af7d37a3541a	not-for-merge	'refs/pull/5012/merge' of https://github.com/danielgindi/Charts
7a22c755e8d5b90c67711eb48af3843088209d6b	not-for-merge	'refs/pull/5016/head' of https://github.com/danielgindi/Charts
488c3271a8f979f21e750f7f2a00b066fb09a86e	not-for-merge	'refs/pull/502/head' of https://github.com/danielgindi/Charts
41771f96a2077394d18299a56aa549d22d6eb99e	not-for-merge	'refs/pull/502/merge' of https://github.com/danielgindi/Charts
eb236ab8bd716676bce818ea9865f7aec2a63848	not-for-merge	'refs/pull/5020/head' of https://github.com/danielgindi/Charts
079f1484ae2d830c236b247e569aa6d7576f4e1d	not-for-merge	'refs/pull/5021/head' of https://github.com/danielgindi/Charts
500b395ee68eb3589afaf84f0cf40eeadd440488	not-for-merge	'refs/pull/5021/merge' of https://github.com/danielgindi/Charts
df3ad87cc33545fd35ff72b1e5c869913c553922	not-for-merge	'refs/pull/5025/head' of https://github.com/danielgindi/Charts
d665306d937e1f291fad077cfb3ef4d351a3db6f	not-for-merge	'refs/pull/5025/merge' of https://github.com/danielgindi/Charts
8e3a0b3bdecf81ec90e9309e2e13cf47f7a5d223	not-for-merge	'refs/pull/5026/head' of https://github.com/danielgindi/Charts
0c194fa6cb5d65ba42c40a3bc984eef10d9126f6	not-for-merge	'refs/pull/5027/head' of https://github.com/danielgindi/Charts
284a7788b741e4408d86690f605a87a047bc0e50	not-for-merge	'refs/pull/5028/head' of https://github.com/danielgindi/Charts
286319250cd7841d86caa58bd6de1ad75aae5646	not-for-merge	'refs/pull/503/head' of https://github.com/danielgindi/Charts
2b361dffe7e62e04b5804b03cd3894c9468e8b30	not-for-merge	'refs/pull/503/merge' of https://github.com/danielgindi/Charts
b42e0c1cf04da30051a1875a697ebe41d85df1fe	not-for-merge	'refs/pull/5030/head' of https://github.com/danielgindi/Charts
6343810f1b06a3270bc0dc0b1a8e5432fa0ec49f	not-for-merge	'refs/pull/5030/merge' of https://github.com/danielgindi/Charts
14868095e84dfa77ce41f41d2357eb98f2683540	not-for-merge	'refs/pull/5031/head' of https://github.com/danielgindi/Charts
35dbb04bce1af63dcd66abae0db917f3ca8fb27f	not-for-merge	'refs/pull/5031/merge' of https://github.com/danielgindi/Charts
48bf7b03b7b1592cb348174c8b2f529299dc2785	not-for-merge	'refs/pull/5032/head' of https://github.com/danielgindi/Charts
4e7434dffc5278837abcae9db31bbf2b679c2350	not-for-merge	'refs/pull/5035/head' of https://github.com/danielgindi/Charts
1cda0a022178d29963c965d6dfad529adb4af23e	not-for-merge	'refs/pull/5035/merge' of https://github.com/danielgindi/Charts
1bc32ba3892fbb49fa07540474fce084dabc32f0	not-for-merge	'refs/pull/5038/head' of https://github.com/danielgindi/Charts
71ea447394246a59ea521a65daebd41f16d9f6ec	not-for-merge	'refs/pull/5041/head' of https://github.com/danielgindi/Charts
466fdb8d1cbf134eccaaaa468f0cbc668638b294	not-for-merge	'refs/pull/5043/head' of https://github.com/danielgindi/Charts
1bb968345725cb248b6455045eda2320fabac3f0	not-for-merge	'refs/pull/5044/head' of https://github.com/danielgindi/Charts
5743d0b5c31710763392f42f5e593dcc39bfbc43	not-for-merge	'refs/pull/5044/merge' of https://github.com/danielgindi/Charts
67736cef7135aeca74397df6ebe65b6c740c3540	not-for-merge	'refs/pull/5049/head' of https://github.com/danielgindi/Charts
726ca17a1fcf10145bc599ca0eb33a28de8a310a	not-for-merge	'refs/pull/5049/merge' of https://github.com/danielgindi/Charts
bd92d508ac5a3dc762be14af946bdbe0d901dc8e	not-for-merge	'refs/pull/5059/head' of https://github.com/danielgindi/Charts
b93e268623a15bedf6802397ce0c7832e7b4af42	not-for-merge	'refs/pull/5060/head' of https://github.com/danielgindi/Charts
158cdb68dbfef7d4f45de9bb6406e77e2bd30e23	not-for-merge	'refs/pull/5062/head' of https://github.com/danielgindi/Charts
10ab125c13fae172d52920a393423c87d43cec73	not-for-merge	'refs/pull/5067/head' of https://github.com/danielgindi/Charts
7c17075f5220bbf8bf13f485e5db14014dedd163	not-for-merge	'refs/pull/5068/head' of https://github.com/danielgindi/Charts
796f785ad0b5d20ca0b8436a691358c5ecb5ff35	not-for-merge	'refs/pull/5069/head' of https://github.com/danielgindi/Charts
3d5b031cf9bec76f835bfd4365a5ed53ef3bec79	not-for-merge	'refs/pull/5070/head' of https://github.com/danielgindi/Charts
ba0e62e3f1192d3857a80d3b97d3dbe98c44b1e4	not-for-merge	'refs/pull/5077/head' of https://github.com/danielgindi/Charts
5e878efbbe90b24fa8cd7796ba948f9ee88ab02b	not-for-merge	'refs/pull/5077/merge' of https://github.com/danielgindi/Charts
6a706eee770686101524c8660a6f825946d8cefa	not-for-merge	'refs/pull/5078/head' of https://github.com/danielgindi/Charts
04f7774c8466d3d068775084ce41ed9a8f999ea8	not-for-merge	'refs/pull/5079/head' of https://github.com/danielgindi/Charts
5d6d2ac3d2b84440a7112f39c23f3b52fd3e7b5b	not-for-merge	'refs/pull/5085/head' of https://github.com/danielgindi/Charts
c9ae47da1bfa6fad726843f018598799d279b7cf	not-for-merge	'refs/pull/5087/head' of https://github.com/danielgindi/Charts
768715a36191a7bb49ba23724cf176f98e56e1d7	not-for-merge	'refs/pull/5089/head' of https://github.com/danielgindi/Charts
d2ff638fd0458d156b3ed2c0f3b1274cc27a7e20	not-for-merge	'refs/pull/5089/merge' of https://github.com/danielgindi/Charts
b6bfb48ac88a4b69009b00fed236110c26a62811	not-for-merge	'refs/pull/5092/head' of https://github.com/danielgindi/Charts
8688db39e17524e31d302d442cd69cea5e91a8fe	not-for-merge	'refs/pull/5093/head' of https://github.com/danielgindi/Charts
af830f293a9de5b09a8bff21dec13a301cd75197	not-for-merge	'refs/pull/5097/head' of https://github.com/danielgindi/Charts
3a909d028c3ec45f924739d13beaa6ea56ec1576	not-for-merge	'refs/pull/5099/head' of https://github.com/danielgindi/Charts
87bbe9d5bdf4d3ba2898893da98461ba40425f54	not-for-merge	'refs/pull/5101/head' of https://github.com/danielgindi/Charts
11e984db54ac9ef4bc6b04ea2f9117930dfd35f9	not-for-merge	'refs/pull/5101/merge' of https://github.com/danielgindi/Charts
52914548d8023c059da93cf54df2f6bddbbe9ba6	not-for-merge	'refs/pull/5105/head' of https://github.com/danielgindi/Charts
8405c40e27f3bb8cf0d099fbf38f2a7cd852a6f7	not-for-merge	'refs/pull/5105/merge' of https://github.com/danielgindi/Charts
9fee72f089607160bda872931a4880fc9134584d	not-for-merge	'refs/pull/5109/head' of https://github.com/danielgindi/Charts
88f9f1a631beae79cb2ec92d19e3fbb04d7951c3	not-for-merge	'refs/pull/5109/merge' of https://github.com/danielgindi/Charts
39378c5cfedc091842e50e6718f52ce8e114f1f6	not-for-merge	'refs/pull/5110/head' of https://github.com/danielgindi/Charts
08c76980abeb73138cc6b333699be467167f5bd1	not-for-merge	'refs/pull/5114/head' of https://github.com/danielgindi/Charts
09fc8c1ac0566d981ddddbff18accee7bfbf13e3	not-for-merge	'refs/pull/5114/merge' of https://github.com/danielgindi/Charts
8a9cb343a55fe3a26fe126d5b9dc6e4abc1edc3d	not-for-merge	'refs/pull/5115/head' of https://github.com/danielgindi/Charts
4d4955ef251a8b309b04b712ccf2792ce4cbd379	not-for-merge	'refs/pull/5117/head' of https://github.com/danielgindi/Charts
0e49a6ed63b4f6643727a91f637c3f43852a9e50	not-for-merge	'refs/pull/5117/merge' of https://github.com/danielgindi/Charts
720582ed70a302b41fb9a78401509cbf18b9d13f	not-for-merge	'refs/pull/5119/head' of https://github.com/danielgindi/Charts
0cc524f070f5fdf44be551120138fe2249c33615	not-for-merge	'refs/pull/5119/merge' of https://github.com/danielgindi/Charts
c7f6ad9d4a4c37418bede71b97e71aed974be888	not-for-merge	'refs/pull/5124/head' of https://github.com/danielgindi/Charts
8367365bfd60cf10247aa440ff0b27c27829f1eb	not-for-merge	'refs/pull/513/head' of https://github.com/danielgindi/Charts
89abab2fc1b6fb69913370c0395800a7a13f3d0c	not-for-merge	'refs/pull/5134/head' of https://github.com/danielgindi/Charts
c300992086a6f64651f1b85fba19646e4e1b700e	not-for-merge	'refs/pull/5136/head' of https://github.com/danielgindi/Charts
afcb24a955143996a3aaac5392029cbd14f9a472	not-for-merge	'refs/pull/5136/merge' of https://github.com/danielgindi/Charts
ae094e92dd5e9aeba0bb16bf766c064fe7fbe87b	not-for-merge	'refs/pull/5137/head' of https://github.com/danielgindi/Charts
bfb96408c08a059588e2bbed7291a7eb08cd8564	not-for-merge	'refs/pull/5139/head' of https://github.com/danielgindi/Charts
d9d5590e06af8aba2a7193af2eb69ed4635a72ed	not-for-merge	'refs/pull/5141/head' of https://github.com/danielgindi/Charts
eb48c218cd574a101e3ed7bbc14cd954847e7b3b	not-for-merge	'refs/pull/5142/head' of https://github.com/danielgindi/Charts
96da36a719e2424d9dae2220d1d3029820c4390d	not-for-merge	'refs/pull/5142/merge' of https://github.com/danielgindi/Charts
219d88dc61242f88b4cfb6ae965b74c72185a37b	not-for-merge	'refs/pull/5143/head' of https://github.com/danielgindi/Charts
84f14c13b227004bc917cd14f3a065f9f6423ab2	not-for-merge	'refs/pull/5143/merge' of https://github.com/danielgindi/Charts
15daa9c926bf9c53af7da2e0e8d3a923c6da2596	not-for-merge	'refs/pull/5149/head' of https://github.com/danielgindi/Charts
a264503511f42df761945d4a188ff6e0e493ec2b	not-for-merge	'refs/pull/5150/head' of https://github.com/danielgindi/Charts
7352d1c10c16bb3b12f5d5ad68cb64eb2485c59d	not-for-merge	'refs/pull/5156/head' of https://github.com/danielgindi/Charts
b620fa7f7b1607706f2708260089ab5f91c95647	not-for-merge	'refs/pull/5159/head' of https://github.com/danielgindi/Charts
1c925209baebb7b834223f9873e474a059bdb51b	not-for-merge	'refs/pull/5163/head' of https://github.com/danielgindi/Charts
56b1e285cef25dfa23dc4397effcdd79cd8984af	not-for-merge	'refs/pull/5167/head' of https://github.com/danielgindi/Charts
b29cb80344c40cd1ca7cbbaf08fbd9808ba49630	not-for-merge	'refs/pull/5168/head' of https://github.com/danielgindi/Charts
bf43db811afdfc3c4f4137aa87a72e309b2c2eba	not-for-merge	'refs/pull/5173/head' of https://github.com/danielgindi/Charts
c0188748f4b2706937042828d172bcbf7ce38076	not-for-merge	'refs/pull/5173/merge' of https://github.com/danielgindi/Charts
3121e9fbaaba747f81e933915ced18f2b95c69b9	not-for-merge	'refs/pull/5177/head' of https://github.com/danielgindi/Charts
098b2f65bd3d8b5ce472e151bd501c1f32de223c	not-for-merge	'refs/pull/5177/merge' of https://github.com/danielgindi/Charts
d46c322292c76f2f132b438558ad8b07d5f4c176	not-for-merge	'refs/pull/5179/head' of https://github.com/danielgindi/Charts
1bd03b60f1c8c90f3ff17ea75936209ec3976cd6	not-for-merge	'refs/pull/5180/head' of https://github.com/danielgindi/Charts
b7243ba0381f867f08deba77f3167d69353af5d9	not-for-merge	'refs/pull/5180/merge' of https://github.com/danielgindi/Charts
a558f17c63baf8c5cc7dad15deeee24b4215d8ad	not-for-merge	'refs/pull/5185/head' of https://github.com/danielgindi/Charts
eedf4fd1d144f33735ee3c53a2c92f21ebda8aa5	not-for-merge	'refs/pull/5185/merge' of https://github.com/danielgindi/Charts
196202d6fea5bf5642dc928a58cf1ae52df4c11e	not-for-merge	'refs/pull/5192/head' of https://github.com/danielgindi/Charts
ecf51f60269a65a2b9e337ed413c5a2fe1688744	not-for-merge	'refs/pull/5194/head' of https://github.com/danielgindi/Charts
04dc03b59544b70dd3833432fd0a5328fbb86f9d	not-for-merge	'refs/pull/5196/head' of https://github.com/danielgindi/Charts
301a2bd06bfaa1155ea16e9ae400e31d89181f18	not-for-merge	'refs/pull/5198/head' of https://github.com/danielgindi/Charts
a930d59132a0f9e4f695873f2cf32c76507fb14e	not-for-merge	'refs/pull/5200/head' of https://github.com/danielgindi/Charts
d0d26e5a92b1d52be62c8258d9a7112d70c35968	not-for-merge	'refs/pull/5202/head' of https://github.com/danielgindi/Charts
caf6ef1266172d5efee6ba8803112070567b872c	not-for-merge	'refs/pull/521/head' of https://github.com/danielgindi/Charts
f893e48754ff923f69b2628dbde291661cf3ec05	not-for-merge	'refs/pull/521/merge' of https://github.com/danielgindi/Charts
e1e9796426506edb62ebfa841d188e75a2b0e958	not-for-merge	'refs/pull/5211/head' of https://github.com/danielgindi/Charts
afc7c983d2f98c4d81cac28b1169951cc7323bb5	not-for-merge	'refs/pull/5226/head' of https://github.com/danielgindi/Charts
04aeec53d9c1d1df5563973488f47aaaf499443d	not-for-merge	'refs/pull/5231/head' of https://github.com/danielgindi/Charts
1935638107ef37d4048323b9488d07f6fa62307c	not-for-merge	'refs/pull/5235/head' of https://github.com/danielgindi/Charts
621ebe051c6a0feb5da6279a13680582807c5872	not-for-merge	'refs/pull/5238/head' of https://github.com/danielgindi/Charts
862de8907b9397a6732512e001d3e914e81043c1	not-for-merge	'refs/pull/558/head' of https://github.com/danielgindi/Charts
7068ba0ba2af229826193beabd87e0d859433163	not-for-merge	'refs/pull/56/head' of https://github.com/danielgindi/Charts
dcbc9552f8e191f17b9235dfe510f1822eaebfed	not-for-merge	'refs/pull/567/head' of https://github.com/danielgindi/Charts
f068abd9d195872ac46949ac13e42934f9bf836f	not-for-merge	'refs/pull/567/merge' of https://github.com/danielgindi/Charts
8cf8d1aad782f7766be3fd41b0341c6c7de20905	not-for-merge	'refs/pull/571/head' of https://github.com/danielgindi/Charts
0b85afeb14142a5b4f8ad29714f7fc53a0dc805a	not-for-merge	'refs/pull/575/head' of https://github.com/danielgindi/Charts
56b4ed576392463ec3e3473fdeef22eb91fb7935	not-for-merge	'refs/pull/575/merge' of https://github.com/danielgindi/Charts
0bcc2158baeffd83e58d695fcd33fe3105fef13c	not-for-merge	'refs/pull/577/head' of https://github.com/danielgindi/Charts
2e88eaaf201d94a405c71909002ccf7353e004c9	not-for-merge	'refs/pull/577/merge' of https://github.com/danielgindi/Charts
e108da52fbe9ee946cbad04fb0f6fd30bd6da73f	not-for-merge	'refs/pull/580/head' of https://github.com/danielgindi/Charts
c8a547ce679885cc11839e10786f3eb39db6c30e	not-for-merge	'refs/pull/582/head' of https://github.com/danielgindi/Charts
274ef6ad314892409d90e36b296f3bf2798e4df8	not-for-merge	'refs/pull/582/merge' of https://github.com/danielgindi/Charts
10b71bc230f0df9d4d637925c8f24fc3b9f79bfe	not-for-merge	'refs/pull/586/head' of https://github.com/danielgindi/Charts
1d38ab8de324789042958e232e40a47442bd9bfb	not-for-merge	'refs/pull/586/merge' of https://github.com/danielgindi/Charts
1cfe6e866bb287a6f2efd7f32110f46bb25acfb6	not-for-merge	'refs/pull/600/head' of https://github.com/danielgindi/Charts
d02783a9667b1e0852f88b58e123f4dcb111c093	not-for-merge	'refs/pull/605/head' of https://github.com/danielgindi/Charts
2496c8bd34b0b93768d6089c3586187b0600f6ca	not-for-merge	'refs/pull/614/head' of https://github.com/danielgindi/Charts
de167866f88ba831815fa64c3e5929a2f3ca33e5	not-for-merge	'refs/pull/624/head' of https://github.com/danielgindi/Charts
ca0a3fc7fd81902b0aef82685a476355eb304d42	not-for-merge	'refs/pull/624/merge' of https://github.com/danielgindi/Charts
33de7d7b75897f65f9bf45f143b3bf22c09b7ec1	not-for-merge	'refs/pull/629/head' of https://github.com/danielgindi/Charts
6f178c8e00062b1db9dd30c550434877f16b949d	not-for-merge	'refs/pull/629/merge' of https://github.com/danielgindi/Charts
2d08c1f75a2b412be4ac885768e877be9d7f4f89	not-for-merge	'refs/pull/630/head' of https://github.com/danielgindi/Charts
2e166b1bd8be4f18a98e87dc4387264bbcf7fd3b	not-for-merge	'refs/pull/632/head' of https://github.com/danielgindi/Charts
cc7c36ee5f38b7d85831c0a1da14d48d548092a5	not-for-merge	'refs/pull/635/head' of https://github.com/danielgindi/Charts
86bf36af0b3a1e57a1d978c066d4009f499903d8	not-for-merge	'refs/pull/636/head' of https://github.com/danielgindi/Charts
fb0672ac89ee79b3cc8da31d2b09a374c65e0b3d	not-for-merge	'refs/pull/638/head' of https://github.com/danielgindi/Charts
2fc9980227915abeb07cec2a46014b6cb98bb918	not-for-merge	'refs/pull/638/merge' of https://github.com/danielgindi/Charts
de117c1ff6dccf12cc5b402b7e918fb226ef8d62	not-for-merge	'refs/pull/639/head' of https://github.com/danielgindi/Charts
8885bfa98936a6c1f0c166c2923cada05dc435e0	not-for-merge	'refs/pull/640/head' of https://github.com/danielgindi/Charts
daaf9ac8a32825ff5156e123183013e3a46a7b55	not-for-merge	'refs/pull/640/merge' of https://github.com/danielgindi/Charts
48c28c88a67f969cb9be6021c8aaaed2031fae4a	not-for-merge	'refs/pull/643/head' of https://github.com/danielgindi/Charts
bb0527fbd292204bfe3b674ca1dea47625fad4fd	not-for-merge	'refs/pull/648/head' of https://github.com/danielgindi/Charts
25bd262fbb1dfb72e288438a3d6e9c0794e32acb	not-for-merge	'refs/pull/649/head' of https://github.com/danielgindi/Charts
1cb49be03523f1808842ca6e97f031675bd0ef37	not-for-merge	'refs/pull/649/merge' of https://github.com/danielgindi/Charts
4b55450d3de6434e829d94818513ab6e41c490f5	not-for-merge	'refs/pull/651/head' of https://github.com/danielgindi/Charts
6e9f64b8852a72c98aa94e0abed48c3666f0359d	not-for-merge	'refs/pull/658/head' of https://github.com/danielgindi/Charts
a6baa456165c1f9797109d4ac81d922f48ee09a5	not-for-merge	'refs/pull/66/head' of https://github.com/danielgindi/Charts
4f21232f18f9c08fe0431750ef1b9ea7f7605dd7	not-for-merge	'refs/pull/660/head' of https://github.com/danielgindi/Charts
efc483510fa5f45ac03bc93e3bd33f2bfaf8f385	not-for-merge	'refs/pull/670/head' of https://github.com/danielgindi/Charts
ee3aa8567c588b754806b44cfce568c56d148499	not-for-merge	'refs/pull/670/merge' of https://github.com/danielgindi/Charts
29b25a23273764dd0d4fb97c03fee2c7ad42e5cc	not-for-merge	'refs/pull/673/head' of https://github.com/danielgindi/Charts
2aa8f3b343d8161f6e06432e7aef74e3f0b27e19	not-for-merge	'refs/pull/673/merge' of https://github.com/danielgindi/Charts
645f12448e3ac2dbba836f5f5030abf6b68be78d	not-for-merge	'refs/pull/675/head' of https://github.com/danielgindi/Charts
dd0a3d12713d912dada19f32abae8c7b3850fd8c	not-for-merge	'refs/pull/679/head' of https://github.com/danielgindi/Charts
5abae597f044fba8da7a37e965fcd8d5672002d5	not-for-merge	'refs/pull/679/merge' of https://github.com/danielgindi/Charts
0ec73e64686a24cb66d5e63336ee10b848bbee62	not-for-merge	'refs/pull/686/head' of https://github.com/danielgindi/Charts
3e2603edde90014ff9f5c5cec4ea94eedbfd346a	not-for-merge	'refs/pull/686/merge' of https://github.com/danielgindi/Charts
1b0e75d75da2fcf6a255027828c129e2475ad070	not-for-merge	'refs/pull/690/head' of https://github.com/danielgindi/Charts
75c2583128fb3eb15b9d896d90b2949c42cfcc44	not-for-merge	'refs/pull/690/merge' of https://github.com/danielgindi/Charts
10b95359c78cde9aac6842ce99b4253d62014b5c	not-for-merge	'refs/pull/694/head' of https://github.com/danielgindi/Charts
57df7fa4ec20f3956c001ef3697e4afbc969fa2c	not-for-merge	'refs/pull/694/merge' of https://github.com/danielgindi/Charts
72e95a1612b9f4dd1ede6f2cda5291f8c85ff439	not-for-merge	'refs/pull/698/head' of https://github.com/danielgindi/Charts
88be2b5a50cdb2b492c7f9dc76b837bc38effca4	not-for-merge	'refs/pull/702/head' of https://github.com/danielgindi/Charts
1b2123009369ebb4d4d977a714101714bbbb457a	not-for-merge	'refs/pull/702/merge' of https://github.com/danielgindi/Charts
5d8336799ba026c2ce080c85306fc866b599dab0	not-for-merge	'refs/pull/703/head' of https://github.com/danielgindi/Charts
73ce11885655c93defe29eb3f0dd1dea39215173	not-for-merge	'refs/pull/704/head' of https://github.com/danielgindi/Charts
32356de498693a7e0f1d1d42fa7be0db7a326b99	not-for-merge	'refs/pull/704/merge' of https://github.com/danielgindi/Charts
00ccb387b035497afde084c60c729f79e322e616	not-for-merge	'refs/pull/708/head' of https://github.com/danielgindi/Charts
2c0c9cc386d26f779de841c7ccfe818f9f87acaa	not-for-merge	'refs/pull/712/head' of https://github.com/danielgindi/Charts
d6c22ebc6734161f037119e284945db161891c2c	not-for-merge	'refs/pull/713/head' of https://github.com/danielgindi/Charts
b06d383c000fbde96505527cb3440f42686e2b45	not-for-merge	'refs/pull/715/head' of https://github.com/danielgindi/Charts
4f9fbbf78f58b0f2b0b9174ac5c9d5d073987376	not-for-merge	'refs/pull/715/merge' of https://github.com/danielgindi/Charts
eb76c4c847676c76fb8c806a23c2156a0df67a36	not-for-merge	'refs/pull/718/head' of https://github.com/danielgindi/Charts
9d546b4b2f11761eb2cc5e28f8b896f95e8f3b8c	not-for-merge	'refs/pull/719/head' of https://github.com/danielgindi/Charts
1d9d5336b82755450f4a05cf98d5fb9729a5eb47	not-for-merge	'refs/pull/719/merge' of https://github.com/danielgindi/Charts
771c23632e15db7ed3c2a0ae28735e004ac8b5b1	not-for-merge	'refs/pull/725/head' of https://github.com/danielgindi/Charts
4629ebca8b26613a4436857248292f10c6d3247a	not-for-merge	'refs/pull/74/head' of https://github.com/danielgindi/Charts
08a88e7a99ef18ea1ce0ea5c18b272c7355c15dc	not-for-merge	'refs/pull/740/head' of https://github.com/danielgindi/Charts
4a0f1a458f7106777141fa6e137c7ed9f148cf6a	not-for-merge	'refs/pull/740/merge' of https://github.com/danielgindi/Charts
5f23bfaba5f6b4305dd4a4c9ae21f9906f1ad9bf	not-for-merge	'refs/pull/742/head' of https://github.com/danielgindi/Charts
d554d6a204221e9d5bc41e75b9ce4ecb517b3a62	not-for-merge	'refs/pull/742/merge' of https://github.com/danielgindi/Charts
c6f5954cc48974d12c7756d18a128016b517c932	not-for-merge	'refs/pull/746/head' of https://github.com/danielgindi/Charts
8a2636ebb7f93d80edba536e54bc87308b40a1b1	not-for-merge	'refs/pull/751/head' of https://github.com/danielgindi/Charts
9a025276d18d5c7fa9c2c2a6d6cb1f0158d7f644	not-for-merge	'refs/pull/752/head' of https://github.com/danielgindi/Charts
7a52baeea5cfd00384ea477497192cfa0f98ec76	not-for-merge	'refs/pull/755/head' of https://github.com/danielgindi/Charts
c8f6f9fdfceceba9e3f9764c67d5deb8b0febc5d	not-for-merge	'refs/pull/759/head' of https://github.com/danielgindi/Charts
0a2e1c4a5a7fa033f5e99e8b1c80ea4844d2bb58	not-for-merge	'refs/pull/759/merge' of https://github.com/danielgindi/Charts
f883d393d46cb26b2ea19aea5b35f8c80f91a023	not-for-merge	'refs/pull/76/head' of https://github.com/danielgindi/Charts
1b6c880c1cb855d1a2675fccb2a18826f53755ce	not-for-merge	'refs/pull/76/merge' of https://github.com/danielgindi/Charts
767d1e8dcc32d828879edd8f3f2d66de37402f2c	not-for-merge	'refs/pull/764/head' of https://github.com/danielgindi/Charts
145366b35c50ca0bc8c46408f4d2fadd447d8000	not-for-merge	'refs/pull/764/merge' of https://github.com/danielgindi/Charts
b370e1047a172a577a7a4b1a899eb8d2f708742b	not-for-merge	'refs/pull/765/head' of https://github.com/danielgindi/Charts
51fb28277c92280c600bce1bf6110dfbf71360d8	not-for-merge	'refs/pull/765/merge' of https://github.com/danielgindi/Charts
ed81d4a54a8f225e75b146f5f4c3353f42b9b644	not-for-merge	'refs/pull/769/head' of https://github.com/danielgindi/Charts
bcc69ef8b205925d632d67c9456fa833db9ab9b8	not-for-merge	'refs/pull/772/head' of https://github.com/danielgindi/Charts
70cd5bde276ddf6cb32e057e6755cabfcc411944	not-for-merge	'refs/pull/778/head' of https://github.com/danielgindi/Charts
63bb919ee919314829a4890d2d1b254e88f96446	not-for-merge	'refs/pull/780/head' of https://github.com/danielgindi/Charts
ed1ef117617b810094beba6bda66eca87d5312b2	not-for-merge	'refs/pull/781/head' of https://github.com/danielgindi/Charts
08dc38144bc04506d2703adc20754d3ca995baff	not-for-merge	'refs/pull/789/head' of https://github.com/danielgindi/Charts
06b08ffda4ea9fb238d23026ed9e0a341305215c	not-for-merge	'refs/pull/789/merge' of https://github.com/danielgindi/Charts
79a6a0bd7ca1c6793a2f104a201f734eebfac610	not-for-merge	'refs/pull/794/head' of https://github.com/danielgindi/Charts
002c2e86a4199c4ee289469d1d840b45aac9d390	not-for-merge	'refs/pull/794/merge' of https://github.com/danielgindi/Charts
8b5e806f8725f9a04981671554c19cef77480b75	not-for-merge	'refs/pull/795/head' of https://github.com/danielgindi/Charts
8eaba3482879751d8a6ca9a7f9a6f9a0a384731c	not-for-merge	'refs/pull/811/head' of https://github.com/danielgindi/Charts
f0acad10538d6fc951c0d201da8493f4216d59a2	not-for-merge	'refs/pull/814/head' of https://github.com/danielgindi/Charts
052fff2f078e4c90fce532bde78f95fc2671be35	not-for-merge	'refs/pull/814/merge' of https://github.com/danielgindi/Charts
d5aac13cbffa7dadc6ece06863d6524ef191fbd4	not-for-merge	'refs/pull/815/head' of https://github.com/danielgindi/Charts
afc1be65f88ca787d2b80add1711d8f275b123d4	not-for-merge	'refs/pull/824/head' of https://github.com/danielgindi/Charts
84842b50b6b289f5ceac2ab42ee4a2e93265a674	not-for-merge	'refs/pull/828/head' of https://github.com/danielgindi/Charts
bcf09b96786ee8dae9f8fe57909b10dec10e3a5d	not-for-merge	'refs/pull/836/head' of https://github.com/danielgindi/Charts
462b677f553df4af240aff6afd75d52a79120fd6	not-for-merge	'refs/pull/836/merge' of https://github.com/danielgindi/Charts
221dc668d4b25ce999c8996cc51cb9c556297265	not-for-merge	'refs/pull/843/head' of https://github.com/danielgindi/Charts
db4bdd1cd5a515ea77a3fcd32a9abed84f5d9406	not-for-merge	'refs/pull/843/merge' of https://github.com/danielgindi/Charts
3a05e830593edbe378d1710cce880be0750222d9	not-for-merge	'refs/pull/844/head' of https://github.com/danielgindi/Charts
5a831163e95145561d6d40733dc4ae5cefecd032	not-for-merge	'refs/pull/847/head' of https://github.com/danielgindi/Charts
eff73c66a5d8b231320269786f5daec999f1fa0f	not-for-merge	'refs/pull/856/head' of https://github.com/danielgindi/Charts
aceab80d5ef0eebfe4a5772b094bc999a41b434c	not-for-merge	'refs/pull/856/merge' of https://github.com/danielgindi/Charts
fc4e935109dff2212f139294fc030940e58ecdde	not-for-merge	'refs/pull/861/head' of https://github.com/danielgindi/Charts
0cdfab08c0944397f0c9f2b7de5337713b6e5584	not-for-merge	'refs/pull/868/head' of https://github.com/danielgindi/Charts
e44033b0fcf32464e83e027e65d0f1ce963a1787	not-for-merge	'refs/pull/868/merge' of https://github.com/danielgindi/Charts
d078986784e5d1dcc4c988019b995f5b57383d38	not-for-merge	'refs/pull/869/head' of https://github.com/danielgindi/Charts
28393ed7ea67790cb0214f7b8e86400a6f48779a	not-for-merge	'refs/pull/872/head' of https://github.com/danielgindi/Charts
82e493a552083832be804dbd26ca5dd0c7883c60	not-for-merge	'refs/pull/872/merge' of https://github.com/danielgindi/Charts
c721aa07477b5bfe475b603f164fb9557bea9e82	not-for-merge	'refs/pull/880/head' of https://github.com/danielgindi/Charts
be49e3f6427c4f359f07f1238025ee7acca99f1c	not-for-merge	'refs/pull/880/merge' of https://github.com/danielgindi/Charts
4c9d3c8d35dd11f6ad25039591dd56fc9a33d405	not-for-merge	'refs/pull/887/head' of https://github.com/danielgindi/Charts
8a4b58df2ed4825fcca42f7dbd22725f9a6b005a	not-for-merge	'refs/pull/894/head' of https://github.com/danielgindi/Charts
8485dfa293e9b7701abab9ff20c1686514048bbb	not-for-merge	'refs/pull/9/head' of https://github.com/danielgindi/Charts
c422e1ac8adb4c19078d639b193e0c167606eff6	not-for-merge	'refs/pull/9/merge' of https://github.com/danielgindi/Charts
463f92fe96798f168c492d4f827c53c722532875	not-for-merge	'refs/pull/913/head' of https://github.com/danielgindi/Charts
2e284aa7a87f972687a89dfc7a92c59bc81e88d0	not-for-merge	'refs/pull/922/head' of https://github.com/danielgindi/Charts
8d02895d0b1c6de79c8b6b33cb455684c62ab929	not-for-merge	'refs/pull/93/head' of https://github.com/danielgindi/Charts
b1dcdbd25f4b3bff3fba9c9593fcede1bb148617	not-for-merge	'refs/pull/932/head' of https://github.com/danielgindi/Charts
d42c59b3238993f7f5bf356a7ef348ec1d7edb56	not-for-merge	'refs/pull/934/head' of https://github.com/danielgindi/Charts
7b5462ee6b1401234f446831d966be9fd4842a14	not-for-merge	'refs/pull/935/head' of https://github.com/danielgindi/Charts
e5992387c1adddc4922627db3cb63b677048c418	not-for-merge	'refs/pull/937/head' of https://github.com/danielgindi/Charts
2ab382efa0c2885a1a9588786f56ae9c95ed6afb	not-for-merge	'refs/pull/941/head' of https://github.com/danielgindi/Charts
4dbc05b23b6f740d3b669f3d44d412bc8b26b55f	not-for-merge	'refs/pull/944/head' of https://github.com/danielgindi/Charts
e6e1db353df9e1d1e2b9aecbad41419506461b87	not-for-merge	'refs/pull/944/merge' of https://github.com/danielgindi/Charts
f790343000ced62f708e008af32b2c8009a1e63a	not-for-merge	'refs/pull/948/head' of https://github.com/danielgindi/Charts
6ad3218a912a2410855dae3d45b3583877de9a71	not-for-merge	'refs/pull/948/merge' of https://github.com/danielgindi/Charts
8ff8202a37836783b9c8872c812b5b26c7f8feac	not-for-merge	'refs/pull/949/head' of https://github.com/danielgindi/Charts
89026314807d968f71745529c81fff5fe623ea4b	not-for-merge	'refs/pull/95/head' of https://github.com/danielgindi/Charts
d0cd6c2bb66d860de6745bdae35c6765113c89b3	not-for-merge	'refs/pull/969/head' of https://github.com/danielgindi/Charts
5bdaba6097832a1dcbbb80f1b3484194fabcc10e	not-for-merge	'refs/pull/969/merge' of https://github.com/danielgindi/Charts
9bf511e5f1be31bfbaeac5d742caec7da286a25c	not-for-merge	'refs/pull/978/head' of https://github.com/danielgindi/Charts
47cf77e464399e3a16aa71cab49db6c5b91e7673	not-for-merge	'refs/pull/978/merge' of https://github.com/danielgindi/Charts
7765c04838dea87ba88cf323907f201347714407	not-for-merge	'refs/pull/982/head' of https://github.com/danielgindi/Charts
ee00036e23d0992df428f6c3331f7123e9d20f10	not-for-merge	'refs/pull/982/merge' of https://github.com/danielgindi/Charts
1d425d567d4759d8022f9c649c38007d79f64fe0	not-for-merge	'refs/pull/995/head' of https://github.com/danielgindi/Charts
319ea24994fbdd0735828aa319410114e3162696	not-for-merge	'refs/pull/995/merge' of https://github.com/danielgindi/Charts
56edf30627ce3e09bcf05b5cb1c6c89785ce0643	not-for-merge	tag '0.0.1' of https://github.com/danielgindi/Charts
3a486aef94632166ccabc863bd4142a65fdedc80	not-for-merge	tag '2.2.1' of https://github.com/danielgindi/Charts
fcb6b2fb69d629a59893467043a3e9142cfa9414	not-for-merge	tag '2.3.0' of https://github.com/danielgindi/Charts
5b22fa99ef80f2004a7439ee6d3c1cfc9c4be010	not-for-merge	tag '2.3.1' of https://github.com/danielgindi/Charts
a9f80b766ec36e01676326b25a14919246066fb5	not-for-merge	tag '3.0.0' of https://github.com/danielgindi/Charts
5d9c81ab7f440e231c6d283c517eaabc2ba4bb4e	not-for-merge	tag '3.0.1' of https://github.com/danielgindi/Charts
f23e872e7573f8c79862ff4822cfc5683703d424	not-for-merge	tag '3.0.2' of https://github.com/danielgindi/Charts
983d556bbc278fe773951f3f715bcccfbfef8785	not-for-merge	tag '3.0.3' of https://github.com/danielgindi/Charts
d953129eb6dbd50e85059e784dab9570dd654276	not-for-merge	tag '3.0.4' of https://github.com/danielgindi/Charts
5d8b81bdb4baf59f2a637f1ebe4151a30ba69f0e	not-for-merge	tag '3.0.5' of https://github.com/danielgindi/Charts
0aeb63c1ac707dd06d674b4946150ec4b61b7b55	not-for-merge	tag '3.1.0' of https://github.com/danielgindi/Charts
6b730a035c6daf19f695aedbb0d7e5e441fdede2	not-for-merge	tag '3.1.1' of https://github.com/danielgindi/Charts
0a229f8c914b0ec93798cee058cf75b339297513	not-for-merge	tag '5.0.0' of https://github.com/danielgindi/Charts
dd9c72e3d7e751e769971092a6bd72d39198ae63	not-for-merge	tag '5.1.0' of https://github.com/danielgindi/Charts
7df9bf4e12b77cae9f7fba12725b910a1dba52f2	not-for-merge	tag 'v2.0.9' of https://github.com/danielgindi/Charts
3c0775caac28b33531b4441643e060184b803c78	not-for-merge	tag 'v2.1.0' of https://github.com/danielgindi/Charts
8353646077ab97283847c63716ee83b5fc6c3b64	not-for-merge	tag 'v2.1.1' of https://github.com/danielgindi/Charts
36641ece4ca178e210b4b948cd964cc38923774f	not-for-merge	tag 'v2.1.2' of https://github.com/danielgindi/Charts
4d5c4f6a003f1d39db539ffb761df66ea57848fd	not-for-merge	tag 'v2.1.3' of https://github.com/danielgindi/Charts
7b8507750c032b17dce46826911c425387f3f0fb	not-for-merge	tag 'v2.1.4' of https://github.com/danielgindi/Charts
b55038ff79abf04ff45eee529425dd35f961b203	not-for-merge	tag 'v2.1.4a' of https://github.com/danielgindi/Charts
3da826022dce15ac33a5e54f716107ca1cf3bf67	not-for-merge	tag 'v2.1.5' of https://github.com/danielgindi/Charts
1364bb2b9651b728407bcf242a3911f929df182a	not-for-merge	tag 'v2.1.6' of https://github.com/danielgindi/Charts
8b91b30dd86269d8f23b9ca7eea57cf08cebbc80	not-for-merge	tag 'v2.2.0' of https://github.com/danielgindi/Charts
3a486aef94632166ccabc863bd4142a65fdedc80	not-for-merge	tag 'v2.2.1' of https://github.com/danielgindi/Charts
89aae3d7e1416cb414a21028479e6feb932cdbf3	not-for-merge	tag 'v2.2.2' of https://github.com/danielgindi/Charts
1788e53f22eb3de79eb4f08574d8ea4b54b5e417	not-for-merge	tag 'v2.2.3' of https://github.com/danielgindi/Charts
f38ea99dbf7567387e729e6c088110e83faf6241	not-for-merge	tag 'v2.2.4' of https://github.com/danielgindi/Charts
276076f1180a0676e4766fe27bb3fa48864df8dc	not-for-merge	tag 'v2.2.5' of https://github.com/danielgindi/Charts
fcb6b2fb69d629a59893467043a3e9142cfa9414	not-for-merge	tag 'v2.3.0' of https://github.com/danielgindi/Charts
5b22fa99ef80f2004a7439ee6d3c1cfc9c4be010	not-for-merge	tag 'v2.3.1' of https://github.com/danielgindi/Charts
a9f80b766ec36e01676326b25a14919246066fb5	not-for-merge	tag 'v3.0.0' of https://github.com/danielgindi/Charts
5d9c81ab7f440e231c6d283c517eaabc2ba4bb4e	not-for-merge	tag 'v3.0.1' of https://github.com/danielgindi/Charts
f23e872e7573f8c79862ff4822cfc5683703d424	not-for-merge	tag 'v3.0.2' of https://github.com/danielgindi/Charts
983d556bbc278fe773951f3f715bcccfbfef8785	not-for-merge	tag 'v3.0.3' of https://github.com/danielgindi/Charts
d953129eb6dbd50e85059e784dab9570dd654276	not-for-merge	tag 'v3.0.4' of https://github.com/danielgindi/Charts
5d8b81bdb4baf59f2a637f1ebe4151a30ba69f0e	not-for-merge	tag 'v3.0.5' of https://github.com/danielgindi/Charts
0aeb63c1ac707dd06d674b4946150ec4b61b7b55	not-for-merge	tag 'v3.1.0' of https://github.com/danielgindi/Charts
6b730a035c6daf19f695aedbb0d7e5e441fdede2	not-for-merge	tag 'v3.1.1' of https://github.com/danielgindi/Charts
856ef4ccedf8142032c0f9998f8f38d0f3ffe136	not-for-merge	tag 'v3.2.0' of https://github.com/danielgindi/Charts
fbec767b7dafacddc05eba1dd2563aa6dfe97e55	not-for-merge	tag 'v3.2.1' of https://github.com/danielgindi/Charts
deae18583b7e82e44c9a0e37514d07d81de20fb9	not-for-merge	tag 'v3.2.2' of https://github.com/danielgindi/Charts
cd1120b004cc6aac40d889fa665c0e495e463b13	not-for-merge	tag 'v3.3.0' of https://github.com/danielgindi/Charts
dbf7fa2891270f88aeafc872ea0beddb344af94b	not-for-merge	tag 'v3.4.0' of https://github.com/danielgindi/Charts
7ac4a05e83d279bac305a472af9128c2a0eca3d7	not-for-merge	tag 'v3.5.0' of https://github.com/danielgindi/Charts
66546404a6739173b8e436ab6bc1f2897cd08594	not-for-merge	tag 'v3.6.0' of https://github.com/danielgindi/Charts
9b24868b61a75666355d824eeb97290e7f0dca42	not-for-merge	tag 'v4.0.0' of https://github.com/danielgindi/Charts
8d134a78bb2cb9e7a0f05ecbd5880bb05cd02863	not-for-merge	tag 'v4.0.1' of https://github.com/danielgindi/Charts
b61fc9b97829ab9b954d01f96e601d52045ef5dc	not-for-merge	tag 'v4.0.2' of https://github.com/danielgindi/Charts
b38b8d45a8cbda9f0f2a3566778ed114f06056b7	not-for-merge	tag 'v4.0.3' of https://github.com/danielgindi/Charts
07b23476ad52b926be772f317d8f1d4511ee8d02	not-for-merge	tag 'v4.1.0' of https://github.com/danielgindi/Charts
