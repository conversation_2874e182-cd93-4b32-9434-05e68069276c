{"object": {"artifacts": [], "dependencies": [{"basedOn": null, "packageRef": {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "name": "Alamofire"}, "state": {"checkoutState": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}, "name": "sourceControlCheckout"}, "subpath": "Alamofire"}, {"basedOn": null, "packageRef": {"identity": "charts", "kind": "remoteSourceControl", "location": "https://github.com/danielgindi/Charts.git", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "state": {"checkoutState": {"revision": "dd9c72e3d7e751e769971092a6bd72d39198ae63", "version": "5.1.0"}, "name": "sourceControlCheckout"}, "subpath": "Charts"}, {"basedOn": null, "packageRef": {"identity": "hotkey", "kind": "remoteSourceControl", "location": "https://github.com/soffes/HotKey.git", "name": "Hot<PERSON>ey"}, "state": {"checkoutState": {"revision": "a3cf605d7a96f6ff50e04fcb6dea6e2613cfcbe4", "version": "0.2.1"}, "name": "sourceControlCheckout"}, "subpath": "Hot<PERSON>ey"}, {"basedOn": null, "packageRef": {"identity": "keychainaccess", "kind": "remoteSourceControl", "location": "https://github.com/kishikawakatsumi/KeychainAccess.git", "name": "KeychainAccess"}, "state": {"checkoutState": {"revision": "84e546727d66f1adc5439debad16270d0fdd04e7", "version": "4.2.2"}, "name": "sourceControlCheckout"}, "subpath": "KeychainAccess"}, {"basedOn": null, "packageRef": {"identity": "socket.io-client-swift", "kind": "remoteSourceControl", "location": "https://github.com/socketio/socket.io-client-swift", "name": "SocketIO"}, "state": {"checkoutState": {"revision": "42da871d9369f290d6ec4930636c40672143905b", "version": "16.1.1"}, "name": "sourceControlCheckout"}, "subpath": "socket.io-client-swift"}, {"basedOn": null, "packageRef": {"identity": "starscream", "kind": "remoteSourceControl", "location": "https://github.com/daltoniam/Starscream.git", "name": "Starscream"}, "state": {"checkoutState": {"revision": "c6bfd1af48efcc9a9ad203665db12375ba6b145a", "version": "4.0.8"}, "name": "sourceControlCheckout"}, "subpath": "Starscream"}, {"basedOn": null, "packageRef": {"identity": "swift-asn1", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-asn1.git", "name": "swift-asn1"}, "state": {"checkoutState": {"revision": "a54383ada6cecde007d374f58f864e29370ba5c3", "version": "1.3.2"}, "name": "sourceControlCheckout"}, "subpath": "swift-asn1"}, {"basedOn": null, "packageRef": {"identity": "swift-async-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-async-algorithms.git", "name": "swift-async-algorithms"}, "state": {"checkoutState": {"revision": "042e1c4d9d19748c9c228f8d4ebc97bb1e339b0b", "version": "1.0.4"}, "name": "sourceControlCheckout"}, "subpath": "swift-async-algorithms"}, {"basedOn": null, "packageRef": {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "name": "swift-collections"}, "state": {"checkoutState": {"revision": "c1805596154bb3a265fd91b8ac0c4433b4348fb0", "version": "1.2.0"}, "name": "sourceControlCheckout"}, "subpath": "swift-collections"}, {"basedOn": null, "packageRef": {"identity": "swift-crypto", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-crypto.git", "name": "swift-crypto"}, "state": {"checkoutState": {"revision": "e8d6eba1fef23ae5b359c46b03f7d94be2f41fed", "version": "3.12.3"}, "name": "sourceControlCheckout"}, "subpath": "swift-crypto"}, {"basedOn": null, "packageRef": {"identity": "swift-log", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-log.git", "name": "swift-log"}, "state": {"checkoutState": {"revision": "3d8596ed08bd13520157f0355e35caed215ffbfa", "version": "1.6.3"}, "name": "sourceControlCheckout"}, "subpath": "swift-log"}, {"basedOn": null, "packageRef": {"identity": "<PERSON><PERSON><PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/SwiftyJSON/SwiftyJSON.git", "name": "SwiftyJSON"}, "state": {"checkoutState": {"revision": "af76cf3ef710b6ca5f8c05f3a31307d44a3c5828", "version": "5.0.2"}, "name": "sourceControlCheckout"}, "subpath": "SwiftyJSON"}], "prebuilts": []}, "version": 7}