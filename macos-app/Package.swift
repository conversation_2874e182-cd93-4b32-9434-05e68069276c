// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "WOWMonitor",
    platforms: [
        .macOS(.v13)
    ],
    products: [
        .executable(
            name: "WOWMonitor",
            targets: ["WOWMonitor"]
        )
    ],
    dependencies: [
        // WebSocket client for real-time communication
        .package(url: "https://github.com/daltoniam/Starscream.git", from: "4.0.0"),
        .package(url: "https://github.com/socketio/socket.io-client-swift", from: "16.0.0"),
        
        // JSON handling and networking
        .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
        .package(url: "https://github.com/SwiftyJSON/SwiftyJSON.git", from: "5.0.0"),
        
        // Keychain access for secure storage
        .package(url: "https://github.com/kishikawakatsumi/KeychainAccess.git", from: "4.2.2"),
        
        // Hot key registration
        .package(url: "https://github.com/soffes/HotKey.git", from: "0.2.0"),
        
        // Charts and data visualization
        .package(url: "https://github.com/danielgindi/Charts.git", from: "5.0.0"),
        
        // Logging framework
        .package(url: "https://github.com/apple/swift-log.git", from: "1.5.3"),
        
        // Async algorithms
        .package(url: "https://github.com/apple/swift-async-algorithms.git", from: "1.0.0"),
        
        // Collections utilities
        .package(url: "https://github.com/apple/swift-collections.git", from: "1.0.0"),
        
        // Crypto utilities for update verification
        .package(url: "https://github.com/apple/swift-crypto.git", from: "3.0.0")
    ],
    targets: [
        .executableTarget(
            name: "WOWMonitor",
            dependencies: [
                "Starscream",
                .product(name: "SocketIO", package: "socket.io-client-swift"),
                "Alamofire",
                "SwiftyJSON",
                "KeychainAccess",
                "HotKey",
                .product(name: "DGCharts", package: "Charts"),
                .product(name: "Logging", package: "swift-log"),
                .product(name: "AsyncAlgorithms", package: "swift-async-algorithms"),
                .product(name: "Collections", package: "swift-collections"),
                .product(name: "Crypto", package: "swift-crypto")
            ],
            path: "Sources",
            swiftSettings: [
                .enableUpcomingFeature("BareSlashRegexLiterals"),
                .enableUpcomingFeature("ConciseMagicFile"),
                .enableUpcomingFeature("ForwardTrailingClosures"),
                .enableUpcomingFeature("ImplicitOpenExistentials"),
                .enableUpcomingFeature("StrictConcurrency")
            ]
        )
    ]
)